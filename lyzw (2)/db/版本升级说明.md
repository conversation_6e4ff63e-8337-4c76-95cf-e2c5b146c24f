# 版本升级方法

> JeecgBoot属于平台级产品，每次升级改动较大，目前做不到平滑升级。

### 增量升级方案

#### 1.代码合并
 本地通过svn或git做好主干，在分支上做业务开发，jeecg每次版本发布，可以手工覆盖主干的代码，对比合并代码；

#### 2.数据库升级
- 从3.6.2+版本增加flyway自动升级数据库机制，支持 mysql5.7、mysql8; 
- 其他库请手工执行SQL, 目录： `jeecg-module-system\jeecg-system-start\src\main\resources\flyway\sql\mysql`
> 注意： 升级sql只提供mysql版本；如果有权限升级, 还需要手工角色授权，退出重新登录才好使。

#### 3.其他数据库脚本说明
  原先官方默认提供oracle和SqlServer的脚本，但是维护成本太高，未提供脚本的数据库，可以参考下面的文档自己转
   https://my.oschina.net/jeecg/blog/4905722
   （注意：定时任务的表qrtz_*，需要删掉用原始的脚本重新执行一下）
   quartz-2.2.3-distribution.tar.gz放到百度网盘中，大家自己下载，执行所需数据库脚本
   https://pan.baidu.com/s/1WrmZdUuAPg3iBwJ-LoHWyg?pwd=8mdz 

#### 4.兼容问题
 每次发版，会针对不兼容地方重点说明。