CREATE TABLE `inz_word_groups` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '分组名称',
  `type` varchar(10) NOT NULL COMMENT '分组类型',
  `is_custom` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否自定义分组',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` varchar(1) NOT NULL DEFAULT '1' COMMENT '状态（0禁用，1启用）',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`),
  KEY `idx_word_groups_type` (`type`),
  KEY `idx_word_groups_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='词汇分组配置'; 