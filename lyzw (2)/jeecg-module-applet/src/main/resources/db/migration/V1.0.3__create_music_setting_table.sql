-- 创建用户音乐设置表
CREATE TABLE IF NOT EXISTS `inz_music_setting` (
  `id` varchar(36) NOT NULL COMMENT '主键',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `mute_sound` tinyint(1) DEFAULT 0 COMMENT '是否关闭声音',
  `current_theme` varchar(50) DEFAULT NULL COMMENT '当前选择的音乐主题',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户音乐设置';

-- 添加索引
CREATE INDEX idx_music_setting_user_id ON inz_music_setting(user_id); 