-- 创建用户背诵设置表
CREATE TABLE IF NOT EXISTS `inz_user_recitation_setting` (
  `id` varchar(36) NOT NULL COMMENT 'ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `remind_chinese_when_memorizing` tinyint(1) DEFAULT 1 COMMENT '记忆时提醒中文 (1是 0否)',
  `remind_chinese_when_matching` tinyint(1) DEFAULT 1 COMMENT '配对时提醒中文 (1是 0否)',
  `pronunciation_type` tinyint(1) DEFAULT 1 COMMENT '发音选择 (1英式 2美式)',
  `timer_type` tinyint(1) DEFAULT 1 COMMENT '计时设置 (1正计时 2倒计时)',
  `speed_duration` int(11) DEFAULT 10 COMMENT '速度时长 (单位:秒)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户背诵设置';

-- 添加索引
CREATE INDEX idx_user_recitation_setting_user_id ON inz_user_recitation_setting(user_id); 