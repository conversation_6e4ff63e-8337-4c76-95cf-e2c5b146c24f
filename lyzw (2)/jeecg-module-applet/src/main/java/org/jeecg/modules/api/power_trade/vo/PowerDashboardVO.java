package org.jeecg.modules.api.power_trade.vo;

import lombok.Data;

import java.util.Date;

/**
 * 首页数据VO
 */
@Data
public class PowerDashboardVO {
    
    private Long id;
    
    private Integer provinceId;
    
    private String regionName;
    
    private Date statsDate;
    
    private Double tradingCapacity;  // 交易容量 MW/GW
    
    private Integer stationCount;  // 场站数量
    
    private Integer windStationCount;  // 风电站数量
    
    private Integer solarStationCount;  // 光伏站数量
    
    private Integer storageStationCount;  // 储能站数量
    
    private Double accumulatedPower;  // 累计发电量 GWh
    
    private Double plannedPower;  // 计划发电量 GWh
    
    private Double limitedPower;  // 限电量 GWh
    
    private Double settlementPower;  // 结算电量 GWh
    
    private Double settlementAvgPrice;  // 结算均价 元/MWh
    
    private Double coalBenchmarkPrice;  // 燃煤标杆价 元/MWh
} 