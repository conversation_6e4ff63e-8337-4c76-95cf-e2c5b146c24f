package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.GuoNengStrategy;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.*;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.util.NullValueHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.jeecg.modules.api.power_trade.constant.StationConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电力交易首页控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-首页接口")
@RequestMapping("/api/power_trade/dashboard")
public class PowerDashboardController {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementMapper screenTradeSettlementMapper;

    @Autowired
    private IElectricityDataService electricityDataService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    /**
     * 获取首页数据
     */
    @GetMapping("/summary")
    @ApiOperation(value = "获取首页概览数据", notes = "获取区域电力交易概况数据")
    public Result<DashboardSummaryDTO> getDashboard(
            @ApiParam(value = "省份Id") @RequestParam Integer provinceId) {
        
        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create().validateProvinceId(provinceId);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        // 检查是否为全国数据源汇总
        if (provinceId == 0) {
            // 全国数据源汇总模式
            Map<String, Object> aggregatedData = multiDataSourceAggregationService.aggregateAllProvincesDashboardSummary();
            // 这里需要将Map转换为DashboardSummaryDTO
            DashboardSummaryDTO result = convertToDashboardSummaryDTO(aggregatedData);
            // 确保返回值不为空
            if (result == null) {
                result = new DashboardSummaryDTO();
            }
            ensureDashboardSummaryNotNull(result);
            return Result.OK(result);
        }

        // 单省份模式
        DashboardSummaryDTO result = new DashboardSummaryDTO();
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID");
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 1. 获取指定省份且已参与交易的所有电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getTradeStatus, 1)
                    .eq(Station::getProvinceId, provinceId);

            List<Station> stationList = stationService.list(stationQuery);

            // 若无电站，直接返回空DTO
            if (stationList == null || stationList.isEmpty()) {
                return Result.OK(result);
            }

            // 2. 计算交易容量
            double tradingCapacity = stationList.stream()
                    .mapToDouble(Station::getCapacity)
                    .sum();

            // 储能站功率单独累加
            double storagePower = stationList.stream()
                    .filter(s -> s.getType() != null && s.getType() == 3)
                    .mapToDouble(station -> station.getPower() != null ? station.getPower() : 0.0)
                    .sum();
            tradingCapacity += storagePower;

            // 3. 统计各类型场站数量
            int windCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 1).count();
            int solarCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 2).count();
            int storageCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 3).count();

            // 4. 统计能源类型及其数量
            Map<Integer, Integer> energyTypeCountMap = stationList.stream()
                    .filter(s -> s.getType() != null)
                    .collect(Collectors.groupingBy(
                            Station::getType,
                            Collectors.summingInt(e -> 1)));

            // 5. 汇总结算数据
            double accumulatedPower = 0.0; // 累计发电量
            double plannedPower = 0.0; // 计划发电量
            double settlementAvgPrice = 0.0; // 结算均价
            double settlementPower = 0.0; // 结算电量
            double limitedPower = 0.0; // 限电量
            int settlementCount = 0; // 有效结算数据数量
            double benchmarkPrice = 0.0;
            double targetPowerPrice = 0.0;

            switch (provinceId) {
                case 1:
                    benchmarkPrice = 391;
                    targetPowerPrice = 391;
                    break;
                case 2:
                    benchmarkPrice = 384.4;
                    targetPowerPrice = 384.4;
                    break;
            }

            // 切换到省份数据源查询结算数据
            dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey != null) {
                log.info("切换到数据源: {} 查询结算数据", dsKey);
                DynamicDataSourceContextHolder.push(dsKey);

                try {
                    // 获取该省份所有电站的ID列表
                    List<Long> stationIds = stationList.stream()
                            .map(Station::getId)
                            .collect(Collectors.toList());

                    log.info("查询首页概览数据，省份ID: {}, 电站数量: {}, 电站IDs: {}",
                            provinceId, stationIds.size(), stationIds);

                    // 先调试检查表中的数据情况
                    Map<String, Object> debugInfo = screenTradeSettlementMapper.debugTableData(stationIds);
                    log.info("表数据调试信息: {}", debugInfo);

                    List<Map<String, Object>> debugDetails = screenTradeSettlementMapper.debugDetailData(stationIds);
                    log.info("表详细数据（前10条）: {}", debugDetails);

                    // 查询最新的结算数据（自动获取最新年月的数据）
                    Map<String, Object> summaryData = screenTradeSettlementMapper
                            .selectDashboardSummaryData(stationIds);

                    if (summaryData != null && !summaryData.isEmpty()) {
                        log.info("查询到概览数据: {}", summaryData);

                        // 设置查询到的真实数据
                        accumulatedPower = convertToDouble(summaryData.get("accumulatedPower"));
                        plannedPower = convertToDouble(summaryData.get("plannedPower"));
                        settlementAvgPrice = convertToDouble(summaryData.get("settlementAvgPrice"));
                        settlementPower = convertToDouble(summaryData.get("settlementPower"));
                        limitedPower = convertToDouble(summaryData.get("limitedPower"));
                        settlementCount = stationIds.size(); // 设置为电站数量
                    } else {
                        log.warn("未查询到概览数据，使用默认值");
                    }
                } catch (Exception e) {
                    log.error("查询首页概览数据失败，使用默认值: {}", e.getMessage(), e);
                } finally {
                    DynamicDataSourceContextHolder.clear();
                }
            } else {
                log.warn("不支持的省份ID: {}, 使用默认值", provinceId);
            }

            // 6. 构建并设置DTO值
            result.setStationTypeStatistics(new StationTypeStatisticsDTO(
                    tradingCapacity,
                    stationList.size(),
                    windCount,
                    solarCount,
                    storageCount,
                    accumulatedPower,
                    plannedPower,
                    settlementCount == 0 ? 0.0 : settlementAvgPrice / settlementCount,
                    settlementPower,
                    limitedPower,
                    targetPowerPrice,
                    benchmarkPrice));
            result.setEnergyTypeCount(energyTypeCountMap);

            // 确保返回值不为空
            ensureDashboardSummaryNotNull(result);
            return Result.OK(result);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @GetMapping("/getStation")
    @ApiOperation("获取对应省份的电站")
    public Result<List<Station>> getStationList(@ApiParam(value = "省份ID") @RequestParam Integer provinceId) {
        LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
        if (provinceId == 0) {
            return Result.OK(stationService.list());
        }
        stationQuery.eq(Station::getProvinceId, provinceId);
        return Result.OK(stationService.getStationsByProvinceId(provinceId));
    }

    @GetMapping("/settlement/summary")
    @ApiOperation(value = "获取电站交易概况", notes = "返回电站列表的交易概况，每个电站包含电站名称、累计结算电量、交易均价")
    public Result<List<SettlementSummaryDTO>> getSettlementSummary(
            @ApiParam(value = "电站ID（可选，为空时返回省份汇总数据）") @RequestParam(required = false) Long stationId,
            @ApiParam(value = "省份ID") @RequestParam Integer provinceId,
            @ApiParam(value = "查询维度: 1-月度 2-年度") @RequestParam(required = false, defaultValue = "1") Integer dimension,
            @ApiParam(value = "月份(格式: yyyy-MM)，仅在dimension=1时有效") @RequestParam(required = false) String month,
            @ApiParam(value = "年份(格式: yyyy)，仅在dimension=2时有效") @RequestParam(required = false) String year) {
        try {
            // 参数验证
            Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
                ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
                validator.validateProvinceId(provinceId);
                validator.validateQueryDimension(dimension);
                if (dimension == 1) {
                    validator.validateMonthQuery(month);
                } else {
                    validator.validateYearQuery(year);
                }
            });
            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }

            // 检查是否为全国数据源汇总
            if (provinceId == 0) {
                // 全国数据源汇总模式
                List<SettlementSummaryDTO> aggregatedData = multiDataSourceAggregationService
                        .aggregateAllProvincesSettlementSummary(stationId, dimension, month, year);
                return Result.OK(aggregatedData);
            }

            // 2. 先在主数据源中查询电站信息
            List<Station> targetStations = new ArrayList<>();
            if (stationId != null) {
                // 查询指定电站
                LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
                stationQuery.eq(Station::getId, stationId)
                        .eq(Station::getProvinceId, provinceId);
                Station station = stationService.getOne(stationQuery);
                if (station == null)
                    return Result.error("指定的电站不属于该省份");
                targetStations.add(station);
            } else {
                // 查询该省份的所有电站
                targetStations = stationService.getStationsByProvinceId(provinceId);
                if (targetStations.isEmpty()) {
                    return Result.error("该省份下没有找到电站数据");
                }
            }

            // 3. 计算时间范围
            String startDate, endDate, startYearMonth, endYearMonth;
            if (dimension == 1) {
                // 月度查询
                startDate = month + "-01";
                endDate = getMonthEndDate(month);
                startYearMonth = month;
                endYearMonth = month;
            } else {
                // 年度查询
                startDate = year + "-01-01";
                endDate = year + "-12-31";
                startYearMonth = year + "-01";
                endYearMonth = year + "-12";
            }

            // 4. 切换到省份数据源查询结算数据
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }

            log.info("切换到数据源: {} 查询结算数据", dsKey);
            DynamicDataSourceContextHolder.push(dsKey);
            try {

                // 5. 查询每个电站的结算数据并构造返回列表
                List<SettlementSummaryDTO> resultList = new ArrayList<>();

                for (Station station : targetStations) {
                    try {
                        // 根据电站类型确定结算单类型
                        SettlementFileTypeEnum fileType;
                        if (station.getType() == 3) { // 储能
                            fileType = SettlementFileTypeEnum.SETTLE_MONTH_STORAGE_DETAILS;
                        } else { // 光伏和风电
                            fileType = SettlementFileTypeEnum.SETTLE_MONTH_DETAILS;
                        }

                        // 查询单个电站的结算数据
                        Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                                station.getId(), startDate, endDate, startYearMonth, endYearMonth, fileType);

                        // 构造SettlementSummaryDTO
                        BigDecimal totalElectricity = BigDecimal.ZERO;
                        BigDecimal avgPrice = BigDecimal.ZERO;
                        BigDecimal totalFee = BigDecimal.ZERO;

                        if (settlementData != null && !settlementData.isEmpty()) {
                            // 直接使用原始数据，不进行单位换算
                            totalElectricity = settlementData.get("totalSettlementElectricity") != null
                                    ? settlementData.get("totalSettlementElectricity")
                                    : BigDecimal.ZERO;

                            avgPrice = settlementData.get("avgTradePrice") != null ? settlementData.get("avgTradePrice")
                                    : BigDecimal.ZERO;

                            totalFee = settlementData.get("totalSettlementElectricFee") != null
                                    ? settlementData.get("totalSettlementElectricFee")
                                    : BigDecimal.ZERO;
                        }

                        SettlementSummaryDTO stationDto = new SettlementSummaryDTO(
                                station.getId(),
                                station.getName(),
                                station.getType(),
                                totalElectricity,
                                avgPrice,
                                totalFee);

                        resultList.add(stationDto);

                    } catch (Exception e) {
                        log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
                        // 即使查询失败，也添加基础信息
                        SettlementSummaryDTO stationDto = new SettlementSummaryDTO(
                                station.getId(),
                                station.getName(),
                                station.getType(),
                                BigDecimal.ZERO,
                                BigDecimal.ZERO,
                                BigDecimal.ZERO);
                        resultList.add(stationDto);
                    }
                }

                // 6. 按结算电量降序排列
                resultList
                        .sort((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity()));

                return Result.OK(resultList);
            } catch (Exception e) {
                log.error("获取交易概况数据失败", e);
                return Result.error("获取交易概况数据失败: " + e.getMessage());
            }
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取发电趋势数据
     */
    @PostMapping("/powerGenerationTrend")
    @ApiOperation(value = "获取发电趋势数据", notes = "根据时间维度获取发电趋势，电站ID为空时返回省份所有电站数据")
    public Result<List<PowerGenerationTrendDto>> getPowerGenerationTrend(
            @RequestBody PowerGenerationTrendQueryParam param) {

        try {
            // 参数验证
            Result<String> validationResult = validatePowerGenerationTrendParam(param);
            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }

            // 检查是否为全国数据源汇总
            if (param.getProvinceId() == 0) {
                // 全国数据源汇总模式
                List<PowerGenerationTrendDto> aggregatedData = multiDataSourceAggregationService
                        .aggregateAllProvincesPowerGenerationTrend(param);
                return Result.OK(aggregatedData);
            }

            // 1. 先在主数据源中查询电站信息
            List<Station> targetStations = getTargetStations(param.getStationId(), param.getProvinceId());
            if (targetStations.isEmpty()) {
                return Result.error(param.getStationId() != null ? "电站不存在" : "该省份下没有找到电站数据");
            }

            // 2. 切换到省份数据源查询发电趋势数据
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(param.getProvinceId());
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + param.getProvinceId());
            }

            DynamicDataSourceContextHolder.push(dsKey);
            try {
                List<PowerGenerationTrendDto> allTrendData = new ArrayList<>();

                // 3. 为每个电站查询发电趋势数据
                for (Station station : targetStations) {
                    try {
                        log.info("开始查询电站发电趋势，电站ID: {}, 电站名称: {}, 省份ID: {}, 时间维度: {}",
                                station.getId(), station.getName(), param.getProvinceId(),
                                validationResult.getResult());

                        PowerGenerationTrendQueryParam stationParam = createStationParam(param, station.getId(),
                                validationResult.getResult());

                        log.info("创建的查询参数: stationId={}, provinceId={}, timeDimension={}, queryDate={}",
                                stationParam.getStationId(), stationParam.getProvinceId(),
                                stationParam.getTimeDimension(), stationParam.getQueryDate());

                        List<PowerGenerationTrendDto> stationTrendData = powerService
                                .getPowerGenerationTrend(stationParam);

                        // 无论是否有数据，都确保返回完整的时间序列
                        List<PowerGenerationTrendDto> completeData = ensureCompleteTimeSeriesData(stationTrendData,
                                param, station);

                        log.info("查询完成，电站ID: {}, 返回数据条数: {}", station.getId(), completeData.size());

                        allTrendData.addAll(completeData);

                    } catch (Exception e) {
                        log.error("查询电站{}发电趋势失败，创建完整的默认数据: {}", station.getId(), e.getMessage(), e);
                        // 即使查询失败，也创建完整的时间序列数据
                        List<PowerGenerationTrendDto> defaultData = createCompleteTimeSeriesData(param, station);
                        allTrendData.addAll(defaultData);
                    }
                }

                // 4. 按时间标签排序
                allTrendData.sort((a, b) -> {
                    // 先按电站ID排序，再按时间标签排序
                    int stationCompare = a.getStationId().compareTo(b.getStationId());
                    if (stationCompare != 0)
                        return stationCompare;
                    return a.getTimeLabel().compareTo(b.getTimeLabel());
                });

                return Result.OK(allTrendData);
            } finally {
                DynamicDataSourceContextHolder.clear();
            }

        } catch (Exception e) {
            log.error("获取发电趋势数据失败: {}", e.getMessage(), e);
            return Result.error("获取发电趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 确保返回完整的时间序列数据（补齐缺失的时间点）
     */
    private List<PowerGenerationTrendDto> ensureCompleteTimeSeriesData(
            List<PowerGenerationTrendDto> actualData,
            PowerGenerationTrendQueryParam param,
            Station station) {

        // 创建完整的时间序列模板
        List<PowerGenerationTrendDto> completeData = createCompleteTimeSeriesData(param, station);

        if (actualData == null || actualData.isEmpty()) {
            return completeData;
        }

        // 将实际数据映射到完整的时间序列中
        Map<String, PowerGenerationTrendDto> actualDataMap = actualData.stream()
                .collect(Collectors.toMap(
                        PowerGenerationTrendDto::getTimeLabel,
                        dto -> dto,
                        (existing, replacement) -> replacement // 如果有重复，使用新的
                ));

        // 用实际数据替换默认数据
        for (PowerGenerationTrendDto template : completeData) {
            PowerGenerationTrendDto actual = actualDataMap.get(template.getTimeLabel());
            if (actual != null) {
                template.setPowerGeneration(actual.getPowerGeneration());
                template.setActualPower(actual.getActualPower());
            }
        }

        return completeData;
    }

    /**
     * 创建完整的时间序列数据模板
     */
    private List<PowerGenerationTrendDto> createCompleteTimeSeriesData(
            PowerGenerationTrendQueryParam param,
            Station station) {

        List<PowerGenerationTrendDto> completeData = new ArrayList<>();
        String timeDimension = param.getTimeDimension();

        if ("3".equals(timeDimension)) {
            // 日维度：创建96个点（每15分钟一个点）
            for (int i = 0; i < 96; i++) {
                int hour = i / 4;
                int minute = (i % 4) * 15;
                String timeLabel = String.format("%02d:%02d", hour, minute);

                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }

        } else if ("2".equals(timeDimension)) {
            // 月维度：创建该月所有天的数据
            String queryDate = param.getQueryDate(); // 格式：YYYY-MM
            int daysInMonth = getDaysInMonth(queryDate);

            for (int day = 1; day <= daysInMonth; day++) {
                String timeLabel = String.format("%02d日", day);
                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }

        } else if ("1".equals(timeDimension)) {
            // 年维度：创建12个月的数据
            for (int month = 1; month <= 12; month++) {
                String timeLabel = String.format("%02d月", month);
                PowerGenerationTrendDto trend = createTrendDto(timeLabel, station);
                completeData.add(trend);
            }
        }

        return completeData;
    }

    /**
     * 创建单个趋势数据点
     */
    private PowerGenerationTrendDto createTrendDto(String timeLabel, Station station) {
        PowerGenerationTrendDto trend = new PowerGenerationTrendDto();
        trend.setTimeLabel(timeLabel);
        trend.setPowerGeneration(0.0);
        trend.setActualPower(0.0);
        trend.setStationId(station.getId());
        trend.setStationName(station.getName());
        trend.setStationType(station.getType());
        return trend;
    }

    /**
     * 计算指定月份的天数
     */
    private int getDaysInMonth(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            if (month == 2) {
                // 二月份，判断闰年
                return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
            } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                return 30;
            } else {
                return 31;
            }
        } catch (Exception e) {
            log.error("解析月份失败: {}", yearMonth, e);
            return 31; // 默认返回31天
        }
    }

    @ApiOperation(value = "电量电价", notes = "安徽查日前节点的出清电量电价。江苏查实时节点出清电量和电价")
    @GetMapping("/getAhuFieldOptimized")
    public Result<?> getAhuFieldOptimized(
            @ApiParam(value = "省份ID，0表示根据电站ID自动获取省份") @RequestParam Integer provinceId,
            @ApiParam(value = "电站ID") @RequestParam(required = false) Long stationId,
            @ApiParam(value = "日期") @RequestParam String date,
            @ApiParam(value = "维度: 1-年度, 2-月度, 3-日度") @RequestParam(defaultValue = "3") String dimension) {
        long startTime = System.currentTimeMillis();

        try {
            // 基础参数验证
            if (dimension == null || dimension.trim().isEmpty()) {
                return Result.error("时间维度不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("日期不能为空");
            }

            // 验证时间维度：1-年，2-月，3-日
            String timeDimension = dimension.trim();
            if (!"1".equals(timeDimension) && !"2".equals(timeDimension) && !"3".equals(timeDimension)) {
                return Result.error("时间维度只能是 1(年)、2(月) 或 3(日)");
            }

            // 处理省份ID为0的情况：通过电站ID获取省份信息
            Integer actualProvinceId = provinceId;
            if (provinceId != null && provinceId == 0) {
                if (stationId == null) {
                    return Result.error("当省份ID为0时，必须提供电站ID");
                }

                log.info("省份ID为0，通过电站ID获取省份信息 - 电站ID: {}", stationId);

                try {
                    // 查询电站信息获取省份ID
                    Station station = stationService.getById(stationId);
                    if (station == null) {
                        return Result.error("电站不存在，电站ID: " + stationId);
                    }

                    if (station.getProvinceId() == null) {
                        return Result.error("电站省份信息缺失，电站ID: " + stationId);
                    }

                    actualProvinceId = station.getProvinceId();
                    log.info("通过电站ID获取到省份信息 - 电站ID: {}, 电站名称: {}, 省份ID: {}",
                            stationId, station.getName(), actualProvinceId);

                } catch (Exception e) {
                    log.error("通过电站ID获取省份信息失败 - 电站ID: {}, 错误: {}", stationId, e.getMessage(), e);
                    return Result.error("获取电站省份信息失败: " + e.getMessage());
                }
            }

            // 验证最终的省份ID
            if (actualProvinceId == null || actualProvinceId <= 0) {
                return Result.error("无效的省份ID: " + actualProvinceId);
            }

            // 获取数据源配置
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(actualProvinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + actualProvinceId);
            }

            log.info("电量电价查询开始 - 原始省份ID: {}, 实际省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}, 数据源: {}",
                    provinceId, actualProvinceId, stationId, date, timeDimension, dsKey);

            // 切换数据源并执行查询
            DynamicDataSourceContextHolder.push(dsKey);
            try {
                Object result = electricityDataService.getElectricityDataByProvince(actualProvinceId, stationId, date,
                        timeDimension);

                long queryTime = System.currentTimeMillis() - startTime;
                log.info("电量电价查询成功 - 省份ID: {}, 电站ID: {}, 返回数据类型: {}, 查询耗时: {}ms",
                        actualProvinceId, stationId, result != null ? result.getClass().getSimpleName() : "null",
                        queryTime);

                return Result.OK(result);

            } finally {
                DynamicDataSourceContextHolder.clear();
            }

        } catch (IllegalArgumentException e) {
            log.error("电量电价查询参数错误 - 省份ID: {}, 电站ID: {}, 错误: {}", provinceId, stationId, e.getMessage());
            return Result.error("参数错误: " + e.getMessage());

        } catch (Exception e) {
            long queryTime = System.currentTimeMillis() - startTime;
            log.error("电量电价查询系统异常 - 省份ID: {}, 电站ID: {}, 查询耗时: {}ms, 错误: {}",
                    provinceId, stationId, queryTime, e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取省份列表", notes = "从station中获取所有省份信息")
    @GetMapping("/provinces")
    public Result<List<ProvinceDTO>> getProvinces() {
        try {
            Map<Integer, String> allDataSources = Optional.ofNullable(ProvinceDataSourceUtil.getAllProvinceDataSource())
                    .orElse(Collections.emptyMap());

            List<ProvinceDTO> provinceList = allDataSources.entrySet().stream()
                    .map(entry -> {
                        Integer provinceId = entry.getKey();
                        String dataSource = entry.getValue();
                        String provinceName = ProvinceDataSourceUtil.getProvinceName(provinceId);
                        ProvinceDTO dto = new ProvinceDTO();
                        dto.setProvinceId(provinceId);
                        dto.setDataSource(dataSource);
                        dto.setProvinceName(provinceName);
                        return dto;
                    })
                    .sorted(Comparator.comparing(ProvinceDTO::getProvinceId))
                    .collect(Collectors.toList());

            return Result.OK(provinceList);
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return Result.error("获取省份列表失败: " + (e.getMessage() != null ? e.getMessage() : "未知错误"));
        }
    }

    /**
     * 获取月份的最后一天
     */
    private String getMonthEndDate(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 计算该月的最后一天
            int lastDay;
            if (month == 2) {
                // 二月份，需要判断闰年
                lastDay = (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
            } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                lastDay = 30;
            } else {
                lastDay = 31;
            }

            return String.format("%04d-%02d-%02d", year, month, lastDay);
        } catch (Exception e) {
            log.error("计算月末日期失败: {}", yearMonth, e);
            return yearMonth + "-31"; // 默认返回31号
        }
    }

    /**
     * 将省份汇总数据转换为电站列表格式
     * 
     * @param provinceData 省份汇总数据
     * @return 电站列表
     */
    private List<StationSettlementDTO> convertToStationList(List<Map<String, Object>> provinceData) {
        if (provinceData == null || provinceData.isEmpty()) {
            return new ArrayList<>();
        }

        return provinceData.stream()
                .filter(data -> data.get("stationId") != null) // 过滤掉没有电站ID的数据
                .map(data -> {
                    Long stationId = convertToLong(data.get("stationId"));
                    String stationName = convertToString(data.get("stationName"));
                    Integer stationType = convertToInteger(data.get("stationType"));

                    // 直接使用原始数据，不进行单位换算
                    BigDecimal totalElectricity = convertToBigDecimal(data.get("totalSettlementElectricity"));
                    BigDecimal avgPrice = convertToBigDecimal(data.get("avgTradePrice"));
                    BigDecimal totalFee = convertToBigDecimal(data.get("totalSettlementElectricFee"));

                    return new StationSettlementDTO(
                            stationId,
                            stationName,
                            stationType,
                            totalElectricity,
                            avgPrice,
                            totalFee);
                })
                .filter(dto -> dto.getTotalSettlementElectricity().compareTo(BigDecimal.ZERO) > 0) // 过滤掉没有结算数据的电站
                .sorted((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity())) // 按结算电量降序排列
                .collect(Collectors.toList());
    }

    /**
     * 安全转换为Long
     */
    private Long convertToLong(Object value) {
        if (value == null)
            return null;
        if (value instanceof Long)
            return (Long) value;
        if (value instanceof Integer)
            return ((Integer) value).longValue();
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 安全转换为String
     */
    private String convertToString(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全转换为Integer
     */
    private Integer convertToInteger(Object value) {
        if (value == null)
            return null;
        if (value instanceof Integer)
            return (Integer) value;
        if (value instanceof Long)
            return ((Long) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 安全转换为BigDecimal
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null)
            return BigDecimal.ZERO;
        if (value instanceof BigDecimal)
            return (BigDecimal) value;
        if (value instanceof Double)
            return BigDecimal.valueOf((Double) value);
        if (value instanceof Float)
            return BigDecimal.valueOf((Float) value);
        if (value instanceof Integer)
            return BigDecimal.valueOf((Integer) value);
        if (value instanceof Long)
            return BigDecimal.valueOf((Long) value);
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取电站列表的结算数据（统一格式）
     * 
     * @param stations       电站信息列表（已在主数据源中查询）
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param startYearMonth 开始年月
     * @param endYearMonth   结束年月
     * @return 电站结算数据列表
     */
    private List<StationSettlementDTO> getStationListSettlementData(List<Station> stations, String startDate,
            String endDate,
            String startYearMonth, String endYearMonth) {
        List<StationSettlementDTO> result = new ArrayList<>();

        for (Station station : stations) {
            try {
                // 根据电站类型确定结算单类型
                SettlementFileTypeEnum fileType;
                if (station.getType() == 3) { // 储能
                    fileType = SettlementFileTypeEnum.SETTLE_MONTH_STORAGE_DETAILS;
                } else { // 光伏和风电
                    fileType = SettlementFileTypeEnum.SETTLE_MONTH_DETAILS;
                }

                // 查询电站结算数据
                Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                        station.getId(), startDate, endDate, startYearMonth, endYearMonth, fileType);

                // 构造estation结算DTO
                StationSettlementDTO stationDto = new StationSettlementDTO();
                stationDto.setStationId(station.getId());
                stationDto.setStationName(station.getName());
                stationDto.setStationType(station.getType());

                if (settlementData != null && !settlementData.isEmpty()) {
                    // 直接使用原始数据，不进行单位换算
                    BigDecimal totalElectricity = settlementData.get("totalSettlementElectricity");
                    BigDecimal avgPrice = settlementData.get("avgTradePrice");
                    BigDecimal totalFee = settlementData.get("totalSettlementElectricFee");

                    stationDto.setTotalSettlementElectricity(
                            totalElectricity != null ? totalElectricity : BigDecimal.ZERO);
                    stationDto.setAvgTradePrice(avgPrice != null ? avgPrice : BigDecimal.ZERO);
                    stationDto.setTotalSettlementElectricFee(totalFee != null ? totalFee : BigDecimal.ZERO);
                } else {
                    stationDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                    stationDto.setAvgTradePrice(BigDecimal.ZERO);
                    stationDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                }

                result.add(stationDto);

            } catch (Exception e) {
                log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
                // 即使查询失败，也添加基础信息
                StationSettlementDTO stationDto = new StationSettlementDTO();
                stationDto.setStationId(station.getId());
                stationDto.setStationName(station.getName());
                stationDto.setStationType(station.getType());
                stationDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                stationDto.setAvgTradePrice(BigDecimal.ZERO);
                stationDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                result.add(stationDto);
            }
        }

        // 按结算电量降序排列
        result.sort((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity()));

        return result;
    }

    /**
     * 优化的参数验证方法
     * 
     * @param param 查询参数
     * @return 验证结果，成功时result包含转换后的时间维度字符串
     */
    private Result<String> validatePowerGenerationTrendParam(PowerGenerationTrendQueryParam param) {
        // 使用 Optional 和 Stream 优化空值检查
        if (param == null) {
            return Result.error("查询参数不能为空");
        }

        // 省份ID验证（必填）
        if (param.getProvinceId() == null) {
            return Result.error("省份ID不能为空");
        }

        // 时间维度验证（必填）
        String timeDimension = Optional.ofNullable(param.getTimeDimension())
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .orElse(null);

        if (timeDimension == null) {
            return Result.error("时间维度不能为空");
        }

        // 查询日期验证（必填）
        String queryDate = Optional.ofNullable(param.getQueryDate())
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .orElse(null);

        if (queryDate == null) {
            return Result.error("查询日期不能为空");
        }

        // 时间维度格式验证和转换
        String dimensionStr = convertTimeDimension(timeDimension);
        if (dimensionStr == null) {
            return Result.error("时间维度只能是 1(年)、2(月) 或 3(日)");
        }

        return Result.OK(dimensionStr);
    }

    /**
     * 时间维度验证方法
     * 
     * @param timeDimension 数字格式的时间维度
     * @return 验证通过返回原值，无效时返回null
     */
    private String convertTimeDimension(String timeDimension) {
        switch (timeDimension) {
            case "1":
                return "1";
            case "2":
                return "2";
            case "3":
                return "3";
            default:
                return null;
        }
    }

    /**
     * 获取目标电站列表
     * 
     * @param stationId  电站ID（可为空）
     * @param provinceId 省份ID
     * @return 电站列表
     */
    private List<Station> getTargetStations(Long stationId, Integer provinceId) {
        if (stationId != null) {
            // 查询指定电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getId, stationId)
                    .eq(Station::getProvinceId, provinceId);
            Station station = stationService.getOne(stationQuery);
            return station != null ? Arrays.asList(station) : new ArrayList<>();
        } else {
            // 查询该省份的所有电站
            return stationService.getStationsByProvinceId(provinceId);
        }
    }

    /**
     * 创建电站查询参数
     * 
     * @param originalParam 原始参数
     * @param stationId     电站ID
     * @param timeDimension 数字格式的时间维度
     * @return 电站查询参数
     */
    private PowerGenerationTrendQueryParam createStationParam(PowerGenerationTrendQueryParam originalParam,
            Long stationId, String timeDimension) {
        // 参数验证
        if (stationId == null) {
            throw new IllegalArgumentException("电站ID不能为空");
        }
        if (originalParam.getProvinceId() == null) {
            throw new IllegalArgumentException("省份ID不能为空");
        }
        if (timeDimension == null || timeDimension.trim().isEmpty()) {
            throw new IllegalArgumentException("时间维度不能为空");
        }
        if (originalParam.getQueryDate() == null || originalParam.getQueryDate().trim().isEmpty()) {
            throw new IllegalArgumentException("查询日期不能为空");
        }

        PowerGenerationTrendQueryParam stationParam = new PowerGenerationTrendQueryParam();
        stationParam.setStationId(stationId);
        stationParam.setProvinceId(originalParam.getProvinceId());
        stationParam.setTimeDimension(timeDimension);
        stationParam.setQueryDate(originalParam.getQueryDate());
        return stationParam;
    }

    /**
     * 将Object转换为Double，处理null值和类型转换
     * 
     * @param value 待转换的值
     * @return 转换后的Double值，null时返回0.0
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法转换为Double: {}", value);
            return 0.0;
        }
    }

    /**
     * 将汇总数据转换为DashboardSummaryDTO
     */
    private DashboardSummaryDTO convertToDashboardSummaryDTO(Map<String, Object> aggregatedData) {
        DashboardSummaryDTO result = new DashboardSummaryDTO();
        
        if (aggregatedData == null || aggregatedData.isEmpty()) {
            return result;
        }

        // 创建电站类型统计数据
        StationTypeStatisticsDTO statistics = new StationTypeStatisticsDTO(
            convertToDouble(aggregatedData.get("totalCapacity")), // tradingCapacity
            convertToInteger(aggregatedData.get("totalStations")), // stationCount
            convertToInteger(aggregatedData.get("windStationCount")), // windStationCount
            convertToInteger(aggregatedData.get("solarStationCount")), // solarStationCount
            convertToInteger(aggregatedData.get("storageStationCount")), // storageStationCount
            convertToDouble(aggregatedData.get("totalPowerGeneration")), // accumulatedPower
            convertToDouble(aggregatedData.get("plannedPower")), // plannedPower
            convertToDouble(aggregatedData.get("avgPrice")), // settlementAvgPrice
            convertToDouble(aggregatedData.get("settlementPower")), // settlementPower
            convertToDouble(aggregatedData.get("limitedPower")), // limitedPower
            convertToDouble(aggregatedData.get("targetPowerPrice")), // targetPowerPrice
            convertToDouble(aggregatedData.get("benchmarkPrice")) // benchmarkPrice
        );
        
        result.setStationTypeStatistics(statistics);
        
        // 设置能源类型统计（如果有的话）
        @SuppressWarnings("unchecked")
        Map<Integer, Integer> energyTypeCount = (Map<Integer, Integer>) aggregatedData.get("energyTypeCount");
        if (energyTypeCount != null) {
            result.setEnergyTypeCount(energyTypeCount);
        } else {
            result.setEnergyTypeCount(new HashMap<>());
        }
        
        return result;
    }
}