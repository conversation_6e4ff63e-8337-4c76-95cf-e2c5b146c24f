package org.jeecg.modules.api.power_trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.api.power_trade.constant.StationConstants;
import org.jeecg.modules.api.power_trade.mapper.StationMapper;
import org.jeecg.modules.api.power_trade.service.StationAggregationService;
import org.jeecg.modules.api.power_trade.vo.PowerDashboardVO;
import org.jeecg.modules.api.power_trade.vo.StationTypeStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 电站数据聚合服务实现类
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class StationAggregationServiceImpl implements StationAggregationService {

    @Autowired
    private StationMapper stationMapper;

    @Override
    public PowerDashboardVO getAggregatedTradeDataByStationIds(List<Long> stationIds, String year, String month) {
        log.info("开始聚合查询电站交易数据，电站数量: {}, 年份: {}, 月份: {}", 
                stationIds != null ? stationIds.size() : 0, year, month);
        
        // 参数校验
        if (stationIds == null || stationIds.isEmpty()) {
            log.warn("电站ID列表为空，返回默认数据");
            return createDefaultPowerDashboardVO();
        }
        
        if (StringUtils.isBlank(year) || StringUtils.isBlank(month)) {
            log.warn("年份或月份参数为空，year: {}, month: {}", year, month);
            return createDefaultPowerDashboardVO();
        }
        
        try {
            PowerDashboardVO result = stationMapper.getAggregatedTradeDataByStationIds(stationIds, year, month);
            
            if (result == null) {
                log.warn("查询结果为空，返回默认数据");
                result = createDefaultPowerDashboardVO();
            } else {
                log.info("聚合查询完成，交易容量: {}MW, 场站数量: {}, 结算均价: {}元/MWh", 
                        result.getTradingCapacity(), result.getStationCount(), result.getSettlementAvgPrice());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("聚合查询电站交易数据异常: {}", e.getMessage(), e);
            return createDefaultPowerDashboardVO();
        }
    }

    @Override
    public PowerDashboardVO getAggregatedTradeDataByRegion(Integer provinceId, String year, String month) {
        log.info("开始按区域聚合查询交易数据，区域: {}, 年份: {}, 月份: {}", provinceId, year, month);
        
//        // 参数校验
//        if (StringUtils.isBlank(provinceId)) {
//            log.warn("区域代码为空，返回默认数据");
//            return createDefaultPowerDashboardVO();
//        }
        
        if (StringUtils.isBlank(year) || StringUtils.isBlank(month)) {
            log.warn("年份或月份参数为空，year: {}, month: {}", year, month);
            return createDefaultPowerDashboardVO();
        }
        
        try {
            PowerDashboardVO result = stationMapper.getAggregatedTradeDataByRegion(provinceId, year, month);
            
            if (result == null) {
                log.warn("查询结果为空，返回默认数据");
                result = createDefaultPowerDashboardVO();
            } else {
                // 设置区域信息
                result.setProvinceId(provinceId);
                log.info("区域聚合查询完成，区域: {}, 交易容量: {}MW, 场站数量: {}", 
                        provinceId, result.getTradingCapacity(), result.getStationCount());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("按区域聚合查询交易数据异常: {}", e.getMessage(), e);
            return createDefaultPowerDashboardVO();
        }
    }

    @Override
    public List<StationTypeStatisticsVO> getStationTypeStatistics(List<Long> stationIds, String year, String month) {
        log.info("开始查询电站类型统计，电站数量: {}, 年份: {}, 月份: {}", 
                stationIds != null ? stationIds.size() : 0, year, month);
        
        // 参数校验
        if (stationIds == null || stationIds.isEmpty()) {
            log.warn("电站ID列表为空，返回空列表");
            return new ArrayList<>();
        }
        
        try {
            List<StationTypeStatisticsVO> result = stationMapper.getStationTypeStatistics(stationIds, year, month);
            
            if (result == null) {
                result = new ArrayList<>();
            }
            
            log.info("电站类型统计查询完成，统计类型数量: {}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询电站类型统计异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Long> getStationIdsByProvinceId(Integer provinceId) {
        log.info("开始查询区域电站ID列表，区域: {}", provinceId);
        
        // 参数校验
//        if (StringUtils.isBlank(provinceId)) {
//            log.warn("区域代码为空，返回空列表");
//            return new ArrayList<>();
//        }
        
        try {
            List<Long> result = stationMapper.getStationIdsByProvinceId(provinceId);
            
            if (result == null) {
                result = new ArrayList<>();
            }
            
            log.info("区域电站ID查询完成，区域: {}, 电站数量: {}", provinceId, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询区域电站ID列表异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建默认的PowerDashboardVO对象
     * @return 默认数据对象
     */
    private PowerDashboardVO createDefaultPowerDashboardVO() {
        PowerDashboardVO vo = new PowerDashboardVO();
        vo.setTradingCapacity(0.0);
        vo.setStationCount(0);
        vo.setWindStationCount(0);
        vo.setSolarStationCount(0);
        vo.setStorageStationCount(0);
        vo.setAccumulatedPower(0.0);
        vo.setPlannedPower(0.0);
        vo.setLimitedPower(0.0);
        vo.setSettlementPower(0.0);
        vo.setSettlementAvgPrice(0.0);
        vo.setCoalBenchmarkPrice(0.0);
        return vo;
    }

    /**
     * 格式化PowerDashboardVO数据
     * @param data 数据对象
     * @param provinceId 区域代码
     */
    private void formatPowerDashboardVO(PowerDashboardVO data, Integer provinceId) {
        if (data == null) {
            return;
        }
        
        // 设置区域信息
        data.setProvinceId(provinceId);
        
        // 容量单位转换：如果>=1000MW则转换为GW
        if (data.getTradingCapacity() != null && data.getTradingCapacity() >= StationConstants.CAPACITY_GW_THRESHOLD) {
            data.setTradingCapacity(data.getTradingCapacity() / StationConstants.CAPACITY_GW_THRESHOLD);
        }
        
        // 确保数值不为null
        if (data.getTradingCapacity() == null) data.setTradingCapacity(0.0);
        if (data.getStationCount() == null) data.setStationCount(0);
        if (data.getWindStationCount() == null) data.setWindStationCount(0);
        if (data.getSolarStationCount() == null) data.setSolarStationCount(0);
        if (data.getStorageStationCount() == null) data.setStorageStationCount(0);
        if (data.getAccumulatedPower() == null) data.setAccumulatedPower(0.0);
        if (data.getPlannedPower() == null) data.setPlannedPower(0.0);
        if (data.getLimitedPower() == null) data.setLimitedPower(0.0);
        if (data.getSettlementPower() == null) data.setSettlementPower(0.0);
        if (data.getSettlementAvgPrice() == null) data.setSettlementAvgPrice(0.0);
        if (data.getCoalBenchmarkPrice() == null ) {
            data.setCoalBenchmarkPrice(0.0);
        }
    }
}
