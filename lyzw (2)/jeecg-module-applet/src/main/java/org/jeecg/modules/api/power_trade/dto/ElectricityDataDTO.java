package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ElectricityDataDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "日期")
    private Date date;

    @ApiModelProperty(value = "时间标识")
    private String time;

    @ApiModelProperty(value = "电量值")
    private BigDecimal value;

    @ApiModelProperty(value = "电价值")
    private BigDecimal price;

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "省份ID")
    private Long provinceId;

    @ApiModelProperty(value = "数据来源表")
    private String sourceTable;
}