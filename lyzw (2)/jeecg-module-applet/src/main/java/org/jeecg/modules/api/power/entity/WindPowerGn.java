package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("forecast_power_report.wind_power_gn")
public class WindPowerGn {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("station_id")
    private Long stationId;

    @TableField("date")
    private String date;

    @TableField("time")
    private String time;

    @TableField("power")
    private Double power;

    @TableField("wind_speed")
    private Double windSpeed;

    @TableField("temperature")
    private Double temperature;

    @TableField("humidity")
    private Double humidity;

    @TableField("pressure")
    private Double pressure;

    @TableField("wind_dir")
    private Double windDir;
}
