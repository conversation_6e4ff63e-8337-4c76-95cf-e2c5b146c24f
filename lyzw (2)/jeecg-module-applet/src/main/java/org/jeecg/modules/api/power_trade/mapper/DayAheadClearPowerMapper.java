package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.DayAheadClearPower;

import java.util.List;
import java.util.Map;

public interface DayAheadClearPowerMapper extends BaseMapper<DayAheadClearPower> {

    /**
     * 按日查询出清电量明细
     */
    List<DayAheadClearPower> selectByDay(@Param("stationId") Long stationId, @Param("date") String date);

    /**
     * 按月汇总查询出清电量
     */
    List<Map<String, Object>> selectMonthlySum(@Param("stationId") Long stationId, @Param("yearMonth") String yearMonth);

    /**
     * 按年汇总查询出清电量
     */
    List<Map<String, Object>> selectYearlySum(@Param("stationId") Long stationId, @Param("year") String year);
}