package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电站年度交易信息VO
 */
@Data
@ApiModel(value = "StationYearlyTradeVO", description = "电站年度交易信息")
public class StationYearlyTradeVO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "年度交易总电量(MWh)")
    private BigDecimal totalTradePower;

    @ApiModelProperty(value = "年度交易均价(元/MWh)")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "年计划发电量(MWh)")
    private BigDecimal plannedPower;

    @ApiModelProperty(value = "年实际发电量(MWh)")
    private BigDecimal actualPower;

    @ApiModelProperty(value = "完成率(%)")
    private BigDecimal completionRate;

    /**
     * 计算完成率
     */
    public BigDecimal getCompletionRate() {
        if (plannedPower == null || plannedPower.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        if (actualPower == null) {
            return BigDecimal.ZERO;
        }
        return actualPower.divide(plannedPower, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 获取总电量显示文本
     */
    public String getTotalTradePowerDisplay() {
        if (totalTradePower == null) {
            return "0 MWh";
        }
        return totalTradePower.toPlainString() + " MWh";
    }

    /**
     * 获取均价显示文本
     */
    public String getAvgTradePriceDisplay() {
        if (avgTradePrice == null) {
            return "0 元/MWh";
        }
        return avgTradePrice.toPlainString() + " 元/MWh";
    }
}
