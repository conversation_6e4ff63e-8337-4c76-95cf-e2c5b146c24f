package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.realm.CachingRealm;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.StationDetailDTO;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.mapper.YearlyPowerPlanMapper;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power_trade.service.*;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.util.NullValueHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 电力交易电站控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-电站接口")
@RequestMapping("/api/power_trade/stations")
public class StationController {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private DayAheadClearPowerService dayAheadClearPowerService;

    @Autowired
    private EnergyNewDailyCleanService energyNewDailyCleanService;

    @Autowired
    private EnergyStorageDailyCleanService energyStorageDailyCleanService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    @Autowired
    private CachingRealm cachingRealm;

    @GetMapping("/list")
    @ApiOperation(value = "电站总览", notes = "根据省份显示涉及交易场站列表，包含结算数据")
    public Result<Map<String, Object>> getStationList(
            @ApiParam(value = "页码") @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "省份ID (0-全国, 1-江苏, 2-安徽)", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "年份", required = true) @RequestParam(required = false) String year,
            @ApiParam(value = "月份", required = true) @RequestParam(required = false) String month,
            @ApiParam(value = "电站名称搜索") @RequestParam(required = false) String name) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create()
                    .validatePagination(pageNo, pageSize)
                    .validateProvinceId(provinceId)
                    .validateYearMonth(year, month);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        // 检查是否为全国数据源汇总
        if (provinceId == 0) {
            // 全国数据源汇总模式
            Map<String, Object> aggregatedResult = multiDataSourceAggregationService
                    .aggregateAllProvincesStationList(pageNo, pageSize, year, month, name);
            // 确保返回值不为空
            aggregatedResult = NullValueHandler.ensurePaginationResult(aggregatedResult);
            return Result.OK(aggregatedResult);
        }

        // 单省份模式
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID");
        }

        // 获取基础站点分页数据
        Page<Station> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<Station> stationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        stationLambdaQueryWrapper.eq(Station::getTradeStatus, 1)
                .eq(true, Station::getProvinceId, provinceId);

        // 添加电站名称搜索条件
        if (name != null && !name.trim().isEmpty()) {
            stationLambdaQueryWrapper.like(Station::getName, name.trim());
        }

        IPage<Station> pageList = stationService.page(page, stationLambdaQueryWrapper);

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 为每个电站添加结算数据
            List<Map<String, Object>> stationListWithSettlement = pageList.getRecords().stream()
                    .map(station -> buildStationWithSettlementData(station, year, month))
                    .map(NullValueHandler::ensureNoNullValues)
                    .collect(Collectors.toList());

            // 构建分页结果
            Map<String, Object> paginationInfo = new HashMap<>();
            paginationInfo.put("current", pageList.getCurrent());
            paginationInfo.put("size", pageList.getSize());
            paginationInfo.put("total", pageList.getTotal());
            paginationInfo.put("pages", pageList.getPages());
            paginationInfo.put("records", stationListWithSettlement);

            // 确保分页结果不为空
            paginationInfo = NullValueHandler.ensureNoNullValues(paginationInfo);
            return Result.OK(paginationInfo);

        } catch (Exception e) {
            log.error("获取电站列表数据失败: {}", e.getMessage(), e);
            return Result.error("获取电站列表数据失败: " + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    @ApiOperation(value = "电站详情", notes = "获取电站详细信息，包括基础信息、发电量和交易信息")
    @GetMapping("/{id}/detail")
    public Result<StationDetailResponseDTO> getStationDetail(
            @PathVariable Long id,
            @RequestParam String provinceId,
            @RequestParam String date,
            @RequestParam(defaultValue = "1") String dimension) {
        try {
            // 参数验证
            Result<String> validationResult = ParamValidationUtil.validate(() -> {
                ParamValidationUtil.Validator validator = ParamValidationUtil.Validator.create();
                validator.validateStationId(id);
                String timeDimension = validator.validateTimeDimension(dimension);
                validator.validateDateFormat(date, timeDimension);
                return timeDimension;
            });
            if (!validationResult.isSuccess()) {
                return Result.error(validationResult.getMessage());
            }
            String timeDimension = validationResult.getResult();

            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(Integer.valueOf(provinceId));
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }

            DynamicDataSourceContextHolder.push(dsKey);

            // 构建电站详情响应
            StationDetailResponseDTO response = buildStationDetailResponse(id, date, timeDimension);

            // 确保响应数据不为空
            if (response == null) {
                response = new StationDetailResponseDTO();
            }
            ensureStationDetailNotNull(response);

            return Result.OK(response);

        } catch (Exception e) {
            log.error("获取电站详情失败", e);
            return Result.error("获取电站详情失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取电站年度交易电量信息
     */
    @GetMapping("/{id}/yearly-trading-info")
    @ApiOperation(value = "获取电站年度交易电量信息", notes = "获取电站的年度交易信息，包含月度分解数据")
    public Result<Map<String, Object>> getStationYearlyTradingInfo(
            @ApiParam(value = "电站ID", required = true) @PathVariable Long id,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "年份", required = true) @RequestParam String year) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (year == null || !year.matches("\\d{4}")) {
                return Result.error("年份格式不正确，请使用yyyy格式");
            }

            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }
            DynamicDataSourceContextHolder.push(dsKey);

            log.info("查询电站年度交易电量信息 - 电站ID: {}, 省份ID: {}, 年份: {}", id, provinceId, year);

            // 验证电站是否属于该省份
            Station station = stationService.getById(id);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 查询年度交易电量信息
            Map<String, Object> tradingInfo = powerSideSettleService.getStationYearlyTradingInfo(id, year);

            // 添加额外信息
            tradingInfo.put("stationId", id);
            tradingInfo.put("stationName", station.getName());
            tradingInfo.put("hasData", tradingInfo.get("monthlyData") != null &&
                           !((List<?>) tradingInfo.get("monthlyData")).isEmpty());

            log.info("查询成功 - 年度交易总电量: {}, 年度交易均价: {}",
                    tradingInfo.get("yearlyTotalElectricity"),
                    tradingInfo.get("yearlyAveragePrice"));

            return Result.OK(tradingInfo);

        } catch (Exception e) {
            log.error("获取电站年度交易电量信息失败 - 电站ID: {}, 年份: {}", id, year, e);
            return Result.error("获取年度交易电量信息失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 获取电站月度交易电量信息
     */
    @GetMapping("/{id}/monthly-trading-info")
    @ApiOperation(value = "获取电站月度交易电量信息", notes = "获取电站的月度交易总电量和月度交易均价")
    public Result<Map<String, Object>> getStationMonthlyTradingInfo(
            @ApiParam(value = "电站ID", required = true) @PathVariable Long id,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId,
            @ApiParam(value = "年月", required = true) @RequestParam String yearMonth) {
        try {
            // 参数验证
            if (id == null || id <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (yearMonth == null || !yearMonth.matches("\\d{4}-\\d{2}")) {
                return Result.error("年月格式不正确，请使用yyyy-MM格式");
            }

            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }
            DynamicDataSourceContextHolder.push(dsKey);

            log.info("查询电站月度交易电量信息 - 电站ID: {}, 省份ID: {}, 年月: {}", id, provinceId, yearMonth);

            // 验证电站是否属于该省份
            Station station = stationService.getById(id);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            // 查询月度交易电量信息
            Map<String, Object> tradingInfo = powerSideSettleService.getStationMonthlyTradingInfo(id, yearMonth);

            if (tradingInfo == null || tradingInfo.isEmpty()) {
                log.warn("未查询到电站{}在{}的交易数据", id, yearMonth);
                // 返回默认值
                Map<String, Object> defaultInfo = new HashMap<>();
                defaultInfo.put("monthlyTotalElectricity", 0.0);
                defaultInfo.put("monthlyAveragePrice", 0.0);
                defaultInfo.put("monthlyTotalFee", 0.0);
                defaultInfo.put("actualInternetElectricity", 0.0);
                defaultInfo.put("contractElectricity", 0.0);
                defaultInfo.put("deviationElectricity", 0.0);
                defaultInfo.put("recordCount", 0);
                defaultInfo.put("stationId", id);
                defaultInfo.put("stationName", station.getName());
                defaultInfo.put("yearMonth", yearMonth);
                defaultInfo.put("hasData", false);
                return Result.OK(defaultInfo);
            }

            // 添加额外信息
            tradingInfo.put("stationId", id);
            tradingInfo.put("stationName", station.getName());
            tradingInfo.put("yearMonth", yearMonth);
            tradingInfo.put("hasData", true);

            return Result.OK(tradingInfo);

        } catch (Exception e) {
            log.error("获取电站月度交易电量信息失败 - 电站ID: {}, 年月: {}", id, yearMonth, e);
            return Result.error("获取月度交易电量信息失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 构建电站详情响应数据
     */
    private StationDetailResponseDTO buildStationDetailResponse(Long stationId, String date, String dimension) {
        StationDetailResponseDTO response = new StationDetailResponseDTO();

        // 1. 获取基础信息
        response.setBasicInfo(getStationBasicInfo(stationId, date));

        // 2. 获取发电量信息
        response.setPowerGeneration(getStationPowerGeneration(stationId, date, dimension));

        // 3. 获取交易信息（需要传递provinceId）
        // 从电站信息获取省份ID
        Station station = stationService.getById(stationId);
        String provinceId = station != null ? station.getProvinceId().toString() : "1";
        response.setTradeInfo(getStationTradeInfo(stationId, date, dimension, provinceId));

        return response;
    }

    /**
     * 获取电站基础信息
     */
    private StationDetailResponseDTO.BasicInfo getStationBasicInfo(Long stationId, String date) {
        // 获取电站基本信息
        Station station = stationService.getById(stationId);
        if (station == null) {
            throw new RuntimeException("电站不存在");
        }

        // 省份ID
        Integer provinceId = station.getProvinceId();
        // 获取年份和月份用于查询结算数据
        String year = date.substring(0, 4);
        String month = extractMonthFromDate(date);

        // 获取年度发电计划统计
        StationDetailDTO stationDetail = yearlyPowerPlanService.getStationDetailWithPowerPlan(stationId, year);

        // 计算累计发电量（通过功率预测实际功率求和除以4）
        BigDecimal totalPowerGeneration = calculateTotalPowerGeneration(stationId, year);

        // 获取结算数据（在当前数据源上下文中执行）
        Map<String, Object> settlementData = getSettlementDataForBasicInfo(station, year, month);

        StationDetailResponseDTO.BasicInfo basicInfo = new StationDetailResponseDTO.BasicInfo();
        basicInfo.setStationType(String.valueOf(station.getType()));
        basicInfo.setStationName(station.getName());
        basicInfo.setCapacity(BigDecimal.valueOf(station.getCapacity()));
        basicInfo.setYearPlanPowerGeneration(
                stationDetail != null ? stationDetail.getYearTotalPlanValue() : BigDecimal.ZERO);
        basicInfo.setTotalPowerGeneration(totalPowerGeneration);
        basicInfo.setProvinceId(provinceId);
        basicInfo.setProvinceName(ProvinceDataSourceUtil.getProvinceName(provinceId));

        // 设置结算数据
        basicInfo.setCurrentMonthPower((BigDecimal) settlementData.get("current_month_power"));
        basicInfo.setCurrentMonthPlanPower((BigDecimal) settlementData.get("current_month_plan_power"));
        basicInfo.setSettlementAveragePrice((BigDecimal) settlementData.get("settlement_average_price"));

        return basicInfo;
    }

    /**
     * 从日期中提取月份
     */
    private String extractMonthFromDate(String date) {
        if (date.length() >= 7) {
            return date.substring(5, 7);
        } else if (date.length() == 4) {
            // 如果只有年份，默认使用当前月份或01月
            return "01";
        }
        return "01";
    }

    /**
     * 获取用于BasicInfo的结算数据（在当前数据源上下文中执行）
     */
    private Map<String, Object> getSettlementDataForBasicInfo(Station station, String year, String month) {
        Map<String, Object> result = new HashMap<>();

        try {
            LambdaQueryWrapper<ScreenTradeSettlement> settlementWrapper = new LambdaQueryWrapper<>();
            settlementWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                    .eq(ScreenTradeSettlement::getYear, year)
                    .eq(ScreenTradeSettlement::getMonth, String.format("%02d", Integer.parseInt(month)));

            ScreenTradeSettlement settlement = screenTradeSettlementService.getOne(settlementWrapper);

            if (settlement != null) {
                result.put("current_month_power", settlement.getCurrentMonthPower() != null
                        ? settlement.getCurrentMonthPower()
                        : BigDecimal.ZERO);
                result.put("current_month_plan_power", settlement.getCurrentMonthPlanPower() != null
                        ? settlement.getCurrentMonthPlanPower()
                        : BigDecimal.ZERO);
                result.put("settlement_average_price", settlement.getSettlementAveragePrice() != null
                        ? settlement.getSettlementAveragePrice()
                        : BigDecimal.ZERO);
            } else {
                result.put("current_month_power", BigDecimal.ZERO);
                result.put("current_month_plan_power", BigDecimal.ZERO);
                result.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("current_month_plan_power", BigDecimal.ZERO);
            result.put("settlement_average_price", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 获取电站发电量信息
     */
    private StationDetailResponseDTO.PowerGenerationInfo getStationPowerGeneration(Long stationId, String date,
            String dimension) {

        // 检查是否需要全国汇总（通过电站所属省份判断）
        Station station = stationService.getById(stationId);
        if (station != null && station.getProvinceId() == 0) {
            // 全国汇总模式
            return multiDataSourceAggregationService.aggregateAllProvincesPowerGeneration(stationId, date, dimension);
        }

        // 单省份模式 - 根据维度返回不同的数据
        StationDetailResponseDTO.PowerGenerationInfo powerGeneration = new StationDetailResponseDTO.PowerGenerationInfo();
        powerGeneration.setDimension(dimension);
        powerGeneration.setQueryDate(date);

        if ("3".equals(dimension) || "1".equals(dimension)) {
            // 日维度：返回详细的时间点数据（96个点）
            // 年维度：返回详细的月份数据（12个月）
            List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = getDetailedPowerGenerationData(
                    stationId, date, dimension);
            powerGeneration.setDetailData(detailData);

            // 计算汇总值
            BigDecimal totalGeneration = detailData.stream()
                    .map(data -> BigDecimal.valueOf(data.getGeneration()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            powerGeneration.setCurrentPeriodGeneration(totalGeneration);
        } else {
            // 月度：只返回汇总值
            BigDecimal currentPeriodGeneration = calculateCurrentPeriodGenerationFromActualPower(stationId, date,
                    dimension);
            powerGeneration.setCurrentPeriodGeneration(currentPeriodGeneration);
            powerGeneration.setDetailData(null); // 月维度不返回详细数据
        }

        return powerGeneration;
    }

    /**
     * 获取详细的发电量数据（用于日维度）
     */
    private List<StationDetailResponseDTO.PowerGenerationDetailData> getDetailedPowerGenerationData(Long stationId,
            String date, String dimension) {
        List<StationDetailResponseDTO.PowerGenerationDetailData> detailData = new ArrayList<>();

        try {
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setProvinceId(1); // 在动态数据源上下文中，设置默认省份ID
            param.setTimeDimension(dimension);
            param.setQueryDate(date);

            // 获取功率预测数据
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData != null && !trendData.isEmpty()) {
                for (PowerGenerationTrendDto trendDto : trendData) {
                    StationDetailResponseDTO.PowerGenerationDetailData detail = new StationDetailResponseDTO.PowerGenerationDetailData();
                    detail.setTimeLabel(trendDto.getTimeLabel());
                    detail.setActualPower(trendDto.getActualPower());
                    // 发电量 = 实际功率 / 4
                    detail.setGeneration(trendDto.getActualPower() != null ? trendDto.getActualPower() / 4.0 : 0.0);
                    detailData.add(detail);
                }
            }

            log.info("获取详细发电量数据成功，电站ID: {}, 数据点数: {}", stationId, detailData.size());

        } catch (Exception e) {
            log.error("获取详细发电量数据失败，电站ID: {}, 错误: {}", stationId, e.getMessage(), e);
        }

        return detailData;
    }

    /**
     * 获取电站交易信息（重构版）
     */
    private StationDetailResponseDTO.TradeInfo getStationTradeInfo(Long stationId, String date, String dimension,
            String provinceId) {
        StationDetailResponseDTO.TradeInfo tradeInfo = new StationDetailResponseDTO.TradeInfo();
        tradeInfo.setDimension(dimension);

        switch (dimension) {
            case "3":
                // 日维度：省份ID为2时使用daily_clean表
                if ("2".equals(provinceId)) {
                    tradeInfo.setDataSource("daily_clean_tables");
                    tradeInfo.setPhotovoltaicWind(getPhotovoltaicWindTradeInfoFromDailyClean(stationId, date));
                    tradeInfo.setEnergyStorage(getEnergyStorageTradeInfoFromDailyClean(stationId, date));
                } else {
                    // 其他省份的日维度逻辑（如果需要）
                    tradeInfo.setDataSource("other_source");
                    tradeInfo.setPhotovoltaicWind(getDefaultPhotovoltaicWindInfo());
                    tradeInfo.setEnergyStorage(getDefaultEnergyStorageInfo());
                }
                break;

            case "2":
            case "1":
                // 年月维度：使用power_side_settle表
                tradeInfo.setDataSource("power_side_settle");
                tradeInfo.setPhotovoltaicWind(getPhotovoltaicWindTradeInfoFromSettle(stationId, date, dimension));
                tradeInfo.setEnergyStorage(getEnergyStorageTradeInfoFromSettle(stationId, date, dimension));
                break;

            default:
                throw new RuntimeException("不支持的维度参数：" + dimension + "，只支持 1(年)、2(月)、3(日)");
        }

        return tradeInfo;
    }

    /**
     * 从power_side_settle表获取光伏风电交易信息
     */
    private StationDetailResponseDTO.PhotovoltaicWindInfo getPhotovoltaicWindTradeInfoFromSettle(Long stationId,
            String date, String dimension) {
        StationDetailResponseDTO.PhotovoltaicWindInfo info = new StationDetailResponseDTO.PhotovoltaicWindInfo();

        // 获取电站类型（0-光伏, 1-风电）
        Station station = stationService.getById(stationId);
        if (station == null || (station.getType() != 0 && station.getType() != 1)) {
            return getDefaultPhotovoltaicWindInfo();
        }

        try {
            if ("1".equals(dimension)) {
                // 年度查询：默认展示当年，使用月统推发电侧结算单(type=3)
                String startDate = date + "-01-01";
                String endDate = date + "-12-31";
                String startYearMonth = date + "-01";
                String endYearMonth = date + "-12";

                Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                        stationId, startDate, endDate, startYearMonth, endYearMonth,
                        SettlementFileTypeEnum.SETTLE_MONTH_DETAILS);

                if (settlementData != null && !settlementData.isEmpty()) {
                    BigDecimal totalElectricity = settlementData.get("totalSettlementElectricity");
                    BigDecimal avgPrice = settlementData.get("avgTradePrice");

                    info.setYearlyTotalPower(totalElectricity);
                    info.setYearlyAvgPrice(avgPrice);
                }

            } else if ("2".equals(dimension)) {
                // 月度查询：使用月统推发电侧结算单(type=3)
                String startDate = date + "-01";
                String endDate = getMonthEndDate(date);
                String startYearMonth = date;
                String endYearMonth = date;

                Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                        stationId, startDate, endDate, startYearMonth, endYearMonth,
                        SettlementFileTypeEnum.SETTLE_MONTH_DETAILS);

                if (settlementData != null && !settlementData.isEmpty()) {
                    BigDecimal monthlyElectricity = settlementData.get("totalSettlementElectricity");
                    BigDecimal avgPrice = settlementData.get("avgTradePrice");

                    info.setMonthlyTotalPower(monthlyElectricity);
                    info.setMonthlyAvgPrice(avgPrice);
                }
            }

        } catch (Exception e) {
            log.warn("获取光伏风电交易信息失败: {}", e.getMessage());
            return getDefaultPhotovoltaicWindInfo();
        }

        return info;
    }

    /**
     * 从power_side_settle表获取储能交易信息
     */
    private StationDetailResponseDTO.EnergyStorageInfo getEnergyStorageTradeInfoFromSettle(Long stationId, String date,
            String dimension) {
        StationDetailResponseDTO.EnergyStorageInfo info = new StationDetailResponseDTO.EnergyStorageInfo();

        try {
            if ("1".equals(dimension)) {
                // 年度查询：默认展示当年，使用月统推独立储能结算单(type=4)
                String startDate = date + "-01-01";
                String endDate = date + "-12-31";
                String startYearMonth = date + "-01";
                String endYearMonth = date + "-12";

                Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                        stationId, startDate, endDate, startYearMonth, endYearMonth,
                        SettlementFileTypeEnum.SETTLE_MONTH_STORAGE_DETAILS);

                if (settlementData != null && !settlementData.isEmpty()) {
                    BigDecimal totalElectricity = settlementData.get("totalSettlementElectricity");
                    BigDecimal avgPrice = settlementData.get("avgTradePrice");

                    info.setYearlyTotalPower(totalElectricity);
                    info.setYearlyAvgPrice(avgPrice);
                }

            } else if ("2".equals(dimension)) {
                // 月度查询：修正startDate格式
                String startDate = date + "-01"; // 确保是完整的yyyy-MM-dd格式
                String endDate = getMonthEndDate(date);
                String startYearMonth = date;
                String endYearMonth = date;

                Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                        stationId, startDate, endDate, startYearMonth, endYearMonth,
                        SettlementFileTypeEnum.SETTLE_MONTH_STORAGE_DETAILS);

                if (settlementData != null && !settlementData.isEmpty()) {
                    BigDecimal monthlyElectricity = settlementData.get("totalSettlementElectricity");
                    BigDecimal avgPrice = settlementData.get("avgTradePrice");

                    info.setMonthlyTotalPower(monthlyElectricity);
                    info.setMonthlyAvgPrice(avgPrice);
                }
            }

        } catch (Exception e) {
            log.warn("获取储能交易信息失败: {}", e.getMessage());
            return getDefaultEnergyStorageInfo();
        }

        return info;
    }

    /**
     * 从daily_clean表获取光伏风电交易信息（日维度，省份ID=2）
     */
    private StationDetailResponseDTO.PhotovoltaicWindInfo getPhotovoltaicWindTradeInfoFromDailyClean(Long stationId,
            String date) {
        List<EnergyNewDailyClean> energyNewData = energyNewDailyCleanService.selectByDay(stationId, date);

        StationDetailResponseDTO.PhotovoltaicWindInfo info = new StationDetailResponseDTO.PhotovoltaicWindInfo();
        if (energyNewData != null && !energyNewData.isEmpty()) {
            BigDecimal totalPower = energyNewData.stream()
                    .map(EnergyNewDailyClean::getTotalPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalFee = energyNewData.stream()
                    .map(EnergyNewDailyClean::getTotalFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal midLongTermPower = energyNewData.stream()
                    .map(EnergyNewDailyClean::getMidLongTermPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal guaranteePower = energyNewData.stream()
                    .map(EnergyNewDailyClean::getGuaranteePower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal avgPrice = calculateAvgPrice(totalFee, totalPower);

            info.setTotalPower(totalPower);
            info.setTotalFee(totalFee);
            info.setSettlementAvgPrice(avgPrice);
            info.setMidLongTermPower(midLongTermPower);
            info.setGuaranteePower(guaranteePower);
        } else {
            info = getDefaultPhotovoltaicWindInfo();
        }

        return info;
    }

    /**
     * 从daily_clean表获取储能交易信息（日维度，省份ID=2）
     */
    private StationDetailResponseDTO.EnergyStorageInfo getEnergyStorageTradeInfoFromDailyClean(Long stationId,
            String date) {
        List<EnergyStorageDailyClean> energyStorageData = energyStorageDailyCleanService
                .selectByDay(stationId.toString(), date);

        StationDetailResponseDTO.EnergyStorageInfo info = new StationDetailResponseDTO.EnergyStorageInfo();
        if (energyStorageData != null && !energyStorageData.isEmpty()) {
            BigDecimal userTotalPower = energyStorageData.stream()
                    .map(EnergyStorageDailyClean::getUserTotalPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal userTotalFee = energyStorageData.stream()
                    .map(EnergyStorageDailyClean::getUserTotalFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal powerGenerationTotalPower = energyStorageData.stream()
                    .map(EnergyStorageDailyClean::getPowerGenerationTotalPower)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal powerGenerationTotalFee = energyStorageData.stream()
                    .map(EnergyStorageDailyClean::getPowerGenerationTotalFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            info.setUserTotalPower(userTotalPower);
            info.setUserTotalFee(userTotalFee);
            info.setPowerGenerationTotalPower(powerGenerationTotalPower);
            info.setPowerGenerationTotalFee(powerGenerationTotalFee);
        } else {
            info = getDefaultEnergyStorageInfo();
        }

        return info;
    }

    /**
     * 计算平均价格
     */
    private BigDecimal calculateAvgPrice(BigDecimal totalFee, BigDecimal totalElectricity) {
        if (totalElectricity == null || totalElectricity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        if (totalFee == null) {
            return BigDecimal.ZERO;
        }
        return totalFee.divide(totalElectricity, 4, RoundingMode.HALF_UP);
    }

    /**
     * 安全转换为BigDecimal
     */
    private BigDecimal convertToDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取默认光伏风电信息
     */
    private StationDetailResponseDTO.PhotovoltaicWindInfo getDefaultPhotovoltaicWindInfo() {
        StationDetailResponseDTO.PhotovoltaicWindInfo info = new StationDetailResponseDTO.PhotovoltaicWindInfo();
        info.setTotalPower(BigDecimal.ZERO);
        info.setTotalFee(BigDecimal.ZERO);
        info.setSettlementAvgPrice(BigDecimal.ZERO);
        info.setMidLongTermPower(BigDecimal.ZERO);
        info.setGuaranteePower(BigDecimal.ZERO);
        info.setYearlyTotalPower(BigDecimal.ZERO);
        info.setYearlyAvgPrice(BigDecimal.ZERO);
        info.setMonthlyTotalPower(BigDecimal.ZERO);
        info.setMonthlyAvgPrice(BigDecimal.ZERO);
        return info;
    }

    /**
     * 获取默认储能信息
     */
    private StationDetailResponseDTO.EnergyStorageInfo getDefaultEnergyStorageInfo() {
        StationDetailResponseDTO.EnergyStorageInfo info = new StationDetailResponseDTO.EnergyStorageInfo();
        info.setUserTotalPower(BigDecimal.ZERO);
        info.setUserTotalFee(BigDecimal.ZERO);
        info.setPowerGenerationTotalPower(BigDecimal.ZERO);
        info.setPowerGenerationTotalFee(BigDecimal.ZERO);
        info.setYearlyTotalPower(BigDecimal.ZERO);
        info.setYearlyAvgPrice(BigDecimal.ZERO);
        info.setMonthlyTotalPower(BigDecimal.ZERO);
        info.setMonthlyAvgPrice(BigDecimal.ZERO);
        return info;
    }

    /**
     * 计算累计发电量（功率预测实际功率求和除以4）
     * 确保与发电量信息使用相同的计算逻辑，返回12个月的完整数据
     */
    private BigDecimal calculateTotalPowerGeneration(Long stationId, String year) {
        try {
            // 使用PowerService获取年度累计发电量
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            LambdaQueryWrapper<Station> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Station::getId, stationId);
            Station station = stationService.getOne(queryWrapper);
            param.setProvinceId(station.getProvinceId());
            param.setTimeDimension("1"); // 使用年度维度，确保返回12个月数据
            param.setQueryDate(year);

            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData != null && !trendData.isEmpty()) {
                // 使用实际功率计算发电量：实际功率求和 ÷ 4
                double totalActualPower = trendData.stream()
                        .map(PowerGenerationTrendDto::getActualPower) // 使用实际功率字段
                        .filter(Objects::nonNull)
                        .reduce(0.0, Double::sum);

                // 实际功率除以4得到发电量
                return BigDecimal.valueOf(totalActualPower / 4.0);
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("计算累计发电量失败，返回0: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算当前周期发电量（保持原有逻辑用于兼容）
     */
    private BigDecimal calculateCurrentPeriodGeneration(Long stationId, String date, String dimension) {
        return calculateCurrentPeriodGenerationFromActualPower(stationId, date, dimension);
    }

    /**
     * 从功率预测实际功率计算当前周期发电量
     * 使用实际功率求和除以4得到发电量，确保返回完整时间序列数据
     */
    private BigDecimal calculateCurrentPeriodGenerationFromActualPower(Long stationId, String date, String dimension) {
        try {
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setProvinceId(1); // 在动态数据源上下文中，设置默认省份ID

            switch (dimension) {
                case "3":
                    param.setTimeDimension("3"); // 日度 - 应返回96个时间点
                    param.setQueryDate(date);
                    break;
                case "2":
                    param.setTimeDimension("2"); // 月度 - 应返回该月所有天数
                    param.setQueryDate(date.substring(0, 7)); // yyyy-MM格式
                    break;
                case "1":
                    param.setTimeDimension("1"); // 年度 - 应返回12个月
                    param.setQueryDate(date.substring(0, 4)); // yyyy格式
                    break;
                default:
                    throw new RuntimeException("不支持的维度参数：" + dimension + "，只支持 1(年)、2(月)、3(日)");
            }

            // 获取功率预测数据，确保返回完整时间序列
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData != null && !trendData.isEmpty()) {
                // 使用实际功率计算发电量：实际功率求和 ÷ 4
                double totalActualPower = trendData.stream()
                        .map(PowerGenerationTrendDto::getActualPower) // 使用实际功率字段
                        .filter(Objects::nonNull)
                        .reduce(0.0, Double::sum);

                // 实际功率除以4得到发电量
                return BigDecimal.valueOf(totalActualPower / 4.0);
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("从功率预测实际功率计算当前周期发电量失败，返回0: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    @ApiOperation(value = "获取新能源日清分数据", notes = "根据日期和电站ID获取新能源日清分数据，支持年/月/日查询")
    @GetMapping("/getEnergyNewDailyClean")
    public Result<List<EnergyNewDailyClean>> getEnergyNewDailyClean(
            @ApiParam(value = "电站ID", required = true) @RequestParam Long stationId,
            @ApiParam(value = "日期参数", required = true) @RequestParam String date,
            @ApiParam(value = "省份ID", required = true) @RequestParam Integer provinceId) {
        try {
            // 参数验证
            if (stationId == null || stationId <= 0) {
                return Result.error("电站ID不能为空且必须大于0");
            }
            if (provinceId == null) {
                return Result.error("省份ID不能为空");
            }
            if (date == null || date.trim().isEmpty()) {
                return Result.error("日期参数不能为空");
            }

            // 验证日期格式并确定查询类型
            String queryType;
            if (date.matches("\\d{4}")) {
                queryType = "year";
            } else if (date.matches("\\d{4}-\\d{2}")) {
                queryType = "month";
            } else if (date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                queryType = "day";
            } else {
                return Result.error("日期格式不正确，支持格式：yyyy（年）、yyyy-MM（月）、yyyy-MM-dd（日）");
            }

            log.info("查询新能源日清分数据 - 电站ID: {}, 省份ID: {}, 日期: {}, 查询类型: {}",
                    stationId, provinceId, date, queryType);

            // 检查是否为全国数据源汇总
            if (provinceId == 0) {
                // 全国数据源汇总模式 - 目前返回空列表，后续可以实现全国汇总
                log.info("全国数据源汇总模式，新能源日清分数据接口返回空数据");
                return Result.OK(new ArrayList<>());
            }

            // 单省份模式
            // 切换到对应省份的数据源
            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
            if (dsKey == null) {
                return Result.error("不支持的省份ID: " + provinceId);
            }

            log.info("数据源切换 - 省份ID: {}, 数据源Key: {}", provinceId, dsKey);
            DynamicDataSourceContextHolder.push(dsKey);

            // 记录当前数据源
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.info("当前数据源: {}", currentDataSource);

            // 验证电站是否属于该省份
            Station station = stationService.getById(stationId);
            if (station == null) {
                return Result.error("电站不存在");
            }
            if (!station.getProvinceId().equals(provinceId)) {
                return Result.error("电站不属于指定省份");
            }

            log.info("电站验证成功 - 电站名称: {}, 电站省份ID: {}", station.getName(), station.getProvinceId());

            // 根据查询类型获取数据
            List<EnergyNewDailyClean> result = null;
            try {
                switch (queryType) {
                    case "day":
                        result = energyNewDailyCleanService.selectByDay(stationId, date);
                        break;
                    case "month":
                        result = energyNewDailyCleanService.selectByMonth(stationId, date);
                        break;
                    case "year":
                        result = energyNewDailyCleanService.selectByYear(stationId, date);
                        break;
                    default:
                        return Result.error("查询类型错误");
                }
            } catch (Exception queryException) {
                log.error("数据库查询异常 - 电站ID: {}, 日期: {}, 查询类型: {}",
                         stationId, date, queryType, queryException);
                // 返回空列表而不是抛出异常
                result = new ArrayList<>();
            }

            // 确保result不为null
            if (result == null) {
                result = new ArrayList<>();
            }

            log.info("查询成功 - 返回记录数: {}", result.size());

            // 确保返回的列表不为空
            result = NullValueHandler.ensureNotNull(result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @ApiOperation(value = "储能", notes = "储能")
    @GetMapping("/getEnergyStorageDailyClean")
    public Result<List<EnergyStorageDailyClean>> getEnergyStorageDailyClean(
            @RequestParam String provinceId,
            @RequestParam String stationId,
            @RequestParam String date,
            @RequestParam(defaultValue = "day") String dimension) {
        try {
            // 检查是否为全国数据源汇总
            if ("0".equals(provinceId)) {
                // 全国数据源汇总模式 - 目前返回空列表，后续可以实现全国汇总
                log.info("全国数据源汇总模式，储能数据接口返回空数据");
                return Result.OK(new ArrayList<>());
            }

            String dsKey = ProvinceDataSourceUtil.getDataSourceKey(Integer.valueOf(provinceId));
            if (dsKey == null) {
                return Result.error("不支持的省份ID");
            }

            DynamicDataSourceContextHolder.push(dsKey);

            List<EnergyStorageDailyClean> result;

            switch (dimension.toLowerCase()) {
                case "day":
                    // 日期格式校验 yyyy-MM-dd
                    if (!date.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        return Result.error("日查询日期格式错误，应为：yyyy-MM-dd");
                    }
                    result = energyStorageDailyCleanService.selectByDay(stationId, date);
                    break;

                case "month":
                    // 日期格式校验 yyyy-MM
                    if (!date.matches("\\d{4}-\\d{2}")) {
                        return Result.error("月查询日期格式错误，应为：yyyy-MM");
                    }
                    result = energyStorageDailyCleanService.selectByMonth(stationId, date);
                    break;

                case "year":
                    // 日期格式校验 yyyy
                    if (!date.matches("\\d{4}")) {
                        return Result.error("年查询日期格式错误，应为：yyyy");
                    }
                    result = energyStorageDailyCleanService.selectByYear(stationId, date);
                    break;

                default:
                    return Result.error("维度参数错误，支持：day、month、year");
            }

            // 确保返回的列表不为空
            result = NullValueHandler.ensureNotNull(result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("查询储能日清洁数据失败", e);
            return Result.error("查询失败：" + e.getMessage());
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 构建包含结算数据的电站信息
     * 
     * @param station 电站基础信息
     * @param year    年份
     * @param month   月份
     * @return 包含结算数据的电站信息
     */
    private Map<String, Object> buildStationWithSettlementData(Station station, String year, String month) {
        Map<String, Object> result = new HashMap<>();
        result.put("stationInfo", station);

        try {
            LambdaQueryWrapper<ScreenTradeSettlement> settlementWrapper = new LambdaQueryWrapper<>();
            settlementWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                    .eq(ScreenTradeSettlement::getYear, year)
                    .eq(ScreenTradeSettlement::getMonth, String.format("%02d", Integer.parseInt(month)));

            LambdaQueryWrapper<YearlyPowerPlan> yearlyWrapper = new LambdaQueryWrapper<>();
            yearlyWrapper.eq(YearlyPowerPlan::getStationId, station.getId())
                    .eq(YearlyPowerPlan::getYear, year)
                    .eq(YearlyPowerPlan::getMonth, String.format("%02d", Integer.parseInt(month)));

            ScreenTradeSettlement settlement = screenTradeSettlementService.getOne(settlementWrapper);
            YearlyPowerPlan yearly = yearlyPowerPlanService.getOne(yearlyWrapper);

            log.debug("电站{}查询结果 - settlement: {}, yearly: {}", station.getId(),
                     settlement != null ? "存在" : "不存在",
                     yearly != null ? "存在" : "不存在");

            if (settlement != null) {
                // 当月实际发电量 - 使用与累计发电量相同的计算逻辑（功率预测实际功率求和除以4）
                BigDecimal currentMonthPower = calculateCurrentPeriodGeneration(station.getId(),
                        year + "-" + String.format("%02d", Integer.parseInt(month)), "2");
                result.put("current_month_power", currentMonthPower);

                // 当月计划发电量
                BigDecimal currentMonthPlanPower = yearly != null && yearly.getPlanValue() != null
                        ? yearly.getPlanValue()
                        : BigDecimal.ZERO;
                result.put("current_month_plan_power", currentMonthPlanPower);

                // 结算均价
                BigDecimal settlementAveragePrice = settlement.getSettlementAveragePrice() != null
                        ? settlement.getSettlementAveragePrice()
                        : BigDecimal.ZERO;
                result.put("settlement_average_price", settlementAveragePrice);
            } else {
                // 没有结算数据时的默认值
                result.put("current_month_power", BigDecimal.ZERO);
                result.put("current_month_plan_power", BigDecimal.ZERO);
                result.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            // 异常时设置默认值
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("current_month_plan_power", BigDecimal.ZERO);
            result.put("settlement_average_price", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 获取月份的最后一天日期
     * 
     * @param yearMonth 格式：yyyy-MM
     * @return 格式：yyyy-MM-dd
     */
    private String getMonthEndDate(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 计算该月的最后一天
            int lastDay;
            switch (month) {
                case 2:
                    // 判断闰年
                    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                        lastDay = 29;
                    } else {
                        lastDay = 28;
                    }
                    break;
                case 4:
                case 6:
                case 9:
                case 11:
                    lastDay = 30;
                    break;
                default:
                    lastDay = 31;
                    break;
            }

            return String.format("%04d-%02d-%02d", year, month, lastDay);
        } catch (Exception e) {
            log.warn("解析月份日期失败: {}", yearMonth, e);
            return yearMonth + "-31"; // 默认返回31号
        }
    }

    /**
     * 确保电站详情响应数据不为空
     */
    private void ensureStationDetailNotNull(StationDetailResponseDTO response) {
        if (response == null) {
            return;
        }

        // 确保基础信息不为空
        if (response.getBasicInfo() == null) {
            response.setBasicInfo(new StationDetailResponseDTO.BasicInfo());
        }
        StationDetailResponseDTO.BasicInfo basicInfo = response.getBasicInfo();
        if (basicInfo.getStationId() == null) basicInfo.setStationId(0L);
        if (basicInfo.getStationName() == null) basicInfo.setStationName("");
        if (basicInfo.getCapacity() == null) basicInfo.setCapacity(BigDecimal.ZERO);
        if (basicInfo.getTotalPowerGeneration() == null) basicInfo.setTotalPowerGeneration(BigDecimal.ZERO);
        if (basicInfo.getCurrentMonthPower() == null) basicInfo.setCurrentMonthPower(BigDecimal.ZERO);
        if (basicInfo.getCurrentMonthPlanPower() == null) basicInfo.setCurrentMonthPlanPower(BigDecimal.ZERO);
        if (basicInfo.getSettlementAveragePrice() == null) basicInfo.setSettlementAveragePrice(BigDecimal.ZERO);

        // 确保发电量信息不为空
        if (response.getPowerGeneration() == null) {
            response.setPowerGeneration(new StationDetailResponseDTO.PowerGenerationInfo());
        }
        StationDetailResponseDTO.PowerGenerationInfo powerGeneration = response.getPowerGeneration();
        if (powerGeneration.getCurrentPeriodGeneration() == null) powerGeneration.setCurrentPeriodGeneration(BigDecimal.ZERO);
        if (powerGeneration.getDetailData() == null) powerGeneration.setDetailData(new ArrayList<>());

        // 确保交易信息不为空
        if (response.getTradeInfo() == null) {
            response.setTradeInfo(new StationDetailResponseDTO.TradeInfo());
        }
        StationDetailResponseDTO.TradeInfo tradeInfo = response.getTradeInfo();
        if (tradeInfo.getDataSource() == null) tradeInfo.setDataSource("");

        // 确保光伏风电信息不为空
        if (tradeInfo.getPhotovoltaicWind() == null) {
            tradeInfo.setPhotovoltaicWind(new StationDetailResponseDTO.TradeDetailInfo());
        }
        ensureTradeDetailInfoNotNull(tradeInfo.getPhotovoltaicWind());

        // 确保储能信息不为空
        if (tradeInfo.getEnergyStorage() == null) {
            tradeInfo.setEnergyStorage(new StationDetailResponseDTO.TradeDetailInfo());
        }
        ensureTradeDetailInfoNotNull(tradeInfo.getEnergyStorage());
    }

    /**
     * 确保交易详情信息不为空
     */
    private void ensureTradeDetailInfoNotNull(StationDetailResponseDTO.TradeDetailInfo info) {
        if (info == null) {
            return;
        }

        if (info.getTotalPower() == null) info.setTotalPower(BigDecimal.ZERO);
        if (info.getTotalFee() == null) info.setTotalFee(BigDecimal.ZERO);
        if (info.getSettlementAvgPrice() == null) info.setSettlementAvgPrice(BigDecimal.ZERO);
        if (info.getMidLongTermPower() == null) info.setMidLongTermPower(BigDecimal.ZERO);
        if (info.getGuaranteePower() == null) info.setGuaranteePower(BigDecimal.ZERO);
        if (info.getYearlyTotalPower() == null) info.setYearlyTotalPower(BigDecimal.ZERO);
        if (info.getYearlyAvgPrice() == null) info.setYearlyAvgPrice(BigDecimal.ZERO);
        if (info.getMonthlyTotalPower() == null) info.setMonthlyTotalPower(BigDecimal.ZERO);
        if (info.getMonthlyAvgPrice() == null) info.setMonthlyAvgPrice(BigDecimal.ZERO);
    }
}