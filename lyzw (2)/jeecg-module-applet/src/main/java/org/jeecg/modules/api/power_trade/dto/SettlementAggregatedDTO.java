package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("聚合结算数据 DTO")
public class SettlementAggregatedDTO {

    @ApiModelProperty(value = "结算电量(GWh)", example = "12.34")
    private Double settlementPower;

    @ApiModelProperty(value = "结算电量单位", example = "GWh")
    private String settlementPowerUnit;

    @ApiModelProperty(value = "结算均价(元/MWh)", example = "420.56")
    private Double settlementAvgPrice;

    @ApiModelProperty(value = "结算均价单位", example = "元/MWh")
    private String settlementAvgPriceUnit;

    @ApiModelProperty(value = "标杆电价(元/MWh)", example = "350.78")
    private Double coalBenchmarkPrice;

    @ApiModelProperty(value = "标杆电价单位", example = "元/MWh")
    private String coalBenchmarkPriceUnit;

    @ApiModelProperty(value = "请求中包含的电站总数", example = "5")
    private Integer totalStations;

    @ApiModelProperty(value = "实际有结算数据的电站数", example = "4")
    private Integer validSettlements;

    @ApiModelProperty(value = "查询的年份", example = "2025")
    private String queryYear;

    @ApiModelProperty(value = "查询的月份", example = "07")
    private String queryMonth;
}
