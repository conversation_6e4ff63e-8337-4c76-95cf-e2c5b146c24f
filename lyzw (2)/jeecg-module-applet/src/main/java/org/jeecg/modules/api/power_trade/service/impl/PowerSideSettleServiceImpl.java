package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;
import org.jeecg.modules.api.power_trade.enums.SettlementFileTypeEnum;
import org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper;
import org.jeecg.modules.api.power_trade.service.PowerSideSettleService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class PowerSideSettleServiceImpl extends ServiceImpl<PowerSideSettleMapper, PowerSideSettle> implements PowerSideSettleService {

    @Override
    public Map<String, BigDecimal> getStationSettlementSummary(Long stationId, String startDate, String endDate,
                                                               String startYearMonth, String endYearMonth, SettlementFileTypeEnum fileType) {
        return baseMapper.getStationSettlementSummary(stationId, startDate, endDate, startYearMonth, endYearMonth, fileType.getCode());
    }

    @Override
    public List<Map<String, Object>> getProvinceStationSettlementSummary(Integer provinceId, String startDate, String endDate,
                                                                         String startYearMonth, String endYearMonth, SettlementFileTypeEnum fileType) {
        return baseMapper.getProvinceStationSettlementSummary(provinceId, startDate, endDate, startYearMonth, endYearMonth, fileType.getCode());
    }

    @Override
    public Map<String, Object> getStationYearlyTradingInfo(Long stationId, String year) {
        // 验证年份格式
        if (!year.matches("\\d{4}")) {
            throw new IllegalArgumentException("年份格式不正确，请使用yyyy格式");
        }

        // 获取按月分解的数据
        log.info("查询年度交易信息 - 电站ID: {}, 年份: {}", stationId, year);
        List<Map<String, Object>> monthlyDataList = baseMapper.getStationYearlyTradingInfo(stationId, year);
        log.info("查询结果 - 返回记录数: {}", monthlyDataList != null ? monthlyDataList.size() : 0);

        if (monthlyDataList != null && !monthlyDataList.isEmpty()) {
            log.info("查询到的月度数据: {}", monthlyDataList);
        } else {
            log.warn("未查询到任何月度交易数据 - 电站ID: {}, 年份: {}", stationId, year);

            // 执行调试查询
            try {
                List<Map<String, Object>> debugData = baseMapper.debugStationDataRelation(stationId, year);
                log.info("调试查询结果: {}", debugData);
            } catch (Exception e) {
                log.error("调试查询失败: {}", e.getMessage());
            }
        }

        // 计算年度汇总数据
        BigDecimal yearlyTotalElectricity = BigDecimal.ZERO;
        BigDecimal yearlyTotalFee = BigDecimal.ZERO;

        List<String> months = new ArrayList<>();
        List<BigDecimal> electricityList = new ArrayList<>();
        List<BigDecimal> averagePriceList = new ArrayList<>();

        if (monthlyDataList != null) {
            for (Map<String, Object> monthData : monthlyDataList) {
                BigDecimal monthlyElectricity = (BigDecimal) monthData.get("monthlyElectricity");
                BigDecimal monthlyFee = (BigDecimal) monthData.get("monthlyTotalFee");
                BigDecimal monthlyPrice = (BigDecimal) monthData.get("monthlyAveragePrice");
                String month = (String) monthData.get("month");

                yearlyTotalElectricity = yearlyTotalElectricity.add(monthlyElectricity);
                yearlyTotalFee = yearlyTotalFee.add(monthlyFee);

                months.add(month);
                electricityList.add(monthlyElectricity);
                averagePriceList.add(monthlyPrice);
            }
        }

        // 计算年度平均价格
        BigDecimal yearlyAveragePrice = BigDecimal.ZERO;
        if (yearlyTotalElectricity.compareTo(BigDecimal.ZERO) > 0) {
            yearlyAveragePrice = yearlyTotalFee.divide(yearlyTotalElectricity, 6, BigDecimal.ROUND_HALF_UP);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("year", year);
        result.put("yearlyTotalElectricity", yearlyTotalElectricity);
        result.put("yearlyAveragePrice", yearlyAveragePrice);
        result.put("yearlyTotalFee", yearlyTotalFee);
        result.put("monthlyData", monthlyDataList);

        // 构建图表数据
        Map<String, Object> chartData = new HashMap<>();
        chartData.put("months", months);
        chartData.put("electricity", electricityList);
        chartData.put("averagePrice", averagePriceList);
        result.put("chartData", chartData);

        return result;
    }

    @Override
    public Map<String, Object> getStationMonthlyTradingInfo(Long stationId, String yearMonth) {
        // 验证年月格式
        if (!yearMonth.matches("\\d{4}-\\d{2}")) {
            throw new IllegalArgumentException("年月格式不正确，请使用yyyy-MM格式");
        }

        return baseMapper.getStationMonthlyTradingInfo(stationId, yearMonth);
    }
}