package org.jeecg.modules.api.coze.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.modules.api.coze.service.CozeService;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Service
public class CozeServiceImpl implements CozeService {

    private static final String BASE_URL = "https://api.coze.cn/v1/";

    public String cozeSpeechToText(byte[] audioBytes, String filename,String token) throws Exception {
        // 根据官方文档修正API端点
        String apiUrl = BASE_URL + "audio/transcriptions";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl);

            // 设置请求头
            httpPost.setHeader("Authorization", "Bearer " + token);

            // 构建multipart请求体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.addPart("file", new ByteArrayBody(audioBytes, filename));

            HttpEntity multipart = (HttpEntity) builder.build();
            httpPost.setEntity(multipart);

            log.info("正在调用Coze语音转文本API，文件名: {}, 大小: {} bytes", filename, audioBytes.length);

            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("语音转文本API调用成功，响应: {}", responseBody);

                // 解析响应JSON
                JSONObject jsonResponse = JSONObject.parseObject(responseBody);

                // 检查响应是否成功
                if (jsonResponse.containsKey("code") && jsonResponse.getIntValue("code") == 0) {
                    // 提取文本内容
                    if (jsonResponse.containsKey("data")) {
                        JSONObject data = jsonResponse.getJSONObject("data");
                        if (data.containsKey("text")) {
                            String recognizedText = data.getString("text");
                            log.info("语音识别成功，识别文本: {}", recognizedText);
                            return recognizedText;
                        }
                    }

                    // 如果直接包含text字段
                    if (jsonResponse.containsKey("text")) {
                        String recognizedText = jsonResponse.getString("text");
                        log.info("语音识别成功，识别文本: {}", recognizedText);
                        return recognizedText;
                    }

                    log.error("响应中未找到文本内容，响应: {}", responseBody);
                    throw new Exception("响应中未找到文本内容");

                } else {
                    String errorMsg = jsonResponse.containsKey("msg") ?
                            jsonResponse.getString("msg") : "未知错误";
                    log.error("语音转文本API返回错误，错误信息: {}", errorMsg);
                    throw new Exception("语音转文本失败: " + errorMsg);
                }

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ?
                        EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : "";
                log.error("语音转文本API调用失败，状态码: {}, 错误信息: {}", statusCode, errorBody);
                throw new Exception("语音转文本API调用失败，状态码: " + statusCode);
            }

        } catch (IOException e) {
            log.error("语音转文本API调用异常: {}", e.getMessage(), e);
            throw new Exception("语音转文本API调用异常: " + e.getMessage(), e);
        }
    }
}