package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class StationDetailResponseDTO {
    
    @ApiModelProperty("基础信息")
    private BasicInfo basicInfo;
    
    @ApiModelProperty("发电量信息")
    private PowerGenerationInfo powerGeneration;
    
    @ApiModelProperty("交易信息")
    private TradeInfo tradeInfo;
    
    @Data
    public static class BasicInfo {
        @ApiModelProperty("电站类型")
        private String stationType;
        
        @ApiModelProperty("电站名称")
        private String stationName;
        
        @ApiModelProperty("电站容量(MW)")
        private BigDecimal capacity;

        @ApiModelProperty("省份id")
        private Integer provinceId;

        @ApiModelProperty("省份名称")
        private String provinceName;

        @ApiModelProperty("年计划发电量(MWh)")
        private BigDecimal yearPlanPowerGeneration;
        
        @ApiModelProperty("累计发电量(MWh)")
        private BigDecimal totalPowerGeneration;
        
        // 从电站列表接口获取的结算数据
        @ApiModelProperty("当月实际发电量")
        private BigDecimal currentMonthPower;
        
        @ApiModelProperty("当月计划发电量")
        private BigDecimal currentMonthPlanPower;
        
        @ApiModelProperty("结算均价")
        private BigDecimal settlementAveragePrice;
    }
    
    @Data
    public static class PowerGenerationInfo {
        @ApiModelProperty("当前查询周期发电量(MWh) - 汇总值")
        private BigDecimal currentPeriodGeneration;

        @ApiModelProperty("详细发电量数据 - 日维度时返回时间点数据，其他维度为空")
        private List<PowerGenerationDetailData> detailData;

        @ApiModelProperty("查询维度")
        private String dimension;

        @ApiModelProperty("查询日期")
        private String queryDate;
    }

    @Data
    public static class PowerGenerationDetailData {
        @ApiModelProperty("时间标签")
        private String timeLabel;

        @ApiModelProperty("实际功率(MW)")
        private Double actualPower;

        @ApiModelProperty("发电量(MWh) - 实际功率除以4")
        private Double generation;
    }
    
    @Data
    public static class TradeInfo {
        @ApiModelProperty("光伏和风电交易信息")
        private PhotovoltaicWindInfo photovoltaicWind;
        
        @ApiModelProperty("储能交易信息")
        private EnergyStorageInfo energyStorage;
    
        @ApiModelProperty("查询维度")
        private String dimension;
    
        @ApiModelProperty("数据来源")
        private String dataSource;
    }
    
    @Data
    public static class PhotovoltaicWindInfo {
        // 日维度字段（来自energy_new_daily_clean）
        @ApiModelProperty("总上网电量(万千瓦时)")
        private BigDecimal totalPower;
        
        @ApiModelProperty("总电费(万元)")
        private BigDecimal totalFee;
        
        @ApiModelProperty("日结算均价(元/千瓦时)")
        private BigDecimal settlementAvgPrice;
        
        @ApiModelProperty("中长期合约电量(万千瓦时)")
        private BigDecimal midLongTermPower;
        
        @ApiModelProperty("保障电量(万千瓦时)")
        private BigDecimal guaranteePower;
        
        // 年月维度字段（来自power_side_settle）
        @ApiModelProperty("年度交易总电量(MWh)")
        private BigDecimal yearlyTotalPower;
        
        @ApiModelProperty("年度交易均价(元/MWh)")
        private BigDecimal yearlyAvgPrice;
        
        @ApiModelProperty("月度交易电量(MWh)")
        private BigDecimal monthlyTotalPower;
        
        @ApiModelProperty("月度交易均价(元/MWh)")
        private BigDecimal monthlyAvgPrice;
    }
    
    @Data
    public static class EnergyStorageInfo {
        // 日维度字段（来自energy_storage_daily_clean）
        @ApiModelProperty("用户用电总电量(万千瓦时)")
        private BigDecimal userTotalPower;
        
        @ApiModelProperty("用户用电总费用(万元)")
        private BigDecimal userTotalFee;
        
        @ApiModelProperty("发电总电量(万千瓦时)")
        private BigDecimal powerGenerationTotalPower;
        
        @ApiModelProperty("发电总费用(万元)")
        private BigDecimal powerGenerationTotalFee;
        
        // 年月维度字段（来自power_side_settle）
        @ApiModelProperty("年度交易总电量(MWh)")
        private BigDecimal yearlyTotalPower;
        
        @ApiModelProperty("年度交易均价(元/MWh)")
        private BigDecimal yearlyAvgPrice;
        
        @ApiModelProperty("月度交易电量(MWh)")
        private BigDecimal monthlyTotalPower;
        
        @ApiModelProperty("月度交易均价(元/MWh)")
        private BigDecimal monthlyAvgPrice;
    }
}