package org.jeecg.modules.api.power_trade.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 交易日历记录服务接口
 */
public interface ITradeCalendarRecordService extends IService<TradeCalendarRecord> {

    /**
     * 根据日期范围查询交易日历记录
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param provinceId 省份ID
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> getCalendarByDateRange(Date startDate, Date endDate, Integer provinceId);

    /**
     * 查询交易日历记录（关联标的日和交易类型）- 适用于安徽省
     * @param date 日期
     * @param provinceId 省份ID
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> getCalendarWithTargetDateAndTradeType(String date, Integer provinceId);
    
    /**
     * 查询交易日历记录（仅关联交易类型）- 适用于江苏省
     * @param date 日期
     * @param provinceId 省份ID
     * @return 交易日历记录列表
     */
    List<TradeCalendarRecord> getCalendarWithTradeType(String date, Integer provinceId);
}