package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 日历查询参数类
 */
@Data
public class CalendarQueryParam {

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 开始日期 (yyyy-MM-dd)
     */
    private String startDate;

    /**
     * 结束日期 (yyyy-MM-dd)
     */
    private String endDate;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 31; // 默认最多一个月
} 