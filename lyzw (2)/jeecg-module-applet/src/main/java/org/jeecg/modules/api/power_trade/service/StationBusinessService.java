package org.jeecg.modules.api.power_trade.service;

import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;

import java.util.List;
import java.util.Map;

/**
 * 电站业务服务接口
 * 负责处理电站相关的复杂业务逻辑，从StationController中迁移而来
 * 
 * <AUTHOR> (架构师)
 * @date 2025-07-27
 */
public interface StationBusinessService {

    /**
     * 获取电站列表（包含结算数据）
     * 处理分页查询、数据源切换、结算数据聚合等复杂逻辑
     * 
     * @param pageNo 页码，默认1
     * @param pageSize 每页条数，默认10
     * @param provinceId 省份ID (0-全国, 1-江苏, 2-安徽)
     * @param year 年份，用于结算数据查询
     * @param month 月份，用于结算数据查询
     * @param name 电站名称搜索关键词，可选
     * @return 分页电站列表数据，包含total、records等分页信息
     * @throws StationBusinessException 当省份ID不支持或查询失败时
     */
    Map<String, Object> getStationList(Integer pageNo, Integer pageSize, Integer provinceId, 
                                       String year, String month, String name);

    /**
     * 获取电站详情信息
     * 处理基础信息获取、发电量计算、交易信息聚合等复杂逻辑
     * 
     * @param id 电站ID
     * @param provinceId 省份ID，用于数据源切换
     * @param date 查询日期，格式根据dimension确定
     * @param dimension 时间维度 (1-年, 2-月, 3-日)
     * @return 电站详情响应数据，包含基础信息、发电量信息、交易信息
     * @throws StationBusinessException 当电站不存在或省份不匹配时
     */
    StationDetailResponseDTO getStationDetail(Long id, String provinceId, String date, String dimension);

    /**
     * 获取电站年度交易电量信息
     * 处理电站归属验证、年度交易数据查询和组装逻辑
     * 
     * @param id 电站ID
     * @param provinceId 省份ID，用于数据源切换和归属验证
     * @param year 年份，格式yyyy
     * @return 年度交易电量信息，包含月度分解数据
     * @throws StationBusinessException 当电站不存在或不属于指定省份时
     */
    Map<String, Object> getStationYearlyTradingInfo(Long id, Integer provinceId, String year);

    /**
     * 获取电站月度交易电量信息
     * 处理电站归属验证、月度交易数据查询和默认值处理逻辑
     * 
     * @param id 电站ID
     * @param provinceId 省份ID，用于数据源切换和归属验证
     * @param yearMonth 年月，格式yyyy-MM
     * @return 月度交易电量信息，包含总电量、均价、费用等
     * @throws StationBusinessException 当电站不存在或不属于指定省份时
     */
    Map<String, Object> getStationMonthlyTradingInfo(Long id, Integer provinceId, String yearMonth);

    /**
     * 获取新能源日清分数据
     * 处理日期格式验证、查询类型判断、电站归属验证等逻辑
     * 
     * @param stationId 电站ID
     * @param date 日期参数，支持yyyy、yyyy-MM、yyyy-MM-dd格式
     * @param provinceId 省份ID，用于数据源切换和归属验证
     * @return 新能源日清分数据列表，按查询类型返回对应维度数据
     * @throws StationBusinessException 当日期格式错误或电站不属于指定省份时
     */
    List<EnergyNewDailyClean> getEnergyNewDailyClean(Long stationId, String date, Integer provinceId);

    /**
     * 获取储能数据
     * 处理维度参数验证、日期格式验证、数据查询等逻辑
     * 
     * @param provinceId 省份ID，用于数据源切换
     * @param stationId 电站ID
     * @param date 日期，格式根据dimension确定
     * @param dimension 维度参数 (day/month/year)
     * @return 储能数据列表，按维度返回对应数据
     * @throws StationBusinessException 当维度参数错误或日期格式不正确时
     */
    List<EnergyStorageDailyClean> getEnergyStorageDailyClean(String provinceId, String stationId, 
                                                             String date, String dimension);

    /**
     * 电站业务异常类
     * 用于Service层抛出具体的业务异常
     */
    class StationBusinessException extends RuntimeException {
        public StationBusinessException(String message) {
            super(message);
        }
        
        public StationBusinessException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
