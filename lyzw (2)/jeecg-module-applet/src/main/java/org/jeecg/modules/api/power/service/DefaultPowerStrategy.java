package org.jeecg.modules.api.power.service;

import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerQueryParam;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认功率预测策略实现
 */
public class DefaultPowerStrategy implements PowerStrategy {
    
    @Override
    public Integer getSupportedFactoryId() {
        return 0; // 默认厂家ID
    }
    
    @Override
    public List<CommonPowerDto> queryPowerPredictionByRange(PowerQueryParam param) {
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerPredictionByDateList(PowerQueryParam param) {
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerRealByRange(PowerQueryParam param) {
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerRealByRangeDateList(PowerQueryParam param) {
        return new ArrayList<>();
    }
    
    @Override
    public String queryMaxVersion(PowerQueryParam param) {
        return "1.0";
    }
} 