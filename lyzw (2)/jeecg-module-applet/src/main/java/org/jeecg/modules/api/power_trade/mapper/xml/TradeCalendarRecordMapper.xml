<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.TradeCalendarRecordMapper">

    <!-- 根据日期范围查询交易日历记录 -->
    <select id="selectCalendarByDateRange" resultType="org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord">
        SELECT * FROM trade_calendar_record
        WHERE trade_date BETWEEN #{startDate} AND #{endDate}
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        ORDER BY trade_date
    </select>

    <!-- 根据日期查询交易日历记录 -->
    <select id="selectCalendarByDate" resultType="org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord">
        SELECT * FROM trade_calendar_record
        WHERE trade_date = #{tradeDate}
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        ORDER BY trade_date
    </select>

    <!-- 根据月份和省份ID高效查询交易日历记录 -->
    <select id="selectCalendarByMonthAndProvince" resultType="org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord">
        SELECT
            tcr.*,
            nt.type_name,
            p.province_name
        FROM trade_calendar_record tcr
        LEFT JOIN notice_type nt ON tcr.type_id = nt.id
        LEFT JOIN province p ON tcr.province_id = p.id
        WHERE tcr.trade_date BETWEEN #{startDate} AND #{endDate}
        <if test="provinceId != null">
            AND tcr.province_id = #{provinceId}
        </if>
        ORDER BY tcr.trade_date
    </select>

    <!-- 查询交易日历记录（关联标的日和交易类型）- 适用于安徽省 -->
    <select id="selectCalendarWithTargetDateAndTradeType" resultType="org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord">
        SELECT
            tcr.*,
            tt.label as tradeType,
            td.target_date
        FROM trade_calendar_record tcr
        LEFT JOIN trade_type tt ON tcr.type_id = tt.id
        LEFT JOIN trade_diary td ON DATE(tcr.trade_date) = DATE(td.target_date)
        WHERE 1=1
        <if test="date != null and date != ''">
            AND DATE(tcr.trade_date) = DATE(#{date})
        </if>
        ORDER BY tcr.trade_date DESC
    </select>

    <!-- 查询交易日历记录（仅关联交易类型）- 适用于江苏省 -->
    <select id="selectCalendarWithTradeType" resultType="org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord">
        SELECT
            tcr.*,
            tt.label as tradeType
        FROM trade_calendar_record tcr
        LEFT JOIN trade_type tt ON tcr.type_id = tt.id
        WHERE 1=1
        <if test="date != null and date != ''">
            AND DATE(tcr.trade_date) = DATE(#{date})
        </if>
        ORDER BY tcr.trade_date DESC
    </select>
</mapper>