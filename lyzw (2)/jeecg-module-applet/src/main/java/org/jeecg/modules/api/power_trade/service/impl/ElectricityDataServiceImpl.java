package org.jeecg.modules.api.power_trade.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.dto.ElectricityDataDTO;
import org.jeecg.modules.api.power_trade.mapper.ElectricityDataMapper;
import org.jeecg.modules.api.power_trade.service.IElectricityDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class ElectricityDataServiceImpl implements IElectricityDataService {

    @Autowired
    private ElectricityDataMapper electricityDataMapper;

    @Override
    public Object getElectricityDataByProvince(Integer provinceId, Long stationId, String date, String dimension) {
        log.info("ElectricityDataService查询开始 - 省份ID: {}, 电站ID: {}, 日期: {}, 维度: {}",
                provinceId, stationId, date, dimension);

        // 详细参数日志
        log.info("参数详情 - provinceId: [{}] (类型: {}), stationId: [{}] (类型: {}), date: [{}] (类型: {}), dimension: [{}] (类型: {})",
                provinceId, provinceId != null ? provinceId.getClass().getSimpleName() : "null",
                stationId, stationId != null ? stationId.getClass().getSimpleName() : "null", 
                date, date != null ? date.getClass().getSimpleName() : "null",
                dimension, dimension != null ? dimension.getClass().getSimpleName() : "null");

        // 特别检查dimension参数
        if (dimension != null) {
            log.info("维度参数分析 - 原始值: [{}], 长度: {}, 等于'3': {}, 等于'2': {}, 等于'1': {}",
                    dimension, dimension.length(), 
                    "3".equals(dimension), "2".equals(dimension), "1".equals(dimension));
            
            // 检查是否有空白字符
            if (!dimension.equals(dimension.trim())) {
                log.warn("维度参数包含空白字符! 原始: [{}], trim后: [{}]", dimension, dimension.trim());
            }
        }

        try {
            // 对于日度维度（数字格式3），直接返回详细数据
            if ("3".equals(dimension)) {
                log.info("执行日度详细数据查询 - 调用selectElectricityDataByProvince");
                Object result = electricityDataMapper.selectElectricityDataByProvince(provinceId, stationId, date, dimension);

                // 检查结果并生成默认数据
                if (result instanceof List) {
                    List<?> list = (List<?>) result;
                    log.info("日度查询完成 - 返回列表，大小: {}", list.size());
                    
                    if (list.isEmpty()) {
                        log.info("日度查询无数据，生成默认数据");
                        return createDefaultElectricityData(provinceId, stationId, date, dimension);
                    }
                    
                    if (!list.isEmpty()) {
                        log.info("第一条数据: {}", list.get(0));
                    }
                } else {
                    log.info("日度查询完成 - 结果类型: {}, 值: {}",
                            result != null ? result.getClass().getSimpleName() : "null", result);
                    
                    if (result == null) {
                        log.info("日度查询结果为null，生成默认数据");
                        return createDefaultElectricityData(provinceId, stationId, date, dimension);
                    }
                }
                return result;
            }

            // 对于月度和年度维度（数字格式2和1），返回分组统计数据
            if ("2".equals(dimension) || "1".equals(dimension)) {
                log.info("执行{}维度分组统计查询 - 调用selectElectricityDataGrouped", "2".equals(dimension) ? "月度" : "年度");
                Object result = electricityDataMapper.selectElectricityDataGrouped(provinceId, stationId, date, dimension);

                // 检查结果并生成默认数据
                if (result instanceof List) {
                    List<?> list = (List<?>) result;
                    log.info("分组统计查询完成 - 返回列表，大小: {}", list.size());
                    
                    if (list.isEmpty()) {
                        log.info("{}维度查询无数据，生成默认统计数据", "2".equals(dimension) ? "月度" : "年度");
                        return createDefaultElectricityStatData(provinceId, stationId, date, dimension);
                    }
                    
                    if (!list.isEmpty()) {
                        log.info("第一条数据: {}", list.get(0));
                    }
                } else {
                    log.info("分组统计查询完成 - 结果类型: {}, 值: {}",
                            result != null ? result.getClass().getSimpleName() : "null", result);
                    
                    if (result == null) {
                        log.info("{}维度查询结果为null，生成默认统计数据", "2".equals(dimension) ? "月度" : "年度");
                        return createDefaultElectricityStatData(provinceId, stationId, date, dimension);
                    }
                }
                return result;
            }

            log.error("维度参数不匹配任何已知值 - dimension: [{}]", dimension);
            throw new IllegalArgumentException("不支持的维度参数: " + dimension + "，只支持 1(年)、2(月)、3(日)");
        } catch (Exception e) {
            log.error("ElectricityDataService查询失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建默认的电量电价详细数据（日度维度）
     */
    private List<ElectricityDataDTO> createDefaultElectricityData(Integer provinceId, Long stationId, String date, String dimension) {
        List<ElectricityDataDTO> defaultData = new ArrayList<>();
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date queryDate = sdf.parse(date);
            
            // 日度维度：生成96个时间点的数据（每15分钟一个点）
            if ("3".equals(dimension)) {
                for (int hour = 0; hour < 24; hour++) {
                    for (int minute = 0; minute < 60; minute += 15) {
                        ElectricityDataDTO dto = new ElectricityDataDTO();
                        dto.setId(0L);
                        dto.setDate(queryDate);
                        dto.setTime(String.format("%02d:%02d", hour, minute));
                        dto.setValue(BigDecimal.ZERO); // 电量初始值为0
                        dto.setPrice(BigDecimal.ZERO); // 电价初始值为0
                        dto.setStationId(stationId);
                        dto.setStationName(stationId != null ? "电站_" + stationId : "默认电站");
                        dto.setProvinceId(provinceId != null ? provinceId.longValue() : 0L);
                        dto.setSourceTable("default_data");
                        
                        defaultData.add(dto);
                    }
                }
            }
            
            log.info("生成默认电量电价数据 - 维度: {}, 数据点数: {}", dimension, defaultData.size());
            
        } catch (Exception e) {
            log.error("生成默认电量电价数据失败: {}", e.getMessage(), e);
            // 如果生成失败，至少返回一个基础数据点
            ElectricityDataDTO dto = new ElectricityDataDTO();
            dto.setId(0L);
            dto.setTime("00:00");
            dto.setValue(BigDecimal.ZERO);
            dto.setPrice(BigDecimal.ZERO);
            dto.setStationId(stationId);
            dto.setStationName(stationId != null ? "电站_" + stationId : "默认电站");
            dto.setProvinceId(provinceId != null ? provinceId.longValue() : 0L);
            dto.setSourceTable("default_data");
            defaultData.add(dto);
        }
        
        return defaultData;
    }

    /**
     * 创建默认的电量电价统计数据（月度/年度维度）
     */
    private List<Map<String, Object>> createDefaultElectricityStatData(Integer provinceId, Long stationId, String date, String dimension) {
        List<Map<String, Object>> defaultData = new ArrayList<>();
        
        try {
            if ("2".equals(dimension)) {
                // 月度维度：生成该月每天的数据
                String[] dateParts = date.split("-");
                int year = Integer.parseInt(dateParts[0]);
                int month = Integer.parseInt(dateParts[1]);
                
                Calendar calendar = Calendar.getInstance();
                calendar.set(year, month - 1, 1);
                int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                
                for (int day = 1; day <= daysInMonth; day++) {
                    Map<String, Object> dayData = new HashMap<>();
                    dayData.put("date", String.format("%04d-%02d-%02d", year, month, day));
                    dayData.put("totalValue", 0.0); // 电量初始值为0
                    dayData.put("avgPrice", 0.0);   // 电价初始值为0
                    dayData.put("totalFee", 0.0);   // 电费初始值为0
                    defaultData.add(dayData);
                }
                
            } else if ("1".equals(dimension)) {
                // 年度维度：生成12个月的数据
                int year = Integer.parseInt(date);
                
                for (int month = 1; month <= 12; month++) {
                    Map<String, Object> monthData = new HashMap<>();
                    monthData.put("month", String.format("%04d-%02d", year, month));
                    monthData.put("totalValue", 0.0); // 电量初始值为0
                    monthData.put("avgPrice", 0.0);   // 电价初始值为0
                    monthData.put("totalFee", 0.0);   // 电费初始值为0
                    defaultData.add(monthData);
                }
            }
            
            log.info("生成默认电量电价统计数据 - 维度: {}, 数据点数: {}", dimension, defaultData.size());
            
        } catch (Exception e) {
            log.error("生成默认电量电价统计数据失败: {}", e.getMessage(), e);
            // 如果生成失败，至少返回一个基础数据点
            Map<String, Object> defaultPoint = new HashMap<>();
            defaultPoint.put("2".equals(dimension) ? "date" : "month", date);
            defaultPoint.put("totalValue", 0.0);
            defaultPoint.put("avgPrice", 0.0);
            defaultPoint.put("totalFee", 0.0);
            defaultData.add(defaultPoint);
        }
        
        return defaultData;
    }
}