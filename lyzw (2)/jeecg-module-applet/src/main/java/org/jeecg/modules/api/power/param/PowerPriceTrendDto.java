package org.jeecg.modules.api.power.param;

import lombok.Data;

import java.util.List;

/**
 * 电量电价趋势DTO
 */
@Data
public class PowerPriceTrendDto {
    
    /**
     * 时间点列表
     */
    private List<String> timePoints;
    
    /**
     * 电量数据列表(MWh)
     */
    private List<Double> powerValues;
    
    /**
     * 电价数据列表(元/MWh)
     */
    private List<Double> priceValues;
    
    /**
     * 电量单位
     */
    private String powerUnit = "MWh";
    
    /**
     * 电价单位
     */
    private String priceUnit = "元/MWh";
} 