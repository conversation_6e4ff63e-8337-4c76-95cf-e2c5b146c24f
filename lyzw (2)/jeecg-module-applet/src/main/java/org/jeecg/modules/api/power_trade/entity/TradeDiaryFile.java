package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("trade_diary_file")
public class TradeDiaryFile {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("trade_dairy_id")
    private Long tradeDiaryId;

    @TableField("type")
    private String type;

    @TableField("file_name")
    private String fileName;

    @TableField("file_url")
    private String fileUrl;
} 