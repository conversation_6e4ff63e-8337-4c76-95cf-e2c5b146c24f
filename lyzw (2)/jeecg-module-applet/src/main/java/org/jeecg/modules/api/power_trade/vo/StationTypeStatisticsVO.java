package org.jeecg.modules.api.power_trade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电站类型统计VO
 */
@Data
@ApiModel(value = "StationTypeStatisticsVO", description = "电站类型统计信息")
public class StationTypeStatisticsVO {

    @ApiModelProperty(value = "电站类型 (1-风电, 2-光伏, 3-储能)")
    private Integer stationType;

    @ApiModelProperty(value = "电站类型名称")
    private String stationTypeName;

    @ApiModelProperty(value = "电站数量")
    private Integer stationCount;

    @ApiModelProperty(value = "总容量(MW)")
    private BigDecimal totalCapacity;

    @ApiModelProperty(value = "平均容量(MW)")
    private BigDecimal avgCapacity;

    @ApiModelProperty(value = "总发电量(MWh)")
    private BigDecimal totalPower;

    @ApiModelProperty(value = "平均电价(元/MWh)")
    private BigDecimal avgPrice;

    @ApiModelProperty(value = "图标URL")
    private String iconUrl;

    @ApiModelProperty(value = "占比(%)")
    private BigDecimal percentage;

    /**
     * 获取电站类型名称
     */
    public String getStationTypeName() {
        if (stationType == null) {
            return "未知";
        }
        switch (stationType) {
            case 1:
                return "风电";
            case 2:
                return "光伏";
            case 3:
                return "储能";
            default:
                return "未知";
        }
    }

    /**
     * 获取图标URL
     */
    public String getIconUrl() {
        if (stationType == null) {
            return "/icons/unknown.png";
        }
        switch (stationType) {
            case 1:
                return "/icons/wind.png";
            case 2:
                return "/icons/solar.png";
            case 3:
                return "/icons/storage.png";
            default:
                return "/icons/unknown.png";
        }
    }

    /**
     * 获取容量显示文本（储能特殊展现形式）
     */
    public String getCapacityDisplay() {
        if (totalCapacity == null) {
            return "0MW";
        }
        
        if (stationType != null && stationType == 3) {
            // 储能电站显示为 "容量MW/功率MWh"
            return totalCapacity + "MW/" + totalCapacity + "MWh";
        } else {
            // 风电、光伏显示为 "容量MW"
            return totalCapacity + "MW";
        }
    }

    /**
     * 获取发电量显示文本
     */
    public String getTotalPowerDisplay() {
        if (totalPower == null) {
            return "0 MWh";
        }
        return totalPower.toPlainString() + " MWh";
    }

    /**
     * 获取平均电价显示文本
     */
    public String getAvgPriceDisplay() {
        if (avgPrice == null) {
            return "0 元/MWh";
        }
        return avgPrice.toPlainString() + " 元/MWh";
    }

    /**
     * 获取占比显示文本
     */
    public String getPercentageDisplay() {
        if (percentage == null) {
            return "0%";
        }
        return percentage.toPlainString() + "%";
    }
}
