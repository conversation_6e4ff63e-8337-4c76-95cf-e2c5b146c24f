package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("bi.forecast_power_factory")
public class ForecastPowerFactory {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("factory_name")
    private String factoryName;
}
