package org.jeecg.modules.api.power.service.impl;

import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerQueryParam;
import org.jeecg.modules.api.power.service.PowerStrategy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认功率预测策略实现
 * 该实现作为找不到匹配厂家策略时的备选策略
 */
@Service
public class DefaultPowerStrategyImpl implements PowerStrategy {
    
    @Override
    public Integer getSupportedFactoryId() {
        return 0; // 默认厂家ID
    }
    
    @Override
    public List<CommonPowerDto> queryPowerPredictionByRange(PowerQueryParam param) {
        // 默认实现，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerPredictionByDateList(PowerQueryParam param) {
        // 默认实现，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerRealByRange(PowerQueryParam param) {
        // 默认实现，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public List<CommonPowerDto> queryPowerRealByRangeDateList(PowerQueryParam param) {
        // 默认实现，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public String queryMaxVersion(PowerQueryParam param) {
        // 默认实现，返回基础版本号
        return "1.0";
    }
} 