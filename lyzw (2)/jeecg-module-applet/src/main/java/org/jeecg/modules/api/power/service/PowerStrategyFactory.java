package org.jeecg.modules.api.power.service;

import java.util.HashMap;
import java.util.Map;

/**
 * 功率预测策略工厂
 */
public class PowerStrategyFactory {
    
    private final Map<Integer, PowerStrategy> strategyMap = new HashMap<>();
    
    /**
     * 注册策略
     * @param factoryId 厂家ID
     * @param strategy 策略
     */
    public void registerStrategy(Integer factoryId, PowerStrategy strategy) {
        strategyMap.put(factoryId, strategy);
    }
    
    /**
     * 根据厂家ID获取策略
     * @param factoryId 厂家ID
     * @return 对应的策略
     */
    public PowerStrategy getStrategy(Integer factoryId) {
        PowerStrategy strategy = strategyMap.get(factoryId);
        if (strategy == null) {
            // 如果没有找到对应的策略，返回默认策略
            return getDefaultStrategy();
        }
        return strategy;
    }
    
    /**
     * 获取默认策略
     * @return 默认策略
     */
    private PowerStrategy getDefaultStrategy() {
        return new DefaultPowerStrategy();
    }
} 