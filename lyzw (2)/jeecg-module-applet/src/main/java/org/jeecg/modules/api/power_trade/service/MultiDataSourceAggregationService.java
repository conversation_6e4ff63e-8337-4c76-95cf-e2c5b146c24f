package org.jeecg.modules.api.power_trade.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.SettlementSummaryDTO;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.entity.MarketNotice;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.vo.MarketNoticeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 多数据源聚合服务
 * 处理全国数据源的数据汇总逻辑，支持所有接口的多数据源汇总
 */
@Slf4j
@Service
public class MultiDataSourceAggregationService {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private ScreenTradeSettlementMapper screenTradeSettlementMapper;

    @Autowired
    private MarketNoticeService marketNoticeService;

    @Autowired
    private ITradeCalendarRecordService tradeCalendarRecordService;

    // 并行查询线程池
    private final ExecutorService queryExecutor = Executors.newFixedThreadPool(20);

    // 查询超时时间（秒）
    private static final int QUERY_TIMEOUT_SECONDS = 30;

    /**
     * 聚合所有省份的电站发电量数据
     */
    public StationDetailResponseDTO.PowerGenerationInfo aggregateAllProvincesPowerGeneration(
            Long stationId, String date, String dimension) {

        // 获取所有可用的数据源
        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<PowerGenerationData>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceData(s, stationId, date, dimension), queryExecutor))
                .collect(Collectors.toList());

        // 等待所有查询完成并聚合结果
        try {
            List<PowerGenerationData> allData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(30, TimeUnit.SECONDS); // 30秒超时
                        } catch (Exception e) {
                            log.warn("查询省份数据失败: {}", e.getMessage());
                            return new PowerGenerationData(); // 返回空数据
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return aggregatePowerGenerationData(allData, dimension, date);

        } catch (Exception e) {
            log.error("聚合全国发电量数据失败: {}", e.getMessage(), e);
            return createDefaultPowerGenerationInfo(dimension, date);
        }
    }

    /**
     * 查询单个省份的发电量数据
     */
    private PowerGenerationData queryProvinceData(Integer provinceId, Long stationId, String date, String dimension) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new PowerGenerationData();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里调用具体的发电量查询逻辑
            // 如果stationId不为null，查询特定电站；否则查询该省份所有电站
            return queryPowerGenerationFromDataSource(stationId, date, dimension, provinceId);

        } catch (Exception e) {
            log.warn("查询省份{}发电量数据失败: {}", provinceId, e.getMessage());
            return new PowerGenerationData();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 从数据源查询发电量数据
     */
    private PowerGenerationData queryPowerGenerationFromDataSource(Long stationId, String date, String dimension, Integer provinceId) {
        // 实现具体的查询逻辑
        // 这里需要调用PowerService或直接查询功率预测表
        PowerGenerationData data = new PowerGenerationData();
        data.setProvinceId(provinceId);

        // 根据dimension生成完整的时间序列数据
        List<TimeSeriesPoint> timeSeriesData = generateCompleteTimeSeries(dimension, date);

        // 查询实际的功率数据并填充到时间序列中
        fillActualPowerData(timeSeriesData, stationId, date, dimension);

        data.setTimeSeriesData(timeSeriesData);
        data.setTotalGeneration(calculateTotalGeneration(timeSeriesData));

        return data;
    }

    /**
     * 生成完整的时间序列结构
     */
    private List<TimeSeriesPoint> generateCompleteTimeSeries(String dimension, String date) {
        List<TimeSeriesPoint> timeSeries = new ArrayList<>();

        switch (dimension) {
            case "3": // 日维度 - 96个时间点
                for (int i = 0; i < 96; i++) {
                    int hour = i / 4;
                    int minute = (i % 4) * 15;
                    String timeLabel = String.format("%02d:%02d", hour, minute);
                    timeSeries.add(new TimeSeriesPoint(timeLabel, BigDecimal.ZERO));
                }
                break;

            case "2": // 月维度 - 该月所有天数
                int daysInMonth = getDaysInMonth(date);
                for (int day = 1; day <= daysInMonth; day++) {
                    String timeLabel = String.format("%02d日", day);
                    timeSeries.add(new TimeSeriesPoint(timeLabel, BigDecimal.ZERO));
                }
                break;

            case "1": // 年维度 - 12个月
                for (int month = 1; month <= 12; month++) {
                    String timeLabel = String.format("%02d月", month);
                    timeSeries.add(new TimeSeriesPoint(timeLabel, BigDecimal.ZERO));
                }
                break;
        }

        return timeSeries;
    }

    /**
     * 填充实际功率数据
     */
    private void fillActualPowerData(List<TimeSeriesPoint> timeSeriesData, Long stationId, String date, String dimension) {
        // 这里需要调用PowerService查询实际功率数据
        // 然后将功率数据除以4转换为发电量，填充到对应的时间点

        // 示例实现（需要根据实际的PowerService接口调整）
        try {
            // Map<String, BigDecimal> actualPowerData = powerService.getActualPowerData(stationId, date, dimension);
            // 
            // for (TimeSeriesPoint point : timeSeriesData) {
            //     BigDecimal power = actualPowerData.get(point.getTimeLabel());
            //     if (power != null) {
            //         point.setPowerGeneration(power.divide(BigDecimal.valueOf(4), 2, RoundingMode.HALF_UP));
            //     }
            // }
        } catch (Exception e) {
            log.warn("填充实际功率数据失败: {}", e.getMessage());
        }
    }

    /**
     * 聚合多个省份的发电量数据
     */
    private StationDetailResponseDTO.PowerGenerationInfo aggregatePowerGenerationData(
            List<PowerGenerationData> allData, String dimension, String date) {

        StationDetailResponseDTO.PowerGenerationInfo result = new StationDetailResponseDTO.PowerGenerationInfo();
        result.setDimension(dimension);
        result.setQueryDate(date);

        if (allData.isEmpty()) {
            result.setCurrentPeriodGeneration(BigDecimal.ZERO);
            return result;
        }

        // 聚合总发电量
        BigDecimal totalGeneration = allData.stream()
                .map(PowerGenerationData::getTotalGeneration)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        result.setCurrentPeriodGeneration(totalGeneration);

        // 聚合时间序列数据（按时间点求和）
        Map<String, BigDecimal> aggregatedTimeSeries = new HashMap<>();
        for (PowerGenerationData data : allData) {
            if (data.getTimeSeriesData() != null) {
                for (TimeSeriesPoint point : data.getTimeSeriesData()) {
                    aggregatedTimeSeries.merge(point.getTimeLabel(),
                            point.getPowerGeneration(), BigDecimal::add);
                }
            }
        }

        // 将聚合后的时间序列数据设置到结果中（如果需要返回详细时间序列）
        // result.setTimeSeriesData(aggregatedTimeSeries);

        return result;
    }

    /**
     * 计算总发电量
     */
    private BigDecimal calculateTotalGeneration(List<TimeSeriesPoint> timeSeriesData) {
        return timeSeriesData.stream()
                .map(TimeSeriesPoint::getPowerGeneration)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取月份天数
     */
    private int getDaysInMonth(String yearMonth) {
        try {
            String[] parts = yearMonth.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            if (month == 2) {
                return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
            } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                return 30;
            } else {
                return 31;
            }
        } catch (Exception e) {
            log.error("解析月份失败: {}", yearMonth, e);
            return 31;
        }
    }

    /**
     * 创建默认的发电量信息
     */
    private StationDetailResponseDTO.PowerGenerationInfo createDefaultPowerGenerationInfo(String dimension, String date) {
        StationDetailResponseDTO.PowerGenerationInfo info = new StationDetailResponseDTO.PowerGenerationInfo();
        info.setCurrentPeriodGeneration(BigDecimal.ZERO);
        info.setDimension(dimension);
        info.setQueryDate(date);
        return info;
    }

    /**
     * 发电量数据内部类
     */
    @Data
    private static class PowerGenerationData {
        // getters and setters
        private Integer provinceId;
        private List<TimeSeriesPoint> timeSeriesData;
        private BigDecimal totalGeneration;

    }

    /**
     * 时间序列数据点
     */
    @Data
    private static class TimeSeriesPoint {
        // getters and setters
        private String timeLabel;
        private BigDecimal powerGeneration;

        public TimeSeriesPoint(String timeLabel, BigDecimal powerGeneration) {
            this.timeLabel = timeLabel;
            this.powerGeneration = powerGeneration;
        }

    }

    /**
     * 聚合所有省份的电站列表数据
     */
    public Map<String, Object> aggregateAllProvincesStationList(
            Integer pageNo, Integer pageSize, String year, String month, String name) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源的电站列表，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.entrySet().stream()
                .filter(entry -> entry.getKey() != 0) // 排除全国汇总
                .map(entry -> CompletableFuture.supplyAsync(() ->
                        queryProvinceStationList(entry.getKey(), year, month, name), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的电站数据
            List<Map<String, Object>> allStations = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份电站列表失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 分页处理
            int total = allStations.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedStations = startIndex < total ?
                    allStations.subList(startIndex, endIndex) : new ArrayList<>();

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("current", pageNo);
            result.put("size", pageSize);
            result.put("total", total);
            result.put("pages", (total + pageSize - 1) / pageSize);
            result.put("records", pagedStations);

            return result;

        } catch (Exception e) {
            log.error("聚合全国电站列表失败: {}", e.getMessage(), e);
            return createEmptyPageResult(pageNo, pageSize);
        }
    }

    /**
     * 查询单个省份的电站列表
     */
    private List<Map<String, Object>> queryProvinceStationList(Integer provinceId, String year, String month, String name) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return new ArrayList<>();
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 构建查询条件
            LambdaQueryWrapper<Station> stationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            stationLambdaQueryWrapper.eq(Station::getTradeStatus, 1)
                    .eq(true, Station::getProvinceId, provinceId);

            // 添加电站名称搜索条件
            if (name != null && !name.trim().isEmpty()) {
                stationLambdaQueryWrapper.like(Station::getName, name.trim());
            }

            // 查询电站列表（不分页，获取所有数据用于汇总）
            List<Station> stations = stationService.list(stationLambdaQueryWrapper);

            log.info("查询到省份{}的电站数量：{}", provinceId, stations.size());

            // 为每个电站添加结算数据
            List<Map<String, Object>> result = stations.stream()
                    .map(station -> buildStationWithSettlementData(station, year, month))
                    .collect(Collectors.toList());

            log.info("省份{}处理完成，返回电站数量：{}", provinceId, result.size());
            return result;

        } catch (Exception e) {
            log.warn("查询省份{}电站列表失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 构建包含结算数据的电站信息（从Controller提取的逻辑）
     */
    private Map<String, Object> buildStationWithSettlementData(Station station, String year, String month) {
        Map<String, Object> result = new HashMap<>();
        result.put("stationInfo", station);

        try {
            LambdaQueryWrapper<ScreenTradeSettlement> settlementWrapper = new LambdaQueryWrapper<>();
            settlementWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                    .eq(ScreenTradeSettlement::getYear, year)
                    .eq(ScreenTradeSettlement::getMonth, String.format("%02d", Integer.parseInt(month)));

            LambdaQueryWrapper<YearlyPowerPlan> yearlyWrapper = new LambdaQueryWrapper<>();
            yearlyWrapper.eq(YearlyPowerPlan::getStationId, station.getId())
                    .eq(YearlyPowerPlan::getYear, year)
                    .eq(YearlyPowerPlan::getMonth, String.format("%02d", Integer.parseInt(month)));

            ScreenTradeSettlement settlement = screenTradeSettlementService.getOne(settlementWrapper);
            YearlyPowerPlan yearly = yearlyPowerPlanService.getOne(yearlyWrapper);



            if (settlement != null) {
                // 当月实际发电量 - 使用功率预测实际功率求和除以4的计算逻辑
                BigDecimal currentMonthPower = calculateCurrentPeriodGeneration(station.getId(),
                        year + "-" + String.format("%02d", Integer.parseInt(month)), "2");
                result.put("current_month_power", currentMonthPower);

                // 当月计划发电量
                BigDecimal currentMonthPlanPower = yearly != null && yearly.getPlanValue() != null
                        ? yearly.getPlanValue()
                        : BigDecimal.ZERO;
                result.put("current_month_plan_power", currentMonthPlanPower);

                // 结算均价
                BigDecimal settlementAveragePrice = settlement.getSettlementAveragePrice() != null
                        ? settlement.getSettlementAveragePrice()
                        : BigDecimal.ZERO;
                result.put("settlement_average_price", settlementAveragePrice);
            } else {
                // 没有结算数据时的默认值
                result.put("current_month_power", BigDecimal.ZERO);
                result.put("current_month_plan_power", BigDecimal.ZERO);
                result.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            // 异常时设置默认值
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("current_month_plan_power", BigDecimal.ZERO);
            result.put("settlement_average_price", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 计算当前周期发电量（从功率预测实际功率计算）
     */
    private BigDecimal calculateCurrentPeriodGeneration(Long stationId, String date, String dimension) {
        try {
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setProvinceId(1); // 在动态数据源上下文中，设置默认省份ID
            param.setTimeDimension(dimension);
            param.setQueryDate(date);

            // 获取功率预测数据
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData != null && !trendData.isEmpty()) {
                // 使用实际功率计算发电量：实际功率求和 ÷ 4
                double totalActualPower = trendData.stream()
                        .map(PowerGenerationTrendDto::getActualPower)
                        .filter(Objects::nonNull)
                        .reduce(0.0, Double::sum);

                // 实际功率除以4得到发电量
                return BigDecimal.valueOf(totalActualPower / 4.0);
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("计算发电量失败，返回0: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 聚合所有省份的首页概览数据
     */
    public Map<String, Object> aggregateAllProvincesDashboardSummary() {
        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<DashboardSummaryData>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceDashboardSummary(s), queryExecutor))
                .collect(Collectors.toList());

        try {
            List<DashboardSummaryData> allData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份首页数据失败: {}", e.getMessage());
                            return new DashboardSummaryData();
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return aggregateDashboardSummaryData(allData);

        } catch (Exception e) {
            log.error("聚合全国首页数据失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 查询单个省份的首页概览数据
     */
    private DashboardSummaryData queryProvinceDashboardSummary(Integer provinceId) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return new DashboardSummaryData();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            DashboardSummaryData data = new DashboardSummaryData();
            data.setProvinceId(provinceId);

            // 1. 获取指定省份且已参与交易的所有电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getTradeStatus, 1)
                    .eq(Station::getProvinceId, provinceId);

            List<Station> stationList = stationService.list(stationQuery);

            if (stationList == null || stationList.isEmpty()) {
                return data;
            }

            // 2. 计算交易容量
            double tradingCapacity = stationList.stream()
                    .mapToDouble(Station::getCapacity)
                    .sum();

            // 储能站功率单独累加
            double storagePower = stationList.stream()
                    .filter(s -> s.getType() != null && s.getType() == 3)
                    .mapToDouble(station -> station.getPower() != null ? station.getPower() : 0.0)
                    .sum();
            tradingCapacity += storagePower;

            // 3. 统计各类型场站数量
            int windCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 1).count();
            int solarCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 2).count();
            int storageCount = (int) stationList.stream().filter(s -> s.getType() != null && s.getType() == 3).count();

            // 4. 查询结算数据
            List<Long> stationIds = stationList.stream()
                    .map(Station::getId)
                    .collect(Collectors.toList());

            Map<String, Object> summaryData = screenTradeSettlementMapper.selectDashboardSummaryData(stationIds);

            double accumulatedPower = 0.0;
            double plannedPower = 0.0;
            double settlementAvgPrice = 0.0;
            double settlementPower = 0.0;
            double limitedPower = 0.0;
            double totalElectricity = 0.0;
            double totalFee = 0.0;

            if (summaryData != null && !summaryData.isEmpty()) {
                accumulatedPower = convertToDouble(summaryData.get("accumulatedPower"));
                plannedPower = convertToDouble(summaryData.get("plannedPower"));
                settlementAvgPrice = convertToDouble(summaryData.get("settlementAvgPrice"));
                settlementPower = convertToDouble(summaryData.get("settlementPower"));
                limitedPower = convertToDouble(summaryData.get("limitedPower"));
                totalElectricity = settlementPower; // 使用结算电量作为总电量
                totalFee = settlementPower * settlementAvgPrice; // 计算总费用
            }

            // 5. 设置数据
            data.setStationCount(stationList.size());
            data.setTotalCapacity(tradingCapacity);
            data.setTotalPowerGeneration(accumulatedPower);
            data.setTotalElectricity(totalElectricity);
            data.setTotalFee(totalFee);
            data.setWindStationCount(windCount);
            data.setSolarStationCount(solarCount);
            data.setStorageStationCount(storageCount);
            data.setPlannedPower(plannedPower);
            data.setSettlementPower(settlementPower);
            data.setLimitedPower(limitedPower);

            return data;

        } catch (Exception e) {
            log.warn("查询省份{}首页数据失败: {}", provinceId, e.getMessage());
            return new DashboardSummaryData();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 安全转换为double
     */
    private double convertToDouble(Object value) {
        if (value == null) {
            return 0.0;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 聚合首页概览数据
     */
    private Map<String, Object> aggregateDashboardSummaryData(List<DashboardSummaryData> allData) {
        Map<String, Object> result = new HashMap<>();

        if (allData.isEmpty()) {
            return result;
        }

        // 汇总电站统计数据
        int totalStations = allData.stream().mapToInt(DashboardSummaryData::getStationCount).sum();
        double totalCapacity = allData.stream().mapToDouble(DashboardSummaryData::getTotalCapacity).sum();
        double totalPowerGeneration = allData.stream().mapToDouble(DashboardSummaryData::getTotalPowerGeneration).sum();

        // 汇总各类型电站数量
        int windStationCount = allData.stream().mapToInt(DashboardSummaryData::getWindStationCount).sum();
        int solarStationCount = allData.stream().mapToInt(DashboardSummaryData::getSolarStationCount).sum();
        int storageStationCount = allData.stream().mapToInt(DashboardSummaryData::getStorageStationCount).sum();

        // 汇总其他数据
        double plannedPower = allData.stream().mapToDouble(DashboardSummaryData::getPlannedPower).sum();
        double settlementPower = allData.stream().mapToDouble(DashboardSummaryData::getSettlementPower).sum();
        double limitedPower = allData.stream().mapToDouble(DashboardSummaryData::getLimitedPower).sum();

        // 计算加权平均价格
        double totalElectricity = allData.stream().mapToDouble(DashboardSummaryData::getTotalElectricity).sum();
        double totalFee = allData.stream().mapToDouble(DashboardSummaryData::getTotalFee).sum();
        double avgPrice = totalElectricity > 0 ? totalFee / totalElectricity : 0.0;

        // 设置默认的目标电价和基准电价（可以根据实际需求调整）
        double targetPowerPrice = 391.0; // 默认值
        double benchmarkPrice = 384.4; // 默认值

        result.put("totalStations", totalStations);
        result.put("totalCapacity", totalCapacity);
        result.put("totalPowerGeneration", totalPowerGeneration);
        result.put("windStationCount", windStationCount);
        result.put("solarStationCount", solarStationCount);
        result.put("storageStationCount", storageStationCount);
        result.put("plannedPower", plannedPower);
        result.put("settlementPower", settlementPower);
        result.put("limitedPower", limitedPower);
        result.put("avgPrice", avgPrice);
        result.put("targetPowerPrice", targetPowerPrice);
        result.put("benchmarkPrice", benchmarkPrice);

        return result;
    }

    /**
     * 聚合所有省份的市场公告数据
     */
    public Map<String, Object> aggregateAllProvincesMarketNotices(
            Integer pageNo, Integer pageSize, Integer typeId, String keyword) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.keySet().stream()
                .filter(provinceId -> provinceId != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceMarketNotices(s, typeId, keyword), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的公告数据
            List<Map<String, Object>> allNotices = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份公告数据失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream).sorted((a, b) -> {
                        // 这里需要根据实际的时间字段进行排序
                        return 0; // 简化实现
                    }).collect(Collectors.toList());

            // 按发布时间排序

            // 分页处理
            int total = allNotices.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedNotices = startIndex < total ?
                    allNotices.subList(startIndex, endIndex) : new ArrayList<>();

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("current", pageNo);
            result.put("size", pageSize);
            result.put("total", total);
            result.put("pages", (total + pageSize - 1) / pageSize);
            result.put("records", pagedNotices);

            return result;

        } catch (Exception e) {
            log.error("聚合全国公告数据失败: {}", e.getMessage(), e);
            return createEmptyPageResult(pageNo, pageSize);
        }
    }

    /**
     * 查询单个省份的市场公告
     */
    private List<Map<String, Object>> queryProvinceMarketNotices(Integer provinceId, Integer typeId, String keyword) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            log.info("查询省份{}的市场公告，类型ID：{}，关键词：{}", provinceId, typeId, keyword);

            // 查询该省份的所有公告（不分页，用于汇总）
            Page<MarketNotice> page = new Page<>(1, 1000); // 设置较大的页面大小获取所有数据
            IPage<MarketNoticeVO> pageList = marketNoticeService.getAnnouncementList(page, typeId, provinceId, keyword);

            // 转换为Map格式
            List<Map<String, Object>> result = pageList.getRecords().stream()
                    .map(notice -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", notice.getId());
                        map.put("title", notice.getNoticeTitle());
                        map.put("content", notice.getNoticeContent());
                        map.put("publishTime", notice.getNoticeDate());
                        map.put("typeId", notice.getTypeId());
                        map.put("typeName", notice.getTypeName());
                        map.put("provinceId", provinceId);
                        return map;
                    })
                    .collect(Collectors.toList());

            log.info("省份{}查询到{}条公告", provinceId, result.size());
            return result;

        } catch (Exception e) {
            log.warn("查询省份{}公告数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 聚合所有省份的交易日历数据
     */
    public List<Map<String, Object>> aggregateAllProvincesTradeCalendar(String date) {
        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.entrySet().stream()
                .filter(entry -> entry.getKey() != 0) // 排除全国汇总
                .map(entry -> CompletableFuture.supplyAsync(() ->
                        queryProvinceTradeCalendar(entry.getKey(), date), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的交易日历数据
            List<Map<String, Object>> allCalendarData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份交易日历失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 按日期和省份合并相同日期的交易事件
            return mergeTradeCalendarData(allCalendarData);

        } catch (Exception e) {
            log.error("聚合全国交易日历失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询单个省份的交易日历
     */
    private List<Map<String, Object>> queryProvinceTradeCalendar(Integer provinceId, String date) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            log.info("查询省份{}的交易日历，日期：{}", provinceId, date);

            // 根据省份ID选择不同的查询策略
            List<TradeCalendarRecord> records;
            switch (provinceId) {
                case 1:  // 江苏省
                    records = tradeCalendarRecordService.getCalendarWithTradeType(date, provinceId);
                    break;
                case 2:  // 安徽省
                    records = tradeCalendarRecordService.getCalendarWithTargetDateAndTradeType(date, provinceId);
                    break;
                default:
                    log.warn("不支持的省份ID: {}", provinceId);
                    return new ArrayList<>();
            }

            // 转换为Map格式
            List<Map<String, Object>> result = records.stream()
                    .map(record -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", record.getId());
                        map.put("date", record.getTradeDate());
                        map.put("tradeType", record.getTradeType());
                        map.put("description", record.getRemark());
                        map.put("provinceId", provinceId);
                        return map;
                    })
                    .collect(Collectors.toList());

            log.info("省份{}查询到{}条交易日历记录", provinceId, result.size());
            return result;

        } catch (Exception e) {
            log.warn("查询省份{}交易日历失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 合并交易日历数据
     */
    private List<Map<String, Object>> mergeTradeCalendarData(List<Map<String, Object>> allCalendarData) {
        // 按日期分组，合并相同日期的交易事件
        Map<String, List<Map<String, Object>>> groupedByDate = allCalendarData.stream()
                .collect(Collectors.groupingBy(data -> (String) data.get("date")));

        List<Map<String, Object>> mergedData = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedByDate.entrySet()) {
            Map<String, Object> mergedEvent = new HashMap<>();
            mergedEvent.put("date", entry.getKey());

            // 合并交易类型和描述
            List<String> tradeTypes = entry.getValue().stream()
                    .map(data -> (String) data.get("tradeType"))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            mergedEvent.put("tradeTypes", tradeTypes);
            mergedEvent.put("eventCount", entry.getValue().size());

            mergedData.add(mergedEvent);
        }

        return mergedData;
    }

    /**
     * 创建空的分页结果
     */
    private Map<String, Object> createEmptyPageResult(Integer pageNo, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        result.put("current", pageNo);
        result.put("size", pageSize);
        result.put("total", 0);
        result.put("pages", 0);
        result.put("records", new ArrayList<>());
        return result;
    }

    /**
     * 首页概览数据内部类
     */
    @Data
    private static class DashboardSummaryData {
        // getters and setters
        private Integer provinceId;
        private int stationCount;
        private double totalCapacity;
        private double totalPowerGeneration;
        private double totalElectricity;
        private double totalFee;
        private int windStationCount;
        private int solarStationCount;
        private int storageStationCount;
        private double plannedPower;
        private double settlementPower;
        private double limitedPower;

    }

    /**
     * 聚合所有省份的结算概况数据
     */
    public List<SettlementSummaryDTO> aggregateAllProvincesSettlementSummary(
            Long stationId, Integer dimension, String month, String year) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<SettlementSummaryDTO>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceSettlementSummary(s, stationId, dimension, month, year), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的结算数据
            List<SettlementSummaryDTO> allSettlements = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份结算数据失败: {}", e.getMessage());
                            return new ArrayList<SettlementSummaryDTO>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 按结算电量降序排列
            allSettlements.sort((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity()));

            return allSettlements;

        } catch (Exception e) {
            log.error("聚合全国结算概况失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 聚合所有省份的储能日清洁数据
     */
    public Map<String, Object> aggregateAllProvincesEnergyStorageDailyClean(
            Integer pageNo, Integer pageSize, String year, String month, String name) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceEnergyStorageDailyClean(s, year, month, name), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的储能数据
            List<Map<String, Object>> allStorageData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份储能数据失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 分页处理
            int total = allStorageData.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedData = startIndex < total ?
                    allStorageData.subList(startIndex, endIndex) : new ArrayList<>();

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("current", pageNo);
            result.put("size", pageSize);
            result.put("total", total);
            result.put("pages", (total + pageSize - 1) / pageSize);
            result.put("records", pagedData);

            return result;

        } catch (Exception e) {
            log.error("聚合全国储能数据失败: {}", e.getMessage(), e);
            return createEmptyPageResult(pageNo, pageSize);
        }
    }

    /**
     * 聚合所有省份的新能源日清洁数据
     */
    public Map<String, Object> aggregateAllProvincesEnergyNewDailyClean(
            Integer pageNo, Integer pageSize, String year, String month, String name) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceEnergyNewDailyClean(s, year, month, name), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的新能源数据
            List<Map<String, Object>> allNewEnergyData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份新能源数据失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 分页处理
            int total = allNewEnergyData.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedData = startIndex < total ?
                    allNewEnergyData.subList(startIndex, endIndex) : new ArrayList<>();

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("current", pageNo);
            result.put("size", pageSize);
            result.put("total", total);
            result.put("pages", (total + pageSize - 1) / pageSize);
            result.put("records", pagedData);

            return result;

        } catch (Exception e) {
            log.error("聚合全国新能源数据失败: {}", e.getMessage(), e);
            return createEmptyPageResult(pageNo, pageSize);
        }
    }

    /**
     * 聚合所有省份的发电趋势数据
     */
    public List<PowerGenerationTrendDto> aggregateAllProvincesPowerGenerationTrend(
            PowerGenerationTrendQueryParam param) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<PowerGenerationTrendDto>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvincePowerGenerationTrend(s, param), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的发电趋势数据
            List<PowerGenerationTrendDto> allTrendData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份发电趋势失败: {}", e.getMessage());
                            return new ArrayList<PowerGenerationTrendDto>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 按时间维度聚合数据
            return aggregatePowerGenerationTrendData(allTrendData, param.getTimeDimension());

        } catch (Exception e) {
            log.error("聚合全国发电趋势失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询单个省份的结算汇总数据
     */
    private List<SettlementSummaryDTO> queryProvinceSettlementSummary(Integer provinceId,
            Long stationId, Integer dimension, String month, String year) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里应该调用具体的结算汇总查询逻辑
            // 暂时返回空列表，避免编译错误
            log.info("查询省份{}结算汇总数据 - 电站ID: {}, 维度: {}, 月份: {}, 年份: {}",
                    provinceId, stationId, dimension, month, year);
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("查询省份{}结算汇总数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 查询单个省份的储能日清分数据
     */
    private List<Map<String, Object>> queryProvinceEnergyStorageDailyClean(Integer provinceId,
            String year, String month, String name) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里应该调用具体的储能日清分查询逻辑
            // 暂时返回空列表，避免编译错误
            log.info("查询省份{}储能日清分数据 - 年份: {}, 月份: {}, 名称: {}",
                    provinceId, year, month, name);
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("查询省份{}储能日清分数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 查询单个省份的新能源日清分数据
     */
    private List<Map<String, Object>> queryProvinceEnergyNewDailyClean(Integer provinceId,
            String year, String month, String name) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里应该调用具体的新能源日清分查询逻辑
            // 暂时返回空列表，避免编译错误
            log.info("查询省份{}新能源日清分数据 - 年份: {}, 月份: {}, 名称: {}",
                    provinceId, year, month, name);
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("查询省份{}新能源日清分数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 查询单个省份的发电趋势数据
     */
    private List<PowerGenerationTrendDto> queryProvincePowerGenerationTrend(Integer provinceId,
            PowerGenerationTrendQueryParam param) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里应该调用具体的发电趋势查询逻辑
            // 暂时返回空列表，避免编译错误
            log.info("查询省份{}发电趋势数据 - 参数: {}", provinceId, param);
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("查询省份{}发电趋势数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 聚合发电趋势数据
     */
    private List<PowerGenerationTrendDto> aggregatePowerGenerationTrendData(
            List<PowerGenerationTrendDto> allTrendData, String timeDimension) {
        try {
            // 这里应该实现具体的数据聚合逻辑
            // 暂时返回原始数据，避免编译错误
            log.info("聚合发电趋势数据 - 数据量: {}, 时间维度: {}", allTrendData.size(), timeDimension);
            return allTrendData;
        } catch (Exception e) {
            log.warn("聚合发电趋势数据失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
}

