package org.jeecg.modules.api.power.service.impl;

import org.jeecg.modules.api.power.service.DefaultPowerStrategy;
import org.jeecg.modules.api.power.service.PowerStrategy;
import org.jeecg.modules.api.power.service.PowerStrategyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功率预测策略工厂实现类
 */
@Service("powerStrategyFactory")
public class PowerStrategyFactoryImpl extends PowerStrategyFactory {

    @Autowired
    public PowerStrategyFactoryImpl(List<PowerStrategy> strategies) {
        // 注册所有已实现的策略
        if (strategies != null && !strategies.isEmpty()) {
            strategies.forEach(strategy ->
                    registerStrategy(strategy.getSupportedFactoryId(), strategy)
            );
        }
    }
}
