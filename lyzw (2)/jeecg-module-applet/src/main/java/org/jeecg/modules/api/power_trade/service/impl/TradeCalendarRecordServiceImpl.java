package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.entity.TradeDiaryFile;
import org.jeecg.modules.api.power_trade.mapper.TradeCalendarRecordMapper;
import org.jeecg.modules.api.power_trade.service.ITradeCalendarRecordService;
import org.jeecg.modules.api.power_trade.service.ITradeDiaryFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 交易日历记录服务实现
 */
@Slf4j
@Service
public class TradeCalendarRecordServiceImpl extends ServiceImpl<TradeCalendarRecordMapper, TradeCalendarRecord> implements ITradeCalendarRecordService {

    @Autowired
    private ITradeDiaryFileService tradeDiaryFileService;

    @Override
    public List<TradeCalendarRecord> getCalendarByDateRange(Date startDate, Date endDate, Integer provinceId) {
        // 已经通过数据源切换到特定省份，不再需要provinceId参数
        List<TradeCalendarRecord> records = baseMapper.selectCalendarByDateRange(startDate, endDate, null);
        loadTradeCalendarFiles(records);
        return records;
    }

    @Override
    public List<TradeCalendarRecord> getCalendarWithTargetDateAndTradeType(String date, Integer provinceId) {
        log.info("查询交易日历记录（安徽省）- 日期: {}, 省份ID: {}", date, provinceId);
        List<TradeCalendarRecord> records = baseMapper.selectCalendarWithTargetDateAndTradeType(date, provinceId);
        log.info("查询结果数量: {}", records != null ? records.size() : 0);

        // 安徽省需要加载附件
        loadTradeCalendarFiles(records);
        return records;
    }

    @Override
    public List<TradeCalendarRecord> getCalendarWithTradeType(String date, Integer provinceId) {
        log.info("查询交易日历记录（江苏省）- 日期: {}, 省份ID: {}", date, provinceId);
        List<TradeCalendarRecord> records = baseMapper.selectCalendarWithTradeType(date, provinceId);
        log.info("查询结果数量: {}", records != null ? records.size() : 0);

        // 江苏省数据库没有附件表，跳过附件加载
        log.info("江苏省数据源，跳过附件加载");
        return records;
    }

    /**
     * 加载交易日历关联的附件
     * @param records 交易日历记录列表
     */
    private void loadTradeCalendarFiles(List<TradeCalendarRecord> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // 提取所有交易日历ID
        List<Long> recordIds = records.stream().map(TradeCalendarRecord::getId).collect(Collectors.toList());

        // 批量查询所有附件并按交易日历ID分组
        Map<Long, List<TradeDiaryFile>> fileMap = tradeDiaryFileService.getFilesByDiaryIds(recordIds);

        // 将附件设置到对应的交易日历中
        records.forEach(record -> record.setFiles(fileMap.getOrDefault(record.getId(), new ArrayList<>())));
    }
}