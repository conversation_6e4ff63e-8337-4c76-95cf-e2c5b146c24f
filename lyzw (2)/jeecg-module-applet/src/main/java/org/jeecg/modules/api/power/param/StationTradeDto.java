package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 电站交易概况DTO
 */
@Data
public class StationTradeDto {
    
    /**
     * 电站ID
     */
    private Long stationId;
    
    /**
     * 电站名称
     */
    private String stationName;
    
    /**
     * 电站类型 (1-风电, 2-光伏, 3-储能)
     */
    private Integer stationType;
    
    /**
     * 电站图标
     */
    private String stationIcon;
    
    /**
     * 结算电量(MWh)
     */
    private Double settlementPower;
    
    /**
     * 交易均价(元/MWh)
     */
    private Double averagePrice;
} 