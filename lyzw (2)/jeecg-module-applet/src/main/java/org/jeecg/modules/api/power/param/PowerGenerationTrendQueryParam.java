package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 发电趋势查询参数
 */
@Data
public class PowerGenerationTrendQueryParam {
    
    /**
     * 电站ID
     */
    private Long stationId;
    
    /**
     * 时间维度: 1-年度, 2-月度, 3-日度
     */
    private String timeDimension;

    /**
     * 查询日期
     * 1(年度): YYYY
     * 2(月度): YYYY-MM
     * 3(日度): YYYY-MM-DD
     */
    private String queryDate;
    
    /**
     * 省份ID(用于场站筛选)
     */
    private Integer provinceId;
}