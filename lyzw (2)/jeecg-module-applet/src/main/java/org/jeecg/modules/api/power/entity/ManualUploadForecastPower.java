package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("forecast_power_report.manual_upload_forecast_power")
public class ManualUploadForecastPower {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("station_id")
    private Long stationId;

    @TableField("date")
    private String date;

    @TableField("time")
    private String time;

    @TableField("value")
    private Double value;

    @TableField("version")
    private String version;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
