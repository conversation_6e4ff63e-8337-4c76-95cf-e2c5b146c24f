package org.jeecg.modules.api.coze.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import java.net.URLDecoder;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.CozeUtil;
import org.jeecg.modules.api.coze.dto.ChatMessageDTO;
import org.jeecg.modules.api.coze.dto.UserMessageDTO;
import org.jeecg.modules.api.coze.service.CozeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import springfox.documentation.spring.web.json.Json;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/coze")
@Api(tags = "Coze AI 智能体接口")
@Slf4j
public class CozeController {


    @Value("${coze.botId}")
    private String DEFAULT_BOT_ID;

    @Value("${coze.appKey}")
    private String DEFAULT_APP_KEY;

    private static final int MAX_RETRY_COUNT = 5; // 最大重试次数
    private static final long RETRY_INTERVAL_MS = 8000; // 重试间隔(毫秒)

    @Autowired
    private CozeService cozeService;

    // ==================== 智能体配置相关接口 ====================

    /**
     * 获取智能体配置
     */
    @GetMapping("/bot/config")
    @ApiOperation(value = "获取智能体配置", notes = "获取指定智能体的配置信息")
    public Result<JSONObject> getBotConfig() {
        try {
            JSONObject config = CozeUtil.getBotConfig(DEFAULT_BOT_ID, DEFAULT_APP_KEY, true);

            if (CozeUtil.isResponseSuccess(config)) {
                log.info("获取智能体配置成功，botId: {}", DEFAULT_BOT_ID);
                return Result.OK(config);
            } else {
                log.error("获取智能体配置失败，botId: {}, 响应: {}", DEFAULT_BOT_ID, config);
                return Result.error("获取智能体配置失败");
            }

        } catch (Exception e) {
            log.error("获取智能体配置异常: {}", e.getMessage(), e);
            return Result.error("获取智能体配置异常: " + e.getMessage());
        }
    }

    @PostMapping("/audio/upload")
    @ApiOperation("上传语音并识别文本")
    public Result<String> uploadAudioAndRecognize(@RequestParam("file") MultipartFile file) {
        try {
            // 1. 将 MultipartFile 转为字节流
            byte[] audioBytes = file.getBytes();

            // 2. 调用 Coze 语音识别 API
            String recognizedText = cozeService.cozeSpeechToText(audioBytes, file.getOriginalFilename(), DEFAULT_APP_KEY);

            // 3. 返回识别结果
            return Result.OK(safeUrlDecode(recognizedText));
        } catch (Exception e) {
            log.error("语音识别失败", e);
            return Result.error("语音识别失败: " + e.getMessage());
        }
    }

    public static String safeUrlDecode(String text){
        if(text != null&& text.contains("%")){
            try{
                return URLDecoder.decode(text,StandardCharsets.UTF_8.name());
            }catch (Exception e){
                return text;
            }
        }
        return text;
    }


    /**
     * 创建用户消息
     */
    @PostMapping("/message/create/user")
    @ApiOperation(value = "创建用户消息", notes = "在指定会话中创建用户消息")
    public Result<JSONObject> createUserMessage(@RequestBody UserMessageDTO request) {
        try {
            String conversationId = request.getConversationId();
            String content = request.getContent();

            if (StringUtils.isBlank(conversationId)) {
                return Result.error("会话ID不能为空");
            }

            if (StringUtils.isBlank(content)) {
                return Result.error("消息内容不能为空");
            }

            JSONObject response = CozeUtil.createUserMessage(conversationId, content, DEFAULT_APP_KEY);

            if (CozeUtil.isResponseSuccess(response)) {
                log.info("创建用户消息成功，conversationId: {}", conversationId);
                return Result.OK(response);
            } else {
                log.error("创建用户消息失败，conversationId: {}, 响应: {}", conversationId, response);
                return Result.error("创建用户消息失败");
            }

        } catch (Exception e) {
            log.error("创建用户消息异常: {}", e.getMessage(), e);
            return Result.error("创建用户消息异常: " + e.getMessage());
        }
    }

    /**
     * 创建助手消息
     */
    @PostMapping("/message/create/assistant")
    @ApiOperation(value = "创建助手消息", notes = "在指定会话中创建助手消息")
    public Result<JSONObject> createAssistantMessage(@RequestBody UserMessageDTO request) {
        try {
            String conversationId = request.getConversationId();
            String content = request.getContent();

            if (StringUtils.isBlank(conversationId)) {
                return Result.error("会话ID不能为空");
            }

            if (StringUtils.isBlank(content)) {
                return Result.error("消息内容不能为空");
            }

            JSONObject response = CozeUtil.createAssistantMessage(conversationId, content, DEFAULT_APP_KEY);

            if (CozeUtil.isResponseSuccess(response)) {
                log.info("创建助手消息成功，conversationId: {}", conversationId);
                return Result.OK(response);
            } else {
                log.error("创建助手消息失败，conversationId: {}, 响应: {}", conversationId, response);
                return Result.error("创建助手消息失败");
            }

        } catch (Exception e) {
            log.error("创建助手消息异常: {}", e.getMessage(), e);
            return Result.error("创建助手消息异常: " + e.getMessage());
        }
    }

    /**
     * 发起对话 (V3 API)
     */
    @PostMapping("/chat/start")
    @ApiOperation(value = "发起对话", notes = "使用V3 API发起新的对话并返回AI回复和消息历史")
    public Result<Map<String, Object>> startChat(@RequestBody ChatMessageDTO request) {
        long startTime = System.currentTimeMillis();
        String traceId = "CHAT_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();

        try {
            log.info("[{}] === 开始处理聊天请求 ===", traceId);
            log.info("[{}] 请求参数: {}", traceId, request);

            String userId = request.getUserId();
            String message = request.getMessage();
            String existingConversationId = request.getConversationId();
            String botId = DEFAULT_BOT_ID;

            // 参数验证
            if (StringUtils.isBlank(userId)) {
                return Result.error("用户ID不能为空");
            }
            if (StringUtils.isBlank(message)) {
                return Result.error("消息内容不能为空");
            }

            Map<String, Object> enhancedResponse = new HashMap<>();

            // 1. 发起对话并发送消息
            JSONObject chatResponse = CozeUtil.startChatWithMessage(botId, userId, message, existingConversationId, DEFAULT_APP_KEY);
            if (!CozeUtil.isResponseSuccess(chatResponse)) {
                String errorMsg = chatResponse != null ? chatResponse.getString("msg") : "API调用失败";
                return Result.error("发起对话失败: " + (errorMsg != null ? errorMsg : "未知错误"));
            }

            // 2. 提取会话ID和对话ID
            String conversationId = null;
            String chatId = null;
            if (chatResponse.containsKey("data")) {
                JSONObject data = chatResponse.getJSONObject("data");
                conversationId = data.getString("conversation_id");
                chatId = data.getString("id");
            }
            if (StringUtils.isBlank(conversationId) || StringUtils.isBlank(chatId)) {
                return Result.error("会话信息无效");
            }

            // 3. 等待AI回复完成
            JSONObject waitResult = waitForChatCompletion(conversationId, chatId, traceId);
            boolean chatCompleted = false;
            if (waitResult != null && "completed".equals(waitResult.getString("status"))) {
                chatCompleted = true;
            }

            String aiReply;
            if (!chatCompleted) {
                enhancedResponse.put("chatCompletion", false);
            } else {
                enhancedResponse.put("chatCompletion", true);
            }

            // 4. 获取消息历史（上下文）
            JSONObject historyDetails = CozeUtil.getConversationHistory(conversationId, DEFAULT_APP_KEY);

            // 5. 提取AI回复
            aiReply = extractAIReply(historyDetails);

            // 6. 组装返回
            enhancedResponse.put("historyDetails", historyDetails);
            enhancedResponse.put("chatResponse", chatResponse);
            enhancedResponse.put("conversationId", conversationId);
            enhancedResponse.put("chatId", chatId);
            enhancedResponse.put("userId", userId);
            enhancedResponse.put("botId", botId);
            enhancedResponse.put("userMessage", message);
            enhancedResponse.put("aiReply", aiReply);
            enhancedResponse.put("success", true);
            enhancedResponse.put("timestamp", System.currentTimeMillis());
            enhancedResponse.put("traceId", traceId);
            enhancedResponse.put("totalDuration", System.currentTimeMillis() - startTime);

            return Result.OK(enhancedResponse);
        } catch (Exception e) {
            long totalDuration = System.currentTimeMillis() - startTime;
            log.error("[{}] 发起对话异常，总耗时: {}ms，异常详情: ", traceId, totalDuration, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            errorResponse.put("errorType", e.getClass().getSimpleName());
            errorResponse.put("traceId", traceId);
            errorResponse.put("timestamp", System.currentTimeMillis());
            return Result.error("发起对话异常: " + e.getMessage());
        }
    }

    private String extractAIReply(JSONObject historyDetails) {
        if (historyDetails == null || !historyDetails.containsKey("data")) {
            return null;
        }
        JSONArray data = historyDetails.getJSONArray("data");
        // 倒序查找最新的AI回复
        for (int i = data.size() - 1; i >= 0; i--) {
            JSONObject msg = data.getJSONObject(i);
            if ("assistant".equals(msg.getString("role"))) {
                return msg.getString("content");
            }
        }
        return null;
    }

    /**
     * SSE流式对话接口，提升AI对话实时体验
     */
    @PostMapping(value = "/chat/start-sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation(value = "发起对话（SSE流式）", notes = "使用V3 API发起新的对话并通过SSE流式返回AI回复进度和最终内容")
    public SseEmitter startChatSse(@RequestBody ChatMessageDTO request) {
        SseEmitter emitter = new SseEmitter(60000L); // 60秒超时
        String traceId = "CHAT_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            try {
                log.info("[{}] === [SSE] 开始处理聊天请求 ===", traceId);
                log.info("[{}] [SSE] 请求参数: {}", traceId, request);

                String userId = request.getUserId();
                String message = request.getMessage();
                String existingConversationId = request.getConversationId();
                String botId = DEFAULT_BOT_ID;

                // 参数验证
                if (StringUtils.isBlank(userId)) {
                    emitter.send(SseEmitter.event().data("error: 用户ID不能为空"));
                    emitter.complete();
                    return;
                }
                if (StringUtils.isBlank(message)) {
                    emitter.send(SseEmitter.event().data("error: 消息内容不能为空"));
                    emitter.complete();
                    return;
                }

                // 步骤1: 发起对话并发送消息
                JSONObject chatResponse = CozeUtil.startChatWithMessage(botId, userId, message, existingConversationId, DEFAULT_APP_KEY);
                if (!CozeUtil.isResponseSuccess(chatResponse)) {
                    emitter.send(SseEmitter.event().data("error: 发起对话失败"));
                    emitter.complete();
                    return;
                }
                JSONObject data = chatResponse.getJSONObject("data");
                String conversationId = data.getString("conversation_id");
                String chatId = data.getString("id");

                // 步骤2: 轮询AI回复进度，实时推送进展
                int retryCount = 0;
                boolean completed = false;
                while (retryCount < MAX_RETRY_COUNT * 3) { // 总时长提升到原来的3倍
                    JSONObject chatDetails = CozeUtil.retrieveChatDetails(conversationId, chatId, DEFAULT_APP_KEY);
                    if (chatDetails != null && chatDetails.containsKey("data")) {
                        String status = chatDetails.getJSONObject("data").getString("status");
                        if ("completed".equals(status)) {
                            // 获取AI回复内容
                            JSONObject history = CozeUtil.getConversationHistory(conversationId, DEFAULT_APP_KEY);
                            String aiReply = "";
                            if (history != null && history.containsKey("data")) {
                                JSONArray messages = history.getJSONArray("data");
                                for (int i = messages.size() - 1; i >= 0; i--) {
                                    JSONObject msg = messages.getJSONObject(i);
                                    if ("assistant".equals(msg.getString("role"))) {
                                        aiReply = msg.getString("content");
                                        break;
                                    }
                                }
                            }
                            emitter.send(SseEmitter.event().data(aiReply));
                            completed = true;
                            break;
                        } else if ("failed".equals(status)) {
                            emitter.send(SseEmitter.event().data("error: AI生成失败"));
                            completed = true;
                            break;
                        } else {
                            emitter.send(SseEmitter.event().data("progress: in_progress"));
                        }
                    } else {
                        emitter.send(SseEmitter.event().data("progress: waiting"));
                    }
                    Thread.sleep(RETRY_INTERVAL_MS);
                    retryCount++;
                }
                if (!completed) {
                    emitter.send(SseEmitter.event().data("error: 超时未完成"));
                }
                emitter.complete();
            } catch (Exception e) {
                try {
                    emitter.send(SseEmitter.event().data("error: " + e.getMessage()));
                } catch (IOException ignored) {
                }
                emitter.complete();
            }
        }).start();
        return emitter;
    }

    /**
     * 查看对话详情
     */
    @GetMapping("/chat/details")
    @ApiOperation(value = "查看对话详情", notes = "根据会话ID和对话ID获取对话的详细信息和状态")
    public Result<JSONObject> getChatDetails(
            @ApiParam(value = "会话ID", required = true) @RequestParam String conversationId,
            @ApiParam(value = "对话ID", required = true) @RequestParam String chatId) {

        String traceId = "CHAT_DETAILS_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();

        try {
            log.info("[{}] === 开始获取对话详情 ===", traceId);
            log.info("[{}] 请求参数 - conversationId: {}, chatId: {}", traceId, conversationId, chatId);

            // 参数验证
            if (StringUtils.isBlank(conversationId)) {
                log.error("[{}] 参数验证失败: 会话ID为空", traceId);
                return Result.error("会话ID不能为空");
            }
            if (StringUtils.isBlank(chatId)) {
                log.error("[{}] 参数验证失败: 对话ID为空", traceId);
                return Result.error("对话ID不能为空");
            }

            log.info("[{}] 参数验证通过，调用CozeUtil.retrieveChatDetails", traceId);

            // 调用CozeUtil获取对话详情
            JSONObject response = CozeUtil.retrieveChatDetails(conversationId, chatId, DEFAULT_APP_KEY);

            // 检查响应是否成功
            if (CozeUtil.isResponseSuccess(response)) {
                log.info("[{}] 获取对话详情成功 - conversationId: {}, chatId: {}", traceId, conversationId, chatId);

                // 记录对话状态信息
                if (response.containsKey("data")) {
                    JSONObject data = response.getJSONObject("data");
                    String status = data.getString("status");
                    String createdAt = data.getString("created_at");
                    String completedAt = data.getString("completed_at");

                    log.info("[{}] 对话状态详情 - status: {}, created_at: {}, completed_at: {}",
                            traceId, status, createdAt, completedAt);
                }

                return Result.OK(response);
            } else {
                log.error("[{}] 获取对话详情失败 - conversationId: {}, chatId: {}, 响应: {}",
                        traceId, conversationId, chatId, response.toJSONString());

                // 提取错误信息
                String errorMsg = "获取对话详情失败";
                if (response.containsKey("msg")) {
                    errorMsg = response.getString("msg");
                } else if (response.containsKey("message")) {
                    errorMsg = response.getString("message");
                }

                return Result.error(errorMsg);
            }

        } catch (Exception e) {
            log.error("[{}] 获取对话详情异常 - conversationId: {}, chatId: {}, 异常: {}",
                    traceId, conversationId, chatId, e.getMessage(), e);
            return Result.error("获取对话详情异常: " + e.getMessage());
        }
    }

    /**
     * 轮询等待对话完成
     */
    private JSONObject waitForChatCompletion(String conversationId, String chatId, String traceId) {
        int retryCount = 0;
        long pollStartTime = System.currentTimeMillis();

        log.info("[{}] 开始轮询对话状态，conversationId: {}, chatId: {}", traceId, conversationId, chatId);

        while (retryCount < MAX_RETRY_COUNT) {
            try {
                // 等待一段时间再查询
                if (retryCount > 0) {
                    log.debug("[{}] 第{}次重试前等待{}ms", traceId, retryCount, RETRY_INTERVAL_MS);
                    Thread.sleep(RETRY_INTERVAL_MS);
                }

                long queryStart = System.currentTimeMillis();
                JSONObject chatDetails = CozeUtil.retrieveChatDetails(conversationId, chatId, DEFAULT_APP_KEY);
                long queryDuration = System.currentTimeMillis() - queryStart;

                log.info("[{}] 第{}次状态查询完成，耗时: {}ms", traceId, retryCount + 1, queryDuration);

                if (chatDetails == null) {
                    log.warn("[{}] 第{}次查询返回null，可能原因: API异常、网络问题、参数错误",
                            traceId, retryCount + 1);
                    retryCount++;
                    continue;
                }

                log.debug("[{}] 第{}次查询响应: {}", traceId, retryCount + 1, chatDetails.toJSONString());

                if (CozeUtil.isResponseSuccess(chatDetails)) {
                    if (chatDetails.containsKey("data")) {
                        JSONObject data = chatDetails.getJSONObject("data");
                        String status = data.getString("status");

                        log.info("[{}] 第{}次查询 - 对话状态: {}, 累计耗时: {}ms",
                                traceId, retryCount + 1, status, System.currentTimeMillis() - pollStartTime);

                        if ("completed".equals(status)) {
                            log.info("[{}] 对话已完成！总轮询次数: {}, 总耗时: {}ms",
                                    traceId, retryCount + 1, System.currentTimeMillis() - pollStartTime);

                            // 记录完成状态的详细信息
                            JSONObject usage = data.getJSONObject("usage");
                            if (usage != null) {
                                log.info("[{}] 对话使用统计: {}", traceId, usage.toJSONString());
                            }

                            return chatDetails;

                        } else if ("failed".equals(status)) {
                            JSONObject lastError = data.getJSONObject("last_error");
                            log.error("[{}] 对话失败！状态: {}, 错误详情: {}, 轮询次数: {}, 耗时: {}ms",
                                    traceId, status, lastError, retryCount + 1,
                                    System.currentTimeMillis() - pollStartTime);

                            // 解析具体的失败原因
                            if (lastError != null) {
                                String errorCode = lastError.getString("code");
                                String errorMsg = lastError.getString("msg");
                                log.error("[{}] 失败原因 - code: {}, msg: {}", traceId, errorCode, errorMsg);
                            }

                            return chatDetails; // 返回失败状态的详情

                        } else if ("in_progress".equals(status)) {
                            log.debug("[{}] 对话进行中，继续等待... 当前进度: {}%",
                                    traceId, (retryCount + 1) * 100 / MAX_RETRY_COUNT);
                            // 继续循环
                        } else {
                            log.warn("[{}] 未知对话状态: {}, 继续等待", traceId, status);
                        }
                    } else {
                        log.warn("[{}] 响应中缺少data字段: {}", traceId, chatDetails.toJSONString());
                    }
                } else {
                    log.warn("[{}] 第{}次查询API调用失败: {}", traceId, retryCount + 1, chatDetails.toJSONString());

                    // 解析API错误
                    String errorCode = chatDetails.getString("code");
                    String errorMsg = chatDetails.getString("msg");
                    log.warn("[{}] API错误 - code: {}, msg: {}", traceId, errorCode, errorMsg);

                    // 对于4001错误（Chat ID无效），立即停止重试
                    if ("4001".equals(errorCode)) {
                        log.error("[{}] Chat ID无效错误，停止轮询。可能原因: 对话创建失败或ID错误", traceId);
                        return chatDetails; // 返回错误信息
                    }
                }
                retryCount++;

            } catch (InterruptedException e) {
                log.error("[{}] 轮询被中断，当前重试次数: {}, 耗时: {}ms",
                        traceId, retryCount, System.currentTimeMillis() - pollStartTime);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("[{}] 第{}次轮询异常，异常类型: {}, 异常信息: {}",
                        traceId, retryCount + 1, e.getClass().getSimpleName(), e.getMessage(), e);
                retryCount++;
            }
        }

        long totalPollTime = System.currentTimeMillis() - pollStartTime;
        log.error("[{}] 对话轮询超时！最大重试次数: {}, 实际重试次数: {}, 总耗时: {}ms",
                traceId, MAX_RETRY_COUNT, retryCount, totalPollTime);

        return null;
    }

    /**
     * 创建会话 (V1 API)
     */
    @PostMapping("/conversation/create")
    @ApiOperation(value = "创建会话", notes = "使用V1 API创建新会话")
    public Result<JSONObject> createConversation() {
        try {
            JSONObject response = CozeUtil.createConversation(DEFAULT_APP_KEY);

            if (CozeUtil.isResponseSuccess(response)) {
                log.info("创建会话成功");
                return Result.OK(response);
            } else {
                log.error("创建会话失败，响应: {}", response);
                return Result.error("创建会话失败");
            }

        } catch (Exception e) {
            log.error("创建会话异常: {}", e.getMessage(), e);
            return Result.error("创建会话异常: " + e.getMessage());
        }
    }

    /**
     * 获取会话历史
     */
    @GetMapping("/conversation/{conversationId}/history")
    @ApiOperation(value = "获取指定会话的消息历史", notes = "获取指定会话的消息历史")
    public Result<JSONObject> getConversationHistory(
            @ApiParam(value = "会话ID") @PathVariable String conversationId) {
        try {
            if (StringUtils.isBlank(conversationId)) {
                return Result.error("会话ID不能为空");
            }

            JSONObject response = CozeUtil.getConversationHistory(conversationId, null);

            if (CozeUtil.isResponseSuccess(response)) {
                log.info("获取会话历史成功，conversationId: {}", conversationId);
                return Result.OK(response);
            } else {
                log.error("获取会话历史失败，conversationId: {}, 响应: {}", conversationId, response);
                return Result.error("获取会话历史失败");
            }

        } catch (Exception e) {
            log.error("获取会话历史异常: {}", e.getMessage(), e);
            return Result.error("获取会话历史异常: " + e.getMessage());
        }
    }
}
