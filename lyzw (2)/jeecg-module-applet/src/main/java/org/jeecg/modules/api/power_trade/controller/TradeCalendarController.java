package org.jeecg.modules.api.power_trade.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power_trade.service.MultiDataSourceAggregationService;
import org.jeecg.modules.api.power_trade.util.ParamValidationUtil;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.service.ITradeCalendarRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交易日历控制器
 */
@Slf4j
@RestController
@Api(tags = "电力交易-交易日历")
@RequestMapping("/api/power_trade/calendar")
public class TradeCalendarController {

    @Autowired
    private ITradeCalendarRecordService tradeCalendarRecordService;

    @Autowired
    private MultiDataSourceAggregationService multiDataSourceAggregationService;

    /**
     * 查询交易日历数据
     *
     * @param date 日期 yyyy-MM-dd
     * @return 交易日历数据
     */
    @GetMapping("/queryByDate")
    @ApiOperation(value = "查询交易日历",
            notes = "根据日期和省份ID查询交易日历数据（省份ID: 0-全国, 1-江苏, 2-安徽）")
    public Result<List<TradeCalendarRecord>> queryByDate(
            @ApiParam(value = "日期，格式: yyyy-MM-dd（可选）")
            @RequestParam(required = false) String date,

            @ApiParam(value = "省份ID (0-全国, 1-江苏, 2-安徽)", required = true)
            @RequestParam Integer provinceId) {

        // 参数验证
        Result<Void> validationResult = ParamValidationUtil.validateVoid(() -> {
            ParamValidationUtil.Validator.create().validateProvinceId(provinceId);
        });
        if (!validationResult.isSuccess()) {
            return Result.error(validationResult.getMessage());
        }

        // 检查是否为全国数据源汇总
        if (provinceId == 0) {
            // 全国数据源汇总模式
            List<Map<String, Object>> aggregatedResult = multiDataSourceAggregationService
                    .aggregateAllProvincesTradeCalendar(date);
            // 转换为TradeCalendarRecord格式返回
            List<TradeCalendarRecord> calendarRecords = convertToTradeCalendarRecords(aggregatedResult);
            return Result.OK(calendarRecords);
        }

        // 单省份模式
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return Result.error("不支持的省份ID: " + provinceId);
        }
        DynamicDataSourceContextHolder.push(dsKey);

        try {
            log.info("查询交易日历 - 省份ID: {}, 日期: {}", provinceId, date != null ? date : "全部");

            // 根据省份ID选择不同的查询策略
            List<TradeCalendarRecord> tradeCalendarRecords = queryTradeCalendarByProvince(date, provinceId);

            log.info("查询完成 - 省份ID: {}, 返回记录数: {}", provinceId,
                    tradeCalendarRecords != null ? tradeCalendarRecords.size() : 0);

            return Result.OK(tradeCalendarRecords);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    private List<TradeCalendarRecord> queryTradeCalendarByProvince(String date, Integer provinceId) {
        switch (provinceId) {
            case 1:  // 江苏省
                return tradeCalendarRecordService.getCalendarWithTradeType(date, provinceId);
            case 2:  // 安徽省
                return tradeCalendarRecordService.getCalendarWithTargetDateAndTradeType(date, provinceId);
            default:
                throw new UnsupportedOperationException("不支持的省份ID: " + provinceId);
        }
    }

    /**
     * 将汇总结果转换为TradeCalendarRecord列表
     */
    private List<TradeCalendarRecord> convertToTradeCalendarRecords(List<Map<String, Object>> aggregatedResult) {
        if (aggregatedResult == null || aggregatedResult.isEmpty()) {
            return new ArrayList<>();
        }

        return aggregatedResult.stream()
                .map(this::convertToTradeCalendarRecord)
                .collect(Collectors.toList());
    }

    /**
     * 将单个Map转换为TradeCalendarRecord
     */
    private TradeCalendarRecord convertToTradeCalendarRecord(Map<String, Object> data) {
        if (data == null) {
            return null;
        }

        TradeCalendarRecord record = new TradeCalendarRecord();
        
        // 映射基本字段
        record.setTradeDate((Date) data.get("date"));
        
        // 处理交易类型列表，转换为字符串
        @SuppressWarnings("unchecked")
        List<String> tradeTypes = (List<String>) data.get("tradeTypes");
        if (tradeTypes != null && !tradeTypes.isEmpty()) {
            record.setTradeType(String.join(",", tradeTypes));
        }
        
        // 设置事件数量作为备注
        Integer eventCount = (Integer) data.get("eventCount");
        if (eventCount != null) {
            record.setRemark("共" + eventCount + "个交易事件");
        }
        
        // 设置省份ID为0（表示全国汇总）
        record.setProvinceId(0);
        
        // 设置创建时间为当前时间
        record.setCreateTime(new Date());
        
        return record;
    }
}