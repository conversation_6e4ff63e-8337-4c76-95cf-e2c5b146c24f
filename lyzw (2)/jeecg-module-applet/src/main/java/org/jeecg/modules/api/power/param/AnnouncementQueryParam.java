package org.jeecg.modules.api.power.param;

import lombok.Data;

/**
 * 公告查询参数类
 */
@Data
public class AnnouncementQueryParam {

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 公告类型 (1-政策文件, 2-市场公告)
     */
    private Integer type;

    /**
     * 标题关键词
     */
    private String keyword;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;
} 