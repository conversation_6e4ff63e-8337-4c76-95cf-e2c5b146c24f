package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.vo.PowerDashboardVO;
import org.jeecg.modules.api.power_trade.vo.StationTypeStatisticsVO;

import java.util.List;

/**
 * 电力交易电站Mapper接口
 */
@Mapper
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 根据电站ID列表聚合查询交易指标数据
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份
     * @return 聚合后的交易指标数据
     */
    PowerDashboardVO getAggregatedTradeDataByStationIds(
        @Param("stationIds") List<Long> stationIds,
        @Param("year") String year,
        @Param("month") String month
    );

    /**
     * 根据区域代码聚合查询交易指标数据
     * @param provinceId 区域代码
     * @param year 年份
     * @param month 月份
     * @return 聚合后的交易指标数据
     */
    PowerDashboardVO getAggregatedTradeDataByRegion(
        @Param("provinceId") Integer provinceId,
        @Param("year") String year,
        @Param("month") String month
    );

    /**
     * 查询电站类型统计信息
     * @param stationIds 电站ID列表
     * @param year 年份
     * @param month 月份
     * @return 各类型电站统计
     */
    List<StationTypeStatisticsVO> getStationTypeStatistics(
        @Param("stationIds") List<Long> stationIds,
        @Param("year") String year,
        @Param("month") String month
    );

    /**
     * 根据区域代码查询电站ID列表
     * @param provinceId 区域代码
     * @return 电站ID列表
     */
    List<Long> getStationIdsByProvinceId(@Param("provinceId") Integer provinceId);

    /**
     * 查询指定电站的基本信息和容量
     * @param stationIds 电站ID列表
     * @return 电站基本信息列表
     */
    List<Station> getStationBasicInfoByIds(@Param("stationIds") List<Long> stationIds);

}