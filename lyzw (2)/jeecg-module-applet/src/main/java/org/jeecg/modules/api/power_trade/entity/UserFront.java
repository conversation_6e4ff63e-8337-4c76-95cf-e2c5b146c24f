package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("sys_user")
public class UserFront {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @TableField("person_number")
    @ApiModelProperty(value = "用户工号", example = "U20230001")
    private String personNumber;

    @TableField("name")
    @ApiModelProperty(value = "用户姓名", example = "张三")
    private String name;

    @TableField("password")
    @ApiModelProperty(value = "用户密码(加密存储)", example = "加密后的密码字符串")
    private String password;

    @TableField("status")
    @ApiModelProperty(value = "用户状态(0:停用,1:启用)", example = "1")
    private Integer status;

    @TableField("wechat_status")
    @ApiModelProperty(value = "是否允许登录")
    private Integer wechatStatus;

    @TableField("pwd_update_status")
    @ApiModelProperty(value = "密码修改状态(0:未修改过,1:修改过)", example = "1")
    private Integer pwdUpdateStatus;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;

    @TableField("create_by")
    @ApiModelProperty(value = "创建人", example = "admin")
    private String createBy;

    @TableField("update_by")
    @ApiModelProperty(value = "更新人", example = "admin")
    private String updateBy;

    @TableField("mac_key")
    @ApiModelProperty(value = "MAC地址(首次使用UKey登录时保存)", example = "00-1A-2B-3C-4D-5E")
    private String macKey;

    @TableField("public_key")
    @ApiModelProperty(value = "公钥(Base64编码)", example = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...")
    private byte[] publicKey;

    @TableField("private_key")
    @ApiModelProperty(value = "私钥(Base64编码)", example = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...")
    private byte[] privateKey;
}