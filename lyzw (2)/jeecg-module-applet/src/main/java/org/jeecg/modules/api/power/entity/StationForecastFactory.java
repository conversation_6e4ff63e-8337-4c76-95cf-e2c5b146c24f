package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("bi.station_forecast_factory")
public class StationForecastFactory {

    @TableId(value = "station_id", type = IdType.INPUT)
    private Long stationId;

    @TableField("factory_id")
    private Integer factoryId;

    @TableField(exist = false)
    private String factoryName;
}
