package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "省份信息")
public class ProvinceDTO {

    @ApiModelProperty(value = "省份ID", example = "1")
    private Integer provinceId;

    @ApiModelProperty(value = "数据源标识", example = "dataSource1")
    private String dataSource;

    @ApiModelProperty(value = "省份名称", example = "北京市")
    private String provinceName;

}