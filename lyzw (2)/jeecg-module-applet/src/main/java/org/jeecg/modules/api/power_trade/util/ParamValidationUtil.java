package org.jeecg.modules.api.power_trade.util;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;

import java.util.regex.Pattern;

/**
 * 参数验证工具类
 * 使用链式调用和异常处理来减少if语句
 */
public class ParamValidationUtil {

    // 日期格式正则表达式
    private static final Pattern YEAR_PATTERN = Pattern.compile("^\\d{4}$");
    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$");
    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");

    /**
     * 参数验证异常
     */
    public static class ValidationException extends RuntimeException {
        public ValidationException(String message) {
            super(message);
        }
    }

    /**
     * 验证器链式调用类
     */
    public static class Validator {
        
        public static Validator create() {
            return new Validator();
        }

        public Validator validateProvinceId(Integer provinceId) {
            if (provinceId == null) throw new ValidationException("省份ID不能为空");
            if (provinceId < 0) throw new ValidationException("省份ID不能为负数");
            return this;
        }

        public Validator validatePagination(Integer pageNo, Integer pageSize) {
            if (pageNo != null && pageNo < 1) throw new ValidationException("页码必须大于0");
            if (pageSize != null && (pageSize < 1 || pageSize > 1000)) throw new ValidationException("每页条数必须在1-1000之间");
            return this;
        }

        public Validator validateRequired(Object value, String fieldName) {
            if (value == null || (value instanceof String && oConvertUtils.isEmpty((String) value))) {
                throw new ValidationException(fieldName + "不能为空");
            }
            return this;
        }

        public Validator validateStationId(Long stationId) {
            if (stationId == null || stationId <= 0) throw new ValidationException("电站ID必须为正数");
            return this;
        }

        public Validator validateYearMonth(String year, String month) {
            validateRequired(year, "年份");
            validateRequired(month, "月份");
            if (!YEAR_PATTERN.matcher(year).matches()) throw new ValidationException("年份格式错误，应为：yyyy");
            if (!month.matches("^(0[1-9]|1[0-2])$")) throw new ValidationException("月份格式错误，应为：01-12");
            return this;
        }

        public String validateTimeDimension(String dimension) {
            if (oConvertUtils.isEmpty(dimension)) throw new ValidationException("时间维度不能为空");
            String trimmedDimension = dimension.trim();
            if (!"1".equals(trimmedDimension) && !"2".equals(trimmedDimension) && !"3".equals(trimmedDimension)) {
                throw new ValidationException("时间维度只能是 1(年)、2(月)、3(日)");
            }
            return trimmedDimension;
        }

        public Validator validateDateFormat(String date, String dimension) {
            if (oConvertUtils.isEmpty(date)) throw new ValidationException("日期不能为空");

            boolean isValid = false;
            String expectedFormat = "";

            switch (dimension) {
                case "1":
                    isValid = YEAR_PATTERN.matcher(date).matches();
                    expectedFormat = "yyyy";
                    break;
                case "2":
                    isValid = MONTH_PATTERN.matcher(date).matches();
                    expectedFormat = "yyyy-MM";
                    break;
                case "3":
                    isValid = DATE_PATTERN.matcher(date).matches();
                    expectedFormat = "yyyy-MM-dd";
                    break;
            }

            if (!isValid) throw new ValidationException("日期格式错误，应为：" + expectedFormat);
            return this;
        }

        public Validator validateQueryDimension(Integer dimension) {
            if (dimension == null) throw new ValidationException("查询维度不能为空");
            if (dimension != 1 && dimension != 2) throw new ValidationException("查询维度参数错误，只能是1(月度)或2(年度)");
            return this;
        }

        public Validator validateMonthQuery(String month) {
            if (oConvertUtils.isEmpty(month)) throw new ValidationException("月份参数不能为空");
            if (!MONTH_PATTERN.matcher(month).matches()) throw new ValidationException("月份格式错误，应为yyyy-MM");
            return this;
        }

        public Validator validateYearQuery(String year) {
            if (oConvertUtils.isEmpty(year)) throw new ValidationException("年份参数不能为空");
            if (!YEAR_PATTERN.matcher(year).matches()) throw new ValidationException("年份格式错误，应为yyyy");
            return this;
        }
    }

    /**
     * 执行验证并返回Result（带返回值）
     */
    public static <T> Result<T> validate(ValidationSupplier<T> supplier) {
        try {
            return Result.OK(supplier.get());
        } catch (ValidationException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 执行验证（无返回值）
     */
    public static Result<Void> validateVoid(ValidationRunnable runnable) {
        try {
            runnable.run();
            return Result.OK();
        } catch (ValidationException e) {
            return Result.error(e.getMessage());
        }
    }

    @FunctionalInterface
    public interface ValidationSupplier<T> {
        T get() throws ValidationException;
    }

    @FunctionalInterface
    public interface ValidationRunnable {
        void run() throws ValidationException;
    }
}