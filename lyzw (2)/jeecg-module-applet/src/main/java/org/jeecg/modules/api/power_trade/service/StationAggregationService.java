package org.jeecg.modules.api.power_trade.service;

import org.jeecg.modules.api.power_trade.vo.PowerDashboardVO;
import org.jeecg.modules.api.power_trade.vo.StationTypeStatisticsVO;

import java.util.List;

/**
 * 电站数据聚合服务接口
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface StationAggregationService {

    PowerDashboardVO getAggregatedTradeDataByStationIds(List<Long> stationIds, String year, String month);

    PowerDashboardVO getAggregatedTradeDataByRegion(Integer provinceId, String year, String month);

    List<StationTypeStatisticsVO> getStationTypeStatistics(List<Long> stationIds, String year, String month);

    List<Long> getStationIdsByProvinceId(Integer provinceId);
}
