package org.jeecg.modules.api.power.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.power.entity.RpJiaYue;

import java.util.List;

@Mapper
public interface JiaYueRpMapper extends BaseMapper<RpJiaYue> {

    @Select({
            "<script>",
            "SELECT t.* ",
            "FROM forecast_power_report.rp t ",
            "JOIN (",
            "    SELECT `date`, MAX(version) AS max_version ",
            "    FROM forecast_power_report.rp ",
            "    WHERE station_id IN ",
            "    <foreach item='sid' collection='stationNos' open='(' separator=',' close=')'>",
            "        #{sid}",
            "    </foreach>",
            "    AND `date` IN ",
            "    <foreach item='date' collection='dates' open='(' separator=',' close=')'>",
            "        #{date}",
            "    </foreach>" +
            "    AND `type` = #{type}",
            "    GROUP BY `date`",
            ") mv ON t.date = mv.date AND t.version = mv.max_version ",
            "WHERE t.station_id IN ",
            "<foreach item='sid' collection='stationNos' open='(' separator=',' close=')'>",
            "    #{sid}",
            "</foreach>" +
            "AND `type` = #{type}",
            "ORDER BY t.date ASC, t.time ASC",
            "</script>"
    })
    List<RpJiaYue> selectMaxVersionByDatesAndStation(
            @Param("dates") List<String> dates,  // 日期列表（如 ["2025-01-01", "2025-01-02"]）
            @Param("stationNos") List<String> stationNos,
            @Param("type") Integer type
    );

    @Select({
            "<script>",
            "SELECT t.* ",
            "FROM forecast_power_report.rp t ",
            "JOIN (",
            "    SELECT `date`, MAX(version) AS max_version ",
            "    FROM forecast_power_report.rp ",
            "    WHERE station_id IN ",
            "    <foreach item='sid' collection='stationNos' open='(' separator=',' close=')'>",
            "        #{sid}",
            "    </foreach>",
            "    AND `date` BETWEEN #{startDate} AND #{endDate}" +
                    "    AND `type` = #{type}",
            "    GROUP BY date",
            ") mv ON t.date = mv.date AND t.version = mv.max_version ",
            "WHERE t.station_id IN ",
            "<foreach item='sid' collection='stationNos' open='(' separator=',' close=')'>",
            "    #{sid}",
            "</foreach>" +
            "AND `type` = #{type}",
            "ORDER BY t.date ASC, t.time ASC",
            "</script>"
    })
    List<RpJiaYue> selectMaxVersionByDateRangeAndStation(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("stationNos") List<String> stationNos,
            @Param("type") Integer type
    );
}
