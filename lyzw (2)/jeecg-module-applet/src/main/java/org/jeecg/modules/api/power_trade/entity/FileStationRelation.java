package org.jeecg.modules.api.power_trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("file_station_relation")
@ApiModel(value = "FileStationRelation", description = "文件关联电站表")
public class FileStationRelation {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "文件在minio地址", example = "http://minio.example.com/file.xlsx")
    private String fileUrl;

    @ApiModelProperty(value = "关联电站ID", example = "1001")
    private Long stationId;

    @ApiModelProperty(value = "文件类型(1:省内日清分明细结算单, 2:日省内日清分结算单, 3:月统推发电侧结算单, 4:月统推独立储能结算单)", example = "3")
    private Integer type;

    @ApiModelProperty(value = "文件结算日期", example = "2024-01-01")
    private LocalDate settleDate;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}