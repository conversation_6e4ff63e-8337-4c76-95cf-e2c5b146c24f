package org.jeecg.modules.api.power.util;

/**
 * 字符串工具类
 */
public class StringUtils {
    
    /**
     * 从字符串中提取数值部分
     * @param str 需要提取的字符串
     * @return 数值字符串，如果没有数值则返回"0"
     */
    public static String extractNumericPart(String str) {
        if (str == null || str.trim().isEmpty()) {
            return "0";
        }
        
        // 移除所有非数字字符(保留小数点和负号)
        String numericPart = str.replaceAll("[^0-9.\\-]", "");
        
        // 处理多个小数点的情况，只保留第一个
        int firstDot = numericPart.indexOf('.');
        if (firstDot >= 0) {
            String beforeDot = numericPart.substring(0, firstDot + 1);
            String afterDot = numericPart.substring(firstDot + 1).replace(".", "");
            numericPart = beforeDot + afterDot;
        }
        
        // 处理多个负号的情况，只保留开头的负号
        if (numericPart.startsWith("-")) {
            numericPart = "-" + numericPart.substring(1).replace("-", "");
        } else {
            numericPart = numericPart.replace("-", "");
        }
        
        // 如果处理后为空，返回"0"
        if (numericPart.isEmpty() || numericPart.equals("-") || numericPart.equals(".") || numericPart.equals("-.")) {
            return "0";
        }
        
        return numericPart;
    }
} 