<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.PowerSideSettleMapper">

    <!-- 根据电站ID和时间范围获取累计结算电量和交易均价 -->
    <select id="getStationSettlementSummary" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementElectricity,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementElectricFee,
            CASE
                WHEN SUM(pss.settlement_electricity) > 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as avgTradePrice
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
          AND fsr.type = #{fileType}
          AND (
            (fsr.type IN (3, 4) AND DATE_FORMAT(fsr.settle_date, '%Y-%m') BETWEEN #{startYearMonth} AND #{endYearMonth})
            OR
            (fsr.type IN (1, 2) AND DATE(fsr.settle_date) BETWEEN #{startDate} AND #{endDate})
          )
    </select>

    <!-- 根据省份ID和时间范围获取所有电站的结算汇总数据 -->
    <select id="getProvinceStationSettlementSummary" resultType="java.util.Map">
        SELECT
            s.id as stationId,
            s.name as stationName,
            s.type as stationType,
            COALESCE(SUM(pss.settlement_electricity), 0) as totalSettlementElectricity,
            COALESCE(SUM(pss.settlement_electric_fee), 0) as totalSettlementElectricFee,
            CASE
                WHEN SUM(pss.settlement_electricity) > 0
                THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
                ELSE 0
            END as avgTradePrice
        FROM station s
        LEFT JOIN file_station_relation fsr ON s.id = fsr.station_id
        LEFT JOIN power_side_settle pss ON fsr.id = pss.file_id
        WHERE s.province_id = #{provinceId}
          AND (fsr.type = #{fileType} OR fsr.type IS NULL)
          AND (
            (fsr.type IN (3, 4) AND DATE_FORMAT(fsr.settle_date, '%Y-%m') BETWEEN #{startYearMonth} AND #{endYearMonth})
            OR
            (fsr.type IN (1, 2) AND DATE(fsr.settle_date) BETWEEN #{startDate} AND #{endDate})
            OR
            fsr.settle_date IS NULL
          )
        GROUP BY s.id, s.name, s.type
        ORDER BY s.id
    </select>

    <!-- 获取电站年度交易数据汇总 -->
    <select id="getStationYearlyTradeData" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND fsr.type IN (3, 4)
    </select>

    <!-- 获取电站月度交易数据汇总 -->
    <select id="getStationMonthlyTradeData" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND MONTH(fsr.settle_date) = #{month}
        AND fsr.type IN (3, 4)
    </select>

    <!-- 根据电站类型获取年度交易数据 -->
    <select id="getStationYearlyTradeDataByType" resultType="java.util.Map">
        SELECT
            SUM(settlement_electricity) as totalElectricity,
            SUM(settlement_electric_fee) as totalFee
        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        INNER JOIN station s ON fsr.station_id = s.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND s.type = #{stationType}
        AND fsr.type = #{settlementType}
    </select>

    <!-- 获取电站年度交易电量信息（用于电站详情） -->
    <select id="getStationYearlyTradingInfo" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(fsr.settle_date, '%Y-%m') as month,
        COALESCE(SUM(pss.settlement_electricity), 0) as monthlyElectricity,
        CASE
            WHEN SUM(pss.settlement_electricity) &gt; 0
            THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
            ELSE 0
        END as monthlyAveragePrice,
        COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlyTotalFee,
        COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
        COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
        COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
        COUNT(*) as recordCount

        FROM power_side_settle pss
        INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
        WHERE fsr.station_id = #{stationId}
        AND YEAR(fsr.settle_date) = #{year}
        AND fsr.type IN (3, 4)  <!-- 月统推结算单类型 -->
        AND pss.settlement_electricity IS NOT NULL
        AND pss.settlement_electricity &gt; 0
        GROUP BY DATE_FORMAT(fsr.settle_date, '%Y-%m')
        ORDER BY month ASC
    </select>

    <!-- 获取电站月度交易数据 -->
<select id="getStationMonthlyTradingInfo" resultType="java.util.Map">
    SELECT
        -- 月度交易总电量
        COALESCE(SUM(pss.settlement_electricity), 0) as monthlyTotalElectricity,
        
        -- 月度交易均价
        CASE
            WHEN SUM(pss.settlement_electricity) &gt; 0
            THEN SUM(pss.settlement_electric_fee) / SUM(pss.settlement_electricity)
            ELSE 0
        END as monthlyAveragePrice,
        
        -- 月度交易总电费
        COALESCE(SUM(pss.settlement_electric_fee), 0) as monthlyTotalFee,
        
        -- 实际上网电量
        COALESCE(SUM(pss.actual_internet_electricity), 0) as actualInternetElectricity,
        
        -- 合同电量
        COALESCE(SUM(pss.contract_electricity), 0) as contractElectricity,
        
        -- 偏差电量
        COALESCE(SUM(pss.deviation_electricity), 0) as deviationElectricity,
        
        -- 统计信息
        COUNT(*) as recordCount

    FROM power_side_settle pss
    INNER JOIN file_station_relation fsr ON pss.file_id = fsr.id
    WHERE fsr.station_id = #{stationId}
    AND DATE_FORMAT(fsr.settle_date, '%Y-%m') = #{yearMonth}  <!-- 关键：日期格式匹配 -->
    AND fsr.type IN (3, 4)  <!-- 月统推结算单类型 -->
    AND pss.settlement_electricity IS NOT NULL
    AND pss.settlement_electricity &gt; 0
</select>

</mapper>