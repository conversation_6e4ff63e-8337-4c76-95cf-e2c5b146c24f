package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("forecast_power_report.rp")
public class RpJiaYue {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("date")
    private String date;

    @TableField("time")
    private String time;

    @TableField("value")
    private Double value;

    @TableField("type")
    private Integer type;

    @TableField("station_id")
    private String stationId;

    @TableField("version")
    private String version;

    @TableField("update_time")
    private Date updateTime;
}
