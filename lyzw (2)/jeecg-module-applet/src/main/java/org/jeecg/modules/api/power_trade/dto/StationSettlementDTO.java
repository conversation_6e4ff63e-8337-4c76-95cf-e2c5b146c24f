package org.jeecg.modules.api.power_trade.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 电站结算数据DTO
 */
@ApiModel("电站结算数据DTO")
@Data
@NoArgsConstructor
public class StationSettlementDTO {

    @ApiModelProperty(value = "电站ID")
    private Long stationId;

    @ApiModelProperty(value = "电站名称")
    private String stationName;

    @ApiModelProperty(value = "电站类型", notes = "0-光伏, 1-风电, 3-储能")
    private Integer stationType;

    @ApiModelProperty(value = "累计结算电量(GWh)")
    private BigDecimal totalSettlementElectricity;

    @ApiModelProperty(value = "累计交易均价(元/kWh)")
    private BigDecimal avgTradePrice;

    @ApiModelProperty(value = "累计结算电费(万元)")
    private BigDecimal totalSettlementElectricFee;

    /**
     * 全参数构造函数
     */
    public StationSettlementDTO(Long stationId, String stationName, Integer stationType,
                               BigDecimal totalSettlementElectricity, BigDecimal avgTradePrice,
                               BigDecimal totalSettlementElectricFee) {
        this.stationId = stationId;
        this.stationName = stationName;
        this.stationType = stationType;
        this.totalSettlementElectricity = totalSettlementElectricity;
        this.avgTradePrice = avgTradePrice;
        this.totalSettlementElectricFee = totalSettlementElectricFee;
    }

    /**
     * 获取电站类型描述
     */
    public String getStationTypeDesc() {
        if (stationType == null) return "未知";
        switch (stationType) {
            case 0: return "光伏";
            case 1: return "风电";
            case 3: return "储能";
            default: return "其他";
        }
    }
}
