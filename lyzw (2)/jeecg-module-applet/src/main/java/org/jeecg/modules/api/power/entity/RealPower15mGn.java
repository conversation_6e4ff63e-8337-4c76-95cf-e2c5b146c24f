package org.jeecg.modules.api.power.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("forecast_power_report.realpower_15m_gn")
public class RealPower15mGn {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("station_id")
    private Long stationId;

    @TableField("date")
    private String date;

    @TableField("time")
    private String time;

    @TableField("value")
    private Double value;
}
