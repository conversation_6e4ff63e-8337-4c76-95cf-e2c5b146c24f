<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.power_trade.mapper.TradeDiaryFileMapper">

    <!-- 根据交易日历ID查询附件列表 -->
    <select id="selectFilesByDiaryId" resultType="org.jeecg.modules.api.power_trade.entity.TradeDiaryFile">
        SELECT * FROM trade_diary_file
        WHERE trade_dairy_id = #{tradeDiaryId}
        ORDER BY id
    </select>

    <!-- 批量查询交易日历附件 -->
    <select id="selectFilesByDiaryIds" resultType="org.jeecg.modules.api.power_trade.entity.TradeDiaryFile">
        SELECT * FROM trade_diary_file
        WHERE trade_dairy_id IN
        <foreach collection="tradeDiaryIds" item="tradeDiaryId" open="(" separator="," close=")">
            #{tradeDiaryId}
        </foreach>
        ORDER BY trade_dairy_id, id
    </select>

</mapper>