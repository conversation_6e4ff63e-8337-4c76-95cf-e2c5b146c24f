package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyNewDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.EnergyNewDailyCleanService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class EnergyNewDailyCleanServiceImpl extends ServiceImpl<EnergyNewDailyCleanMapper, EnergyNewDailyClean> implements EnergyNewDailyCleanService {

    @Override
    public List<EnergyNewDailyClean> selectByDay(Long stationId, String date) {
        try {
            List<EnergyNewDailyClean> result = baseMapper.selectByDay(stationId, date);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("按日查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyNewDailyClean> selectByMonth(Long stationId, String date) {
        try {
            log.info("开始按月查询新能源日清分数据 - 电站ID: {}, 日期: {}", stationId, date);
            List<EnergyNewDailyClean> result = baseMapper.selectByMonth(stationId, date);
            log.info("按月查询完成 - 电站ID: {}, 日期: {}, 结果数量: {}",
                    stationId, date, result != null ? result.size() : 0);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("按月查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<EnergyNewDailyClean> selectByYear(Long stationId, String date) {
        try {
            List<EnergyNewDailyClean> result = baseMapper.selectByYear(stationId, date);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("按年查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
            return new ArrayList<>();
        }
    }
}
