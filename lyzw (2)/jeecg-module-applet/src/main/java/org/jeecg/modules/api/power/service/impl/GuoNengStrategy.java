package org.jeecg.modules.api.power.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.StationService;
import org.jeecg.modules.api.power.entity.LightPowerGn;
import org.jeecg.modules.api.power.entity.RealPower15mGn;
import org.jeecg.modules.api.power.entity.WindPowerGn;
import org.jeecg.modules.api.power.mapper.LightPowerGnMapper;
import org.jeecg.modules.api.power.mapper.RealPower15mGnMapper;
import org.jeecg.modules.api.power.mapper.WindPowerGnMapper;
import org.jeecg.modules.api.power.param.CommonPowerDto;
import org.jeecg.modules.api.power.param.PowerQueryParam;
import org.jeecg.modules.api.power.service.PowerStrategy;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.DateUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GuoNengStrategy implements PowerStrategy {

    // 电站类型常量
    private static final Integer STATION_TYPE_SUN = 0;  // 光伏
    private static final Integer STATION_TYPE_WIND = 1; // 风电

    @Resource
    private LightPowerGnMapper lightPowerGnMapper;

    @Resource
    private WindPowerGnMapper windPowerGnMapper;

    @Resource
    private RealPower15mGnMapper realPower15mGnMapper;

    @Resource
    private StationService biStationService;

    /**
     * 根据时间范围查询功率预测数据
     *
     * @param param 查询参数
     * @return 功率预测数据
     */
    @Override
    public List<CommonPowerDto> queryPowerPredictionByRange(PowerQueryParam param) {
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        Long stationId = param.getStationId();
        String version = param.getVersion();
        Station station = biStationService.getById(stationId);
        // 获取时间->值的map
        Map<String, Double> timeValueMap = new HashMap<>();
        // 光伏
        if (Objects.equals(station.getType(), STATION_TYPE_SUN)) {
            List<LightPowerGn> lightPowerGnList;
            if (StringUtils.isEmpty(version)) {
                lightPowerGnList = lightPowerGnMapper.selectMaxVersionByDateRangeAndStation(startDate, endDate, Collections.singletonList(stationId));
            } else {
                lightPowerGnList = lightPowerGnMapper.selectByVersionAndDateRange(startDate, endDate, Collections.singletonList(stationId), version);
            }
            timeValueMap = lightPowerGnList.stream().filter(item -> Objects.nonNull(item.getPower())).collect(Collectors.toMap(
                    item -> item.getDate() + StrUtil.SPACE + item.getTime()
                    , LightPowerGn::getPower));
        }
        // 风电
        if (Objects.equals(station.getType(), STATION_TYPE_WIND)) {
            List<WindPowerGn> windPowerGnList;
            if (StringUtils.isEmpty(version)) {
                windPowerGnList = windPowerGnMapper.selectMaxVersionByDateRangeAndStation(startDate, endDate, Collections.singletonList(stationId));
            } else {
                windPowerGnList = windPowerGnMapper.selectByVersionAndDateRange(startDate, endDate, Collections.singletonList(stationId), version);
            }
            timeValueMap = windPowerGnList.stream().filter(item -> Objects.nonNull(item.getPower())).collect(Collectors.toMap(
                    item -> item.getDate() + StrUtil.SPACE + item.getTime()
                    , WindPowerGn::getPower));
        }
        if (timeValueMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> completeTimes = generateCompleteTimeList(startDate, endDate, 15);
        List<CommonPowerDto> results = new ArrayList<>();
        for (String completeTime : completeTimes) {
            CommonPowerDto result = new CommonPowerDto();
            String[] dateArray = completeTime.split(StrUtil.SPACE);
            result.setDate(dateArray[0]);
            result.setTime(dateArray[1]);
            result.setValue(handlePowerByRule(timeValueMap.get(completeTime), stationId));
            results.add(result);
        }
        return results;
    }

    @Override
    public List<CommonPowerDto> queryPowerRealByRange(PowerQueryParam param) {
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        Long stationId = param.getStationId();
        List<RealPower15mGn> realPower15mGnList = realPower15mGnMapper.selectList(Wrappers.<RealPower15mGn>lambdaQuery()
                .ge(RealPower15mGn::getDate, startDate)
                .le(RealPower15mGn::getDate, endDate)
                .eq(RealPower15mGn::getStationId, stationId));
        // 获取时间->值的map
        Map<String, Double> timeValueMap = realPower15mGnList.stream().filter(item -> Objects.nonNull(item.getValue())).collect(Collectors.toMap(
                item -> item.getDate() + StrUtil.SPACE + item.getTime()
                , RealPower15mGn::getValue));
        if (timeValueMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> completeTimes = generateCompleteTimeList(startDate, endDate, 15);
        List<CommonPowerDto> results = new ArrayList<>();
        for (String completeTime : completeTimes) {
            CommonPowerDto result = new CommonPowerDto();
            String[] dateArray = completeTime.split(StrUtil.SPACE);
            result.setDate(dateArray[0]);
            result.setTime(dateArray[1]);
            result.setValue(handlePowerByRule(timeValueMap.get(completeTime), stationId));
            results.add(result);
        }
        return results;
    }

    @Override
    public List<CommonPowerDto> queryPowerPredictionByDateList(PowerQueryParam param) {
        List<String> dateList = param.getDateList();
        if (CollectionUtils.isEmpty(dateList)) {
            return Collections.emptyList();
        }
        Long stationId = param.getStationId();
        String version = param.getVersion();
        Station station = biStationService.getById(stationId);
        // 构造返回数据
        List<CommonPowerDto> results = new ArrayList<>();
        for (int i = 0; i < dateList.size(); i += 100) {
            int endIndex = Math.min(i + 100, dateList.size());
            List<String> subList = dateList.subList(i, endIndex);
            // 光伏
            if (Objects.equals(station.getType(), STATION_TYPE_SUN)) {
                List<LightPowerGn> lightPowerGnList;
                if (StringUtils.isEmpty(version)) {
                    lightPowerGnList = lightPowerGnMapper.selectMaxVersionByDatesAndStation(subList, Collections.singletonList(stationId));
                } else {
                    lightPowerGnList = lightPowerGnMapper.selectByVersionAndDates(subList, Collections.singletonList(stationId), version);
                }
                if (CollectionUtils.isEmpty(lightPowerGnList)) {
                    continue;
                }
                for (LightPowerGn lightPowerGn : lightPowerGnList) {
                    CommonPowerDto result = new CommonPowerDto();
                    result.setDate(lightPowerGn.getDate());
                    result.setTime(lightPowerGn.getTime());
                    result.setValue(handlePowerByRule(lightPowerGn.getPower(), stationId));
                    results.add(result);
                }
            }
            // 风电
            if (Objects.equals(station.getType(), STATION_TYPE_WIND)) {
                List<WindPowerGn> windPowerGnList;
                if (StringUtils.isEmpty(version)) {
                    windPowerGnList = windPowerGnMapper.selectMaxVersionByDatesAndStation(subList, Collections.singletonList(stationId));
                } else {
                    windPowerGnList = windPowerGnMapper.selectByVersionAndDates(subList, Collections.singletonList(stationId), version);
                }
                if (CollectionUtils.isEmpty(windPowerGnList)) {
                    continue;
                }
                for (WindPowerGn windPowerGn : windPowerGnList) {
                    CommonPowerDto result = new CommonPowerDto();
                    result.setDate(windPowerGn.getDate());
                    result.setTime(windPowerGn.getTime());
                    result.setValue(handlePowerByRule(windPowerGn.getPower(), stationId));
                    results.add(result);
                }
            }
        }
        return results;
    }

    /**
     * 根据日期列表查询功率实发数据
     * @param param 查询参数
     * @return 功率实发数据
     */
    @Override
    public List<CommonPowerDto> queryPowerRealByRangeDateList(PowerQueryParam param) {
        List<String> dateList = param.getDateList();
        if (CollectionUtils.isEmpty(dateList)) {
            return Collections.emptyList();
        }
        Long stationId = param.getStationId();
        // 构造返回数据
        List<CommonPowerDto> results = new ArrayList<>();
        for (int i = 0; i < dateList.size(); i += 100) {
            int endIndex = Math.min(i + 100, dateList.size());
            List<String> subList = dateList.subList(i, endIndex);
            List<RealPower15mGn> realPower15mGnList = realPower15mGnMapper.selectList(Wrappers.<RealPower15mGn>lambdaQuery()
                    .in(RealPower15mGn::getDate, subList)
                    .eq(RealPower15mGn::getStationId, stationId));
            if (CollectionUtils.isEmpty(realPower15mGnList)) {
                continue;
            }
            for (RealPower15mGn realPower15mGn : realPower15mGnList) {
                CommonPowerDto result = new CommonPowerDto();
                result.setDate(realPower15mGn.getDate());
                result.setTime(realPower15mGn.getTime());
                result.setValue(handlePowerByRule(realPower15mGn.getValue(), stationId));
                results.add(result);
            }
        }
        return results;
    }

    /**
     * 查询预测数据最大版本
     * @param param 查询参数
     * @return 版本
     */
    @Override
    public String queryMaxVersion(PowerQueryParam param) {
        Long stationId = param.getStationId();
        String date = param.getDate();
        Station station = biStationService.getById(stationId);
        if (Objects.equals(station.getType(), STATION_TYPE_SUN)) {
            return lightPowerGnMapper.selectMaxVersionByDate(date, stationId);
        }
        if (Objects.equals(station.getType(), STATION_TYPE_WIND)) {
            return windPowerGnMapper.selectMaxVersionByDate(date, stationId);
        }
        return null;
    }

    @Override
    public Integer getSupportedFactoryId() {
        return 2;
    }

    /**
     * 根据规则处理预测的功率数据
     * @param originPower 原始预测数据
     * @param stationId 电站id
     * @return 处理后数据
     */
    private Double handlePowerByRule(Double originPower, Long stationId) {
        if (Objects.isNull(originPower)) {
            return null;
        }
        if (Objects.equals(stationId, 4L)) {
            return originPower / 2;
        }
        if (Objects.equals(stationId, 3L)) {
            return originPower / 3.34;
        }
        return originPower;
    }

    /**
     * 生成完整的时间列表（替代DateUtils.generateYearCompleteTime方法）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param intervalMinutes 时间间隔（分钟）
     * @return 时间列表
     */
    private List<String> generateCompleteTimeList(String startDate, String endDate, int intervalMinutes) {
        List<String> timeList = new ArrayList<>();
        try {
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
            
            LocalDateTime start = LocalDateTime.parse(startDate + " 00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            LocalDateTime end = LocalDateTime.parse(endDate + " 23:45", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            
            LocalDateTime current = start;
            while (!current.isAfter(end)) {
                String dateStr = current.format(dateFormatter);
                String timeStr = current.format(timeFormatter);
                timeList.add(dateStr + " " + timeStr);
                current = current.plusMinutes(intervalMinutes);
            }
        } catch (Exception e) {
            log.error("生成时间列表失败: {}", e.getMessage(), e);
        }
        return timeList;
    }
    
    /**
     * 计算功率发电趋势（实际功率除以4）
     * @param originalPower 原始功率
     * @return 计算后的功率
     */
    public Double calculatePowerGenerationTrend(Double originalPower) {
        if (originalPower == null) {
            return null;
        }
        return originalPower / 4.0;
    }
}
