package org.jeecg.modules.api.power.param;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 电站交易详情DTO
 */
@Data
public class StationTradeDetailDto {

    /**
     * 电站基本信息
     */
    private StationBaseInfo baseInfo;

    /**
     * 年度交易信息
     */
    private YearTradeInfo yearTradeInfo;

    /**
     * 月度交易信息
     */
    private MonthTradeInfo monthTradeInfo;

    /**
     * 日交易信息列表
     */
    private List<DailyTradeInfo> dailyTradeInfoList;

    /**
     * 电站基本信息
     */
    @Data
    public static class StationBaseInfo {
        /**
         * 电站ID
         */
        private Long id;

        /**
         * 电站名称
         */
        private String name;

        /**
         * 电站类型 (1-风电, 2-光伏, 3-储能)
         */
        private Integer type;

        /**
         * 电站容量
         */
        private Double capacity;

        /**
         * 年计划发电量
         */
        private Double yearPlanPower;

        /**
         * 累计发电量
         */
        private Double totalPower;
    }

    /**
     * 年度交易信息
     */
    @Data
    public static class YearTradeInfo {
        /**
         * 年度交易总电量
         */
        private Double totalPower;

        /**
         * 年度交易均价
         */
        private Double averagePrice;

        /**
         * 年份
         */
        private String year;
    }

    /**
     * 月度交易信息
     */
    @Data
    public static class MonthTradeInfo {
        /**
         * 月度交易总电量
         */
        private Double totalPower;

        /**
         * 月度交易均价
         */
        private Double averagePrice;

        /**
         * 月份
         */
        private String month;

        /**
         * 各类电量电价明细
         */
        private Map<String, Double> powerDetails;

        /**
         * 各类电费明细
         */
        private Map<String, Double> feeDetails;
    }

    /**
     * 日交易信息
     */
    @Data
    public static class DailyTradeInfo {
        /**
         * 交易日期
         */
        private String date;

        /**
         * 电量 (MWh)
         */
        private Double power;

        /**
         * 电价 (元/MWh)
         */
        private Double price;

        /**
         * 电费 (元)
         */
        private Double fee;

        /**
         * 交易类型 (中长期、保障、偏差、实时等)
         */
        private String tradeType;

        /**
         * 侧 (1-购电侧, 2-发电侧)
         */
        private Integer side;
    }
} 