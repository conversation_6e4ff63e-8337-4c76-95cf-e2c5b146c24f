package org.jeecg.modules.api.power.param;

import lombok.Data;

import java.util.List;

/**
 * 能源类型分布DTO
 */
@Data
public class EnergyDistributionDto {
    
    /**
     * 能源类型列表
     */
    private List<EnergyType> energyTypes;
    
    @Data
    public static class EnergyType {
        /**
         * 能源类型名称
         */
        private String name;
        
        /**
         * 能源类型对应的电站数量
         */
        private Integer count;
        
        /**
         * 占比百分比
         */
        private Double percentage;
    }
} 