package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.power_trade.entity.EnergyStorageDailyClean;
import org.jeecg.modules.api.power_trade.mapper.EnergyStorageDailyCleanMapper;
import org.jeecg.modules.api.power_trade.service.EnergyStorageDailyCleanService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EnergyStorageDailyCleanServiceImpl extends ServiceImpl<EnergyStorageDailyCleanMapper, EnergyStorageDailyClean> implements EnergyStorageDailyCleanService {
    
    @Override
    public List<EnergyStorageDailyClean> selectByDay(String stationId, String date) {
        return baseMapper.selectByDay(stationId, date);
    }
    
    @Override
    public List<EnergyStorageDailyClean> selectByMonth( String stationId, String date) {
        return baseMapper.selectByMonth(stationId, date);
    }
    
    @Override
    public List<EnergyStorageDailyClean> selectByYear(String stationId, String date) {
        return baseMapper.selectByYear(stationId, date);
    }
}