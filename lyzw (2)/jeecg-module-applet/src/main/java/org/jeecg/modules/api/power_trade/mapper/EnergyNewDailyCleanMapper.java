package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.EnergyNewDailyClean;

import java.util.List;

public interface EnergyNewDailyCleanMapper extends BaseMapper<EnergyNewDailyClean> {

    /**
     * 按日查询明细数据
     */
    List<EnergyNewDailyClean> selectByDay(@Param("stationId") Long stationId,
                                          @Param("date") String date);

    /**
     * 按月汇总查询
     */
    List<EnergyNewDailyClean> selectByMonth(@Param("stationId") Long stationId,
                                            @Param("yearMonth") String yearMonth);

    /**
     * 按年汇总查询
     */
    List<EnergyNewDailyClean> selectByYear(@Param("stationId") Long stationId,
                                           @Param("year") String year);
}
