package org.jeecg.modules.api.power_trade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.power_trade.entity.PowerSideSettle;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface PowerSideSettleMapper extends BaseMapper<PowerSideSettle> {

    /**
     * 根据电站ID和时间范围获取累计结算电量和交易均价
     */
    Map<String, BigDecimal> getStationSettlementSummary(@Param("stationId") Long stationId,
                                                        @Param("startDate") String startDate,
                                                        @Param("endDate") String endDate,
                                                        @Param("startYearMonth") String startYearMonth,
                                                        @Param("endYearMonth") String endYearMonth,
                                                        @Param("fileType") Integer fileType);

    /**
     * 根据省份ID和时间范围获取所有电站的结算汇总数据
     */
    List<Map<String, Object>> getProvinceStationSettlementSummary(@Param("provinceId") Integer provinceId,
                                                                  @Param("startDate") String startDate,
                                                                  @Param("endDate") String endDate,
                                                                  @Param("startYearMonth") String startYearMonth,
                                                                  @Param("endYearMonth") String endYearMonth,
                                                                  @Param("fileType") Integer fileType);

    /**
     * 获取电站年度交易数据汇总
     */
    Map<String, Object> getStationYearlyTradeData(@Param("stationId") Long stationId, @Param("year") Integer year);

    /**
     * 获取电站月度交易数据汇总
     */
    Map<String, Object> getStationMonthlyTradeData(@Param("stationId") Long stationId, 
                                                   @Param("year") Integer year, 
                                                   @Param("month") Integer month);

    /**
     * 根据电站类型获取年度交易数据（区分光伏风电和储能）
     */
    Map<String, Object> getStationYearlyTradeDataByType(@Param("stationId") Long stationId,
                                                       @Param("year") Integer year,
                                                       @Param("stationType") Integer stationType,
                                                       @Param("settlementType") Integer settlementType);

    /**
 * 获取电站年度交易电量信息（用于电站详情）
 * @param stationId 电站ID
 * @param year 年份（yyyy格式）
 * @return 年度交易电量信息（按月分解的列表）
 */
List<Map<String, Object>> getStationYearlyTradingInfo(@Param("stationId") Long stationId,
                                                     @Param("year") String year);

/**
 * 获取电站月度交易电量信息
 * @param stationId 电站ID
 * @param yearMonth 年月（格式：yyyy-MM）
 * @return 月度交易电量信息
 */
Map<String, Object> getStationMonthlyTradingInfo(@Param("stationId") Long stationId,
                                               @Param("yearMonth") String yearMonth);

/**
 * 调试查询：验证数据关联
 * @param stationId 电站ID
 * @param year 年份
 * @return 关联数据
 */
List<Map<String, Object>> debugStationDataRelation(@Param("stationId") Long stationId,
                                                  @Param("year") String year);
}