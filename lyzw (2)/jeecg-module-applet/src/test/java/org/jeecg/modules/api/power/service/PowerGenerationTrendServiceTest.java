package org.jeecg.modules.api.power.service;

import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 发电趋势服务测试类
 */
@SpringBootTest
public class PowerGenerationTrendServiceTest {

    @Autowired
    private PowerService powerService;

    /**
     * 测试单个电站日维度发电趋势
     */
    @Test
    public void testSingleStationDailyTrend() {
        PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
        param.setStationId(123L); // 单个电站ID
        param.setProvinceId(1);
        param.setTimeDimension("daily");
        param.setQueryDate("2023-08-23");

        List<PowerGenerationTrendDto> result = powerService.getPowerGenerationTrend(param);
        
        assertNotNull(result);
        System.out.println("日维度数据点数量: " + result.size());
        
        if (!result.isEmpty()) {
            PowerGenerationTrendDto firstPoint = result.get(0);
            assertNotNull(firstPoint.getTimeLabel());
            assertNotNull(firstPoint.getActualPower());
            assertNotNull(firstPoint.getPowerGeneration());
            
            // 验证发电量 = 实际功率 / 4
            if (firstPoint.getActualPower() != null && firstPoint.getActualPower() > 0) {
                double expectedGeneration = firstPoint.getActualPower() / 4.0;
                assertEquals(expectedGeneration, firstPoint.getPowerGeneration(), 0.001);
                System.out.println("功率除以4验证通过 - 实际功率: " + firstPoint.getActualPower() + 
                                 ", 发电量: " + firstPoint.getPowerGeneration());
            }
            
            // 验证时间标签格式 (应该是 "HH:mm" 格式)
            assertTrue(firstPoint.getTimeLabel().matches("\\d{2}:\\d{2}"));
            System.out.println("时间标签示例: " + firstPoint.getTimeLabel());
        }
    }

    /**
     * 测试单个电站月维度发电趋势
     */
    @Test
    public void testSingleStationMonthlyTrend() {
        PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
        param.setStationId(123L); // 单个电站ID
        param.setProvinceId(1);
        param.setTimeDimension("monthly");
        param.setQueryDate("2023-08");

        List<PowerGenerationTrendDto> result = powerService.getPowerGenerationTrend(param);
        
        assertNotNull(result);
        System.out.println("月维度数据点数量: " + result.size());
        
        if (!result.isEmpty()) {
            PowerGenerationTrendDto firstPoint = result.get(0);
            assertNotNull(firstPoint.getTimeLabel());
            assertNotNull(firstPoint.getPowerGeneration());
            
            // 验证时间标签格式 (应该是 "XX日" 格式)
            assertTrue(firstPoint.getTimeLabel().matches("\\d{2}日"));
            System.out.println("月维度时间标签示例: " + firstPoint.getTimeLabel());
            System.out.println("月维度发电量示例: " + firstPoint.getPowerGeneration());
        }
    }

    /**
     * 测试单个电站年维度发电趋势
     */
    @Test
    public void testSingleStationYearlyTrend() {
        PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
        param.setStationId(123L); // 单个电站ID
        param.setProvinceId(1);
        param.setTimeDimension("yearly");
        param.setQueryDate("2023");

        List<PowerGenerationTrendDto> result = powerService.getPowerGenerationTrend(param);
        
        assertNotNull(result);
        System.out.println("年维度数据点数量: " + result.size());
        
        if (!result.isEmpty()) {
            PowerGenerationTrendDto firstPoint = result.get(0);
            assertNotNull(firstPoint.getTimeLabel());
            assertNotNull(firstPoint.getPowerGeneration());
            
            // 验证时间标签格式 (应该是 "XX月" 格式)
            assertTrue(firstPoint.getTimeLabel().matches("\\d{2}月"));
            System.out.println("年维度时间标签示例: " + firstPoint.getTimeLabel());
            System.out.println("年维度发电量示例: " + firstPoint.getPowerGeneration());
        }
    }

    /**
     * 测试功率到发电量的转换逻辑
     */
    @Test
    public void testPowerToGenerationConversion() {
        PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
        param.setStationId(123L);
        param.setProvinceId(1);
        param.setTimeDimension("daily");
        param.setQueryDate("2023-08-23");

        List<PowerGenerationTrendDto> result = powerService.getPowerGenerationTrend(param);
        
        for (PowerGenerationTrendDto dto : result) {
            if (dto.getActualPower() != null && dto.getActualPower() > 0) {
                // 验证发电量计算公式：发电量 = 实际功率 / 4
                double expectedGeneration = dto.getActualPower() / 4.0;
                assertEquals(expectedGeneration, dto.getPowerGeneration(), 0.001,
                    "发电量计算错误 - 实际功率: " + dto.getActualPower() + 
                    ", 期望发电量: " + expectedGeneration + 
                    ", 实际发电量: " + dto.getPowerGeneration());
            }
        }
        
        System.out.println("功率到发电量转换验证完成，共验证 " + result.size() + " 个数据点");
    }

    /**
     * 测试参数验证
     */
    @Test
    public void testParameterValidation() {
        // 测试空参数
        List<PowerGenerationTrendDto> result1 = powerService.getPowerGenerationTrend(null);
        assertTrue(result1.isEmpty());

        // 测试缺少电站ID
        PowerGenerationTrendQueryParam param2 = new PowerGenerationTrendQueryParam();
        param2.setProvinceId(1);
        param2.setTimeDimension("daily");
        param2.setQueryDate("2023-08-23");
        List<PowerGenerationTrendDto> result2 = powerService.getPowerGenerationTrend(param2);
        assertTrue(result2.isEmpty());

        // 测试不支持的时间维度
        PowerGenerationTrendQueryParam param3 = new PowerGenerationTrendQueryParam();
        param3.setStationId(123L);
        param3.setProvinceId(1);
        param3.setTimeDimension("invalid");
        param3.setQueryDate("2023-08-23");
        List<PowerGenerationTrendDto> result3 = powerService.getPowerGenerationTrend(param3);
        assertTrue(result3.isEmpty());

        System.out.println("参数验证测试通过");
    }

    /**
     * 测试不同电站的数据查询
     */
    @Test
    public void testDifferentStations() {
        // 测试电站1
        PowerGenerationTrendQueryParam param1 = new PowerGenerationTrendQueryParam();
        param1.setStationId(1L);
        param1.setProvinceId(1);
        param1.setTimeDimension("daily");
        param1.setQueryDate("2023-08-23");

        List<PowerGenerationTrendDto> result1 = powerService.getPowerGenerationTrend(param1);
        System.out.println("电站1数据点数量: " + result1.size());

        // 测试电站2
        PowerGenerationTrendQueryParam param2 = new PowerGenerationTrendQueryParam();
        param2.setStationId(2L);
        param2.setProvinceId(1);
        param2.setTimeDimension("daily");
        param2.setQueryDate("2023-08-23");

        List<PowerGenerationTrendDto> result2 = powerService.getPowerGenerationTrend(param2);
        System.out.println("电站2数据点数量: " + result2.size());

        // 验证每个电站都能独立查询数据
        assertNotNull(result1);
        assertNotNull(result2);
    }
}
