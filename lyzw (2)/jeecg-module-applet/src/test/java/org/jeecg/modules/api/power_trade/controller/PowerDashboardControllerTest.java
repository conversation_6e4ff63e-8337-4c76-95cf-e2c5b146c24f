package org.jeecg.modules.api.power_trade.controller;

import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.service.IElectricityDataService;
import org.jeecg.modules.api.power_trade.service.StationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PowerDashboardController 测试类
 * 主要测试省份自动检测功能
 */
class PowerDashboardControllerTest {

    @Mock
    private StationService stationService;

    @Mock
    private IElectricityDataService electricityDataService;

    @InjectMocks
    private PowerDashboardController powerDashboardController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(powerDashboardController).build();
    }

    @Test
    void testGetAhuFieldOptimized_WithProvinceIdZero_Success() throws Exception {
        // 准备测试数据
        Station mockStation = new Station();
        mockStation.setId(1L);
        mockStation.setName("测试电站");
        mockStation.setProvinceId(2); // 安徽省

        // Mock StationService
        when(stationService.getById(1L)).thenReturn(mockStation);

        // Mock ElectricityDataService
        when(electricityDataService.getElectricityDataByProvince(any(), any(), any(), any()))
                .thenReturn("测试数据");

        // 执行测试
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "0")
                        .param("stationId", "1")
                        .param("date", "2024")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testGetAhuFieldOptimized_WithProvinceIdZero_NoStationId() throws Exception {
        // 测试省份ID为0但未提供电站ID的情况
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "0")
                        .param("date", "2024")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("当省份ID为0时，必须提供电站ID"));
    }

    @Test
    void testGetAhuFieldOptimized_WithProvinceIdZero_StationNotFound() throws Exception {
        // Mock StationService 返回null（电站不存在）
        when(stationService.getById(999L)).thenReturn(null);

        // 测试电站不存在的情况
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "0")
                        .param("stationId", "999")
                        .param("date", "2024")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("电站不存在，电站ID: 999"));
    }

    @Test
    void testGetAhuFieldOptimized_WithProvinceIdZero_NoProvinceInfo() throws Exception {
        // 准备测试数据 - 电站存在但省份信息缺失
        Station mockStation = new Station();
        mockStation.setId(1L);
        mockStation.setName("测试电站");
        mockStation.setProvinceId(null); // 省份信息缺失

        // Mock StationService
        when(stationService.getById(1L)).thenReturn(mockStation);

        // 测试电站省份信息缺失的情况
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "0")
                        .param("stationId", "1")
                        .param("date", "2024")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("电站省份信息缺失，电站ID: 1"));
    }

    @Test
    void testGetAhuFieldOptimized_WithNormalProvinceId() throws Exception {
        // Mock ElectricityDataService
        when(electricityDataService.getElectricityDataByProvince(any(), any(), any(), any()))
                .thenReturn("测试数据");

        // 测试正常的省份ID（原有功能）
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "2")
                        .param("stationId", "1")
                        .param("date", "2024")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    void testGetAhuFieldOptimized_InvalidDimension() throws Exception {
        // 测试无效的维度参数
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "2")
                        .param("date", "2024")
                        .param("dimension", "4")) // 无效维度
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("时间维度只能是 1(年)、2(月) 或 3(日)"));
    }

    @Test
    void testGetAhuFieldOptimized_EmptyDate() throws Exception {
        // 测试空日期参数
        mockMvc.perform(get("/api/power_trade/dashboard/getAhuFieldOptimized")
                        .param("provinceId", "2")
                        .param("date", "")
                        .param("dimension", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("日期不能为空"));
    }
}