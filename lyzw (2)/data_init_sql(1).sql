CREATE
DATABASE IF NOT EXISTS `bi_js_data`;

USE
`bi_js_data`;

-- bi_js_data.actual_gas_generation definition

CREATE TABLE IF NOT EXISTS `actual_gas_generation`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 3)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    <PERSON>IMAR<PERSON> KEY (`id`),
    UNIQUE KEY `actual_gas_generation_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际燃机固定出力总值';


-- bi_js_data.actual_load definition

CREATE TABLE IF NOT EXISTS `actual_load`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `actual_load_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际负荷情况';


-- bi_js_data.actual_power_receive definition

CREATE TABLE IF NOT EXISTS `actual_power_receive`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `actual_power_receive_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际受电情况';


-- bi_js_data.actual_renewable definition

CREATE TABLE IF NOT EXISTS `actual_renewable`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `actual_renewable_unique` (`date`,`time`,`area`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际统调风光情况';


-- bi_js_data.actual_system_load definition

CREATE TABLE IF NOT EXISTS `actual_system_load`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `actual_system_load_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际系统负荷';


-- bi_js_data.clear_info definition

CREATE TABLE IF NOT EXISTS `clear_info`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                           DEFAULT NULL COMMENT '日期',
    `data_type`   varchar(100)                                                   DEFAULT NULL COMMENT '数据类型',
    `value`       decimal(10, 5)                                                 DEFAULT NULL COMMENT '值',
    `note`        varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `create_time` datetime                                                       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `clear_info_unique` (`date`,`data_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='现货市场申报出清情况';


-- bi_js_data.contract_day_roll definition

CREATE TABLE IF NOT EXISTS `contract_day_roll`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `target_date`  date         DEFAULT NULL COMMENT '标的日',
    `operate_date` date         DEFAULT NULL COMMENT '操作日期',
    `create_time`  datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日滚撮合同';


-- bi_js_data.contract_day_roll_data definition

CREATE TABLE IF NOT EXISTS `contract_day_roll_data`
(
    `contract_id`          bigint unsigned NOT NULL COMMENT '关联合同id',
    `deal_time_start_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分时段开始时间',
    `deal_time_end_time`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分时段开始时间',
    `direction`            tinyint unsigned DEFAULT NULL COMMENT '买卖方向 0买入 1卖出',
    `deal_power`           decimal(10, 4) DEFAULT NULL COMMENT '成交电量',
    `deal_avg_price`       decimal(10, 5) DEFAULT NULL COMMENT '成交均价',
    PRIMARY KEY (`contract_id`, `deal_time_start_time`, `deal_time_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日滚撮合同数据';


-- bi_js_data.contract_period definition

CREATE TABLE IF NOT EXISTS `contract_period`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `period_name` varchar(100) DEFAULT NULL COMMENT '周期名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同周期';


-- bi_js_data.contract_type definition

CREATE TABLE IF NOT EXISTS `contract_type`
(
    `id`        int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name` varchar(100) DEFAULT NULL COMMENT '类型名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同类型';


-- bi_js_data.contract_yearly definition

CREATE TABLE IF NOT EXISTS `contract_yearly`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`     bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `title`          varchar(100)                                                  DEFAULT NULL COMMENT '合同标题',
    `period_id`      int unsigned DEFAULT NULL COMMENT '合同周期id',
    `type_id`        int unsigned DEFAULT NULL COMMENT '合同类型id',
    `total_quantity` decimal(10, 2)                                                DEFAULT NULL COMMENT '合同总电量',
    `start_date`     date                                                          DEFAULT NULL COMMENT '合同开始时间',
    `end_date`       date                                                          DEFAULT NULL COMMENT '合同结束时间',
    `price`          decimal(10, 2)                                                DEFAULT NULL COMMENT '权益电价',
    `buyer`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '购电方',
    `create_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(100)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(100)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY              `contract_yearly_station_id_IDX` (`station_id`,`start_date`,`end_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度合同';


-- bi_js_data.contract_yearly_data definition

CREATE TABLE IF NOT EXISTS `contract_yearly_data`
(
    `contract_id` bigint unsigned NOT NULL COMMENT '合同id',
    `curve_id`    bigint unsigned DEFAULT NULL COMMENT '日分解曲线id',
    `month`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '月份',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '电价',
    `ratio`       decimal(10, 2) DEFAULT NULL COMMENT '比例',
    UNIQUE KEY `contract_yearly_data_unique` (`contract_id`,`month`),
    KEY           `contract_yearly_data_contract_id_IDX` (`contract_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度合同分解月度曲线';


-- bi_js_data.day_ahead_clear_power definition

CREATE TABLE IF NOT EXISTS `day_ahead_clear_power`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_clear_power_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前各时段出清电量';


-- bi_js_data.day_clear_lmp_jb definition

CREATE TABLE IF NOT EXISTS `day_clear_lmp_jb`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `day_clear_lmp_jb_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前出清-江北分区节点边际电价均价';


-- bi_js_data.day_clear_lmp_jn definition

CREATE TABLE IF NOT EXISTS `day_clear_lmp_jn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `day_clear_lmp_jn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前出清-江南分区节点边际电价均价';


-- bi_js_data.day_clear_power definition

CREATE TABLE IF NOT EXISTS `day_clear_power`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `day_clear_power_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前出清-出清电力';


-- bi_js_data.day_clear_price_jb definition

CREATE TABLE IF NOT EXISTS `day_clear_price_jb`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `day_clear_price_jb_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前出清-江北分区价格';


-- bi_js_data.day_clear_price_jn definition

CREATE TABLE IF NOT EXISTS `day_clear_price_jn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `day_clear_price_jn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前出清-江南分区价格';


-- bi_js_data.day_power_curve definition

CREATE TABLE IF NOT EXISTS `day_power_curve`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `curve_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '曲线名称',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日发电曲线';


-- bi_js_data.day_power_curve_data definition

CREATE TABLE IF NOT EXISTS `day_power_curve_data`
(
    `curve_id` bigint unsigned NOT NULL COMMENT '关联日发电曲线表id',
    `time`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '时间',
    `price`    decimal(20, 6) DEFAULT NULL COMMENT '电价',
    `ratio`    decimal(10, 2) DEFAULT NULL COMMENT '比例',
    UNIQUE KEY `day_power_curve_data_unique` (`curve_id`,`time`),
    KEY        `day_power_curve_data_curve_id_IDX` (`curve_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日发电曲线数据表';


-- bi_js_data.energy_new_daily_clean definition

CREATE TABLE IF NOT EXISTS `energy_new_daily_clean`
(
    `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`                      date NOT NULL COMMENT '日期',
    `station_id`                bigint unsigned NOT NULL COMMENT '电站id',
    `mid_long_term_power`       decimal(20, 5) DEFAULT NULL COMMENT '中长期合约电量',
    `mid_long_term_price`       decimal(20, 5) DEFAULT NULL COMMENT '中长期单价',
    `mid_long_term_fee`         decimal(20, 5) DEFAULT NULL COMMENT '中长期电费',
    `guarantee_power`           decimal(20, 5) DEFAULT NULL COMMENT '保障电量',
    `guarantee_price`           decimal(20, 5) DEFAULT NULL COMMENT '保障电价',
    `guarantee_fee`             decimal(20, 5) DEFAULT NULL COMMENT '保障电费',
    `day_ahead_deviation_power` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `day_ahead_deviation_price` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差单价',
    `day_ahead_deviation_fee`   decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电费',
    `realtime_deviation_power`  decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `realtime_deviation_price`  decimal(20, 5) DEFAULT NULL COMMENT '实时偏差单价',
    `realtime_deviation_fee`    decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电费',
    `excess_profit_recovery`    decimal(20, 5) DEFAULT NULL COMMENT '超额收益回收',
    `day_ahead_profit_recovery` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差收益回收',
    `total_power`               decimal(20, 5) DEFAULT NULL COMMENT '上网电量',
    `total_fee`                 decimal(20, 5) DEFAULT NULL COMMENT '总电费',
    `settlement_avg_price`      decimal(20, 5) DEFAULT NULL COMMENT '日结算均价',
    `create_time`               datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`                 varchar(100)   DEFAULT NULL COMMENT '创建人',
    `update_by`                 varchar(100)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `energy_new_daily_clean_unique` (`date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新能源日清分';


-- bi_js_data.energy_storage_daily_clean definition

CREATE TABLE IF NOT EXISTS `energy_storage_daily_clean`
(
    `id`                                                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`                                               date NOT NULL COMMENT '日期',
    `station_id`                                         bigint unsigned DEFAULT NULL COMMENT '电站id',
    `user_day_ahead_deviation_power`                     decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `user_day_ahead_deviation_average_price`             decimal(20, 5) DEFAULT NULL COMMENT '日前平均电价',
    `user_day_ahead_deviation_fee`                       decimal(20, 5) DEFAULT NULL COMMENT '日前电费',
    `user_realtime_deviation_power`                      decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `user_realtime_deviation_average_price`              decimal(20, 5) DEFAULT NULL COMMENT '实时平均单价',
    `user_realtime_deviation_fee`                        decimal(20, 5) DEFAULT NULL COMMENT '实时电费',
    `user_total_power`                                   decimal(20, 5) DEFAULT NULL COMMENT '用电总电量',
    `user_total_fee`                                     decimal(20, 5) DEFAULT NULL COMMENT '用电总费用',
    `power_generation_day_ahead_deviation_power`         decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `power_generation_day_ahead_deviation_average_price` decimal(20, 5) DEFAULT NULL COMMENT '日前平均电价',
    `power_generation_day_ahead_deviation_fee`           decimal(20, 5) DEFAULT NULL COMMENT '日前电费',
    `power_generation_realtime_deviation_power`          decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `power_generation_realtime_deviation_average_price`  decimal(20, 5) DEFAULT NULL COMMENT '实时平均单价',
    `power_generation_realtime_deviation_fee`            decimal(20, 5) DEFAULT NULL COMMENT '实时电费',
    `power_generation_total_power`                       decimal(20, 5) DEFAULT NULL COMMENT '用电总电量',
    `power_generation_total_fee`                         decimal(20, 5) DEFAULT NULL COMMENT '用电总费用',
    `create_time`                                        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                                        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`                                          varchar(100)   DEFAULT NULL COMMENT '创建人',
    `update_by`                                          varchar(100)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `energy_storage_daily_clean_unique` (`date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能日清分';


-- bi_js_data.file_station_relation definition

CREATE TABLE IF NOT EXISTS `file_station_relation`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_url`       varchar(1000) DEFAULT NULL COMMENT '文件在minio地址',
    `station_id`     bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `settle_main_id` bigint unsigned DEFAULT NULL COMMENT '关联结算主体id',
    `type`           tinyint unsigned DEFAULT NULL COMMENT '文件类型',
    `settle_date`    date          DEFAULT NULL COMMENT '文件结算日期',
    `create_time`    datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `file_station_relation_unique` (`settle_main_id`,`station_id`,`type`,`settle_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件关联电站表';


-- bi_js_data.gas_generation_boundary definition

CREATE TABLE IF NOT EXISTS `gas_generation_boundary`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `gas_generation_boundary_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='燃机固定出力总值-边界信息发布电力';


-- bi_js_data.gas_generation_clear definition

CREATE TABLE IF NOT EXISTS `gas_generation_clear`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `gas_generation_clear_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='燃机固定出力总值-出清发布电力';


-- bi_js_data.generation_forecast definition

CREATE TABLE IF NOT EXISTS `generation_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `generation_forecast_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发电总出力预测情况';


-- bi_js_data.generation_output definition

CREATE TABLE IF NOT EXISTS `generation_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `generation_output_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发电总出力情况';


-- bi_js_data.hydro_forecast definition

CREATE TABLE IF NOT EXISTS `hydro_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `hydro_forecast_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='水电总出力预测情况';


-- bi_js_data.hydro_output definition

CREATE TABLE IF NOT EXISTS `hydro_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `hydro_output_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='水电总出力情况';


-- bi_js_data.jiangsu_monthly_trade definition

CREATE TABLE IF NOT EXISTS `jiangsu_monthly_trade`
(
    `year`                   int unsigned DEFAULT NULL COMMENT '年份',
    `month`                  tinyint unsigned DEFAULT NULL COMMENT '月份',
    `last_year_market_usage` decimal(10, 2) DEFAULT NULL COMMENT '去年同期市场化总用电量(亿千瓦时)',
    `annual_plan`            decimal(10, 2) DEFAULT NULL COMMENT '年度分月计划电量(亿千瓦时)',
    `monthly_usage`          decimal(10, 2) DEFAULT NULL COMMENT '用电量-分月电量',
    `bid_volume`             decimal(10, 2) DEFAULT NULL COMMENT '月度集中竞价(电能量)-成交电量',
    `bid_price`              decimal(10, 2) DEFAULT NULL COMMENT '月度集中竞价(电能量)-出清价(元/MWh)',
    `green_volume`           decimal(10, 2) DEFAULT NULL COMMENT '月度绿电-成交电量',
    `green_deals`            decimal(10, 2) DEFAULT NULL COMMENT '月度绿电-成交笔数',
    `green_avg_price`        decimal(10, 2) DEFAULT NULL COMMENT '月度绿电-成交均价(元/MWh)',
    `intra_green_volume`     decimal(10, 2) DEFAULT NULL COMMENT '月内绿电-成交电量',
    `intra_green_deals`      decimal(10, 2) DEFAULT NULL COMMENT '月内绿电-成交笔数',
    `intra_green_avg_price`  decimal(10, 2) DEFAULT NULL COMMENT '月内绿电-成交均价(元/MWh)',
    `monthly_total`          decimal(10, 2) DEFAULT NULL COMMENT '月度（内）交易总量',
    `listing_volume`         decimal(10, 2) DEFAULT NULL COMMENT '月内连续挂牌-成交电量',
    `listing_price`          decimal(10, 2) DEFAULT NULL COMMENT '月内连续挂牌-成交均价(元/MWh)',
    `state_purchase_vol`     decimal(10, 2) DEFAULT NULL COMMENT '国网代理购电-代购电量(亿KWh)',
    `state_purchase_price`   decimal(10, 2) DEFAULT NULL COMMENT '国网代理购电-代购价格(元/MWh)',
    `gen_monthly_vol`        decimal(10, 2) DEFAULT NULL COMMENT '发电侧月度转让-成交电量',
    `gen_monthly_price`      decimal(10, 2) DEFAULT NULL COMMENT '发电侧月度转让-成交均价(元/MWh)',
    `gen_intra_vol`          decimal(10, 2) DEFAULT NULL COMMENT '发电侧月内/上旬转让-成交电量',
    `gen_intra_price`        decimal(10, 2) DEFAULT NULL COMMENT '发电侧月内/上旬转让-成交均价(元/MWh)',
    `create_time`            datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`              varchar(30)    DEFAULT NULL COMMENT '创建人',
    `update_by`              varchar(30)    DEFAULT NULL COMMENT '更新人',
    UNIQUE KEY `jiangsu_monthly_trade_unique` (`year`,`month`),
    KEY                      `jiangsu_monthly_trade_year_IDX` (`year`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='江苏月度交易信息';


-- bi_js_data.load_forecast_boundary definition

CREATE TABLE IF NOT EXISTS `load_forecast_boundary`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `load_forecast_boundary_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短期系统负荷预测信息-边界信息发布电力';


-- bi_js_data.load_forecast_clear definition

CREATE TABLE IF NOT EXISTS `load_forecast_clear`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `load_forecast_clear_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短期系统负荷预测信息-出清发布电力';


-- bi_js_data.market_notice definition

CREATE TABLE IF NOT EXISTS `market_notice`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `notice_title`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '公告标题',
    `type_id`        int unsigned DEFAULT NULL COMMENT '公告类型id（关联公告类型表主键）',
    `notice_date`    date                                                          DEFAULT NULL COMMENT '披露日期',
    `notice_label`   text COMMENT '公告标签',
    `notice_content` text COMMENT '公共内容',
    `create_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(256)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(256)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='市场公告';


-- bi_js_data.new_energy_forecast definition

CREATE TABLE IF NOT EXISTS `new_energy_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 5) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `new_energy_forecast_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新能源总出力预测情况';


-- bi_js_data.new_energy_output definition

CREATE TABLE IF NOT EXISTS `new_energy_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 5) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `new_energy_output_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新能源总出力情况';


-- bi_js_data.non_market_forecast definition

CREATE TABLE IF NOT EXISTS `non_market_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `non_market_forecast_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='非市场机组总出力预测情况';


-- bi_js_data.non_market_output definition

CREATE TABLE IF NOT EXISTS `non_market_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `non_market_output_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='非市场机组总出力情况';


-- bi_js_data.notice_file definition

CREATE TABLE IF NOT EXISTS `notice_file`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_name`   varchar(256)  DEFAULT NULL COMMENT '文件名称',
    `file_url`    varchar(1000) DEFAULT NULL COMMENT '文件url',
    `notice_id`   bigint unsigned DEFAULT NULL COMMENT '文件关联公告id',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256)  DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256)  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公告附件';


-- bi_js_data.notice_type definition

CREATE TABLE IF NOT EXISTS `notice_type`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name`   varchar(256) DEFAULT NULL COMMENT '类型名称',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公告类型';


-- bi_js_data.power_number definition

CREATE TABLE IF NOT EXISTS `power_number`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date          DEFAULT NULL COMMENT '日期',
    `power_type`  varchar(10)   DEFAULT NULL COMMENT '电源类型',
    `number`      int unsigned DEFAULT NULL COMMENT '台数',
    `note`        varchar(2000) DEFAULT NULL COMMENT '备注',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `power_number_unique` (`date`,`power_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电源类型和台数';


-- bi_js_data.power_receive_boundary definition

CREATE TABLE IF NOT EXISTS `power_receive_boundary`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `power_receive_boundary_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='受电计划-边界信息发布电力';


-- bi_js_data.power_receive_clear definition

CREATE TABLE IF NOT EXISTS `power_receive_clear`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `power_receive_clear_unique` (`date`,`time`,`area`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='受电计划-出清发布电力';


-- bi_js_data.power_side_settle definition

CREATE TABLE IF NOT EXISTS `power_side_settle`
(
    `id`                          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_id`                     bigint unsigned DEFAULT NULL COMMENT '关联文件id',
    `station_type`                tinyint unsigned DEFAULT NULL COMMENT '结算单元类型 1 风电 2 光伏 3储能',
    `data_type`                   tinyint unsigned DEFAULT NULL COMMENT '数据类型，只用于储能结算单 0购电侧 1售电侧',
    `actual_internet_electricity` decimal(20, 6) DEFAULT NULL COMMENT '实际上网电量',
    `settlement_electricity`      decimal(20, 6) DEFAULT NULL COMMENT '结算电量',
    `contract_electricity`        decimal(20, 6) DEFAULT NULL COMMENT '合同电量',
    `deviation_electricity`       decimal(20, 6) DEFAULT NULL COMMENT '偏差电量',
    `settlement_electric_fee`     decimal(20, 6) DEFAULT NULL COMMENT '结算电费',
    PRIMARY KEY (`id`),
    KEY                           `power_side_settle_file_id_IDX` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统推发电侧结算单';


-- bi_js_data.realtime_clear_power definition

CREATE TABLE IF NOT EXISTS `realtime_clear_power`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `realtime_clear_power_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='（实时）日内各时段出清电量';


-- bi_js_data.renewable_forecast_boundary definition

CREATE TABLE IF NOT EXISTS `renewable_forecast_boundary`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `renewable_forecast_boundary_unique` (`date`,`time`,`area`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统调风光功率预测-边界信息发布电力';


-- bi_js_data.renewable_forecast_clear definition

CREATE TABLE IF NOT EXISTS `renewable_forecast_clear`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `area`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '区域',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型',
    `value`       decimal(10, 2)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `renewable_forecast_clear_unique` (`date`,`time`,`area`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统调风光功率预测-出清发布电力';


-- bi_js_data.rt_clear_lmp_jb definition

CREATE TABLE IF NOT EXISTS `rt_clear_lmp_jb`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `rt_clear_lmp_jb_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时出清-江北分区节点边际电价均价';


-- bi_js_data.rt_clear_lmp_jn definition

CREATE TABLE IF NOT EXISTS `rt_clear_lmp_jn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `rt_clear_lmp_jn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时出清-江南分区节点边际电价均价';


-- bi_js_data.rt_clear_power definition

CREATE TABLE IF NOT EXISTS `rt_clear_power`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `rt_clear_power_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时出清-实时出清电力';


-- bi_js_data.rt_clear_price_jb definition

CREATE TABLE IF NOT EXISTS `rt_clear_price_jb`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `rt_clear_price_jb_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时出清-江北分区价格';


-- bi_js_data.rt_clear_price_jn definition

CREATE TABLE IF NOT EXISTS `rt_clear_price_jn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                         DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `value`       decimal(10, 6)                                               DEFAULT NULL COMMENT '值',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `rt_clear_price_jn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时出清-江南分区价格';


-- bi_js_data.screen_trade_settlement definition

CREATE TABLE IF NOT EXISTS `screen_trade_settlement`
(
    `id`                           int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `station_id`                   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `year`                         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '年份',
    `month`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '月份',
    `settle_power`                 decimal(15, 2)                                                DEFAULT NULL COMMENT '结算电量',
    `settle_electricity_fee`       decimal(15, 2)                                                DEFAULT NULL COMMENT '结算电费',
    `assessment_fee`               decimal(15, 2)                                                DEFAULT NULL COMMENT '双细则考核电费',
    `settlement_average_price`     decimal(10, 4)                                                DEFAULT NULL COMMENT '结算均价',
    `bench_mark_electricity_price` decimal(10, 4)                                                DEFAULT NULL COMMENT '标杆电价',
    `limited_power`                decimal(15, 2)                                                DEFAULT NULL COMMENT '限电量',
    `current_month_power`          decimal(15, 2)                                                DEFAULT NULL COMMENT '当月发电量',
    `current_month_plan_power`     decimal(15, 2)                                                DEFAULT NULL COMMENT '当月计划发电量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `screen_trade_settlement_unique` (`station_id`,`year`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易结算信息';


-- bi_js_data.strategy_data definition

CREATE TABLE IF NOT EXISTS `strategy_data`
(
    `strategy_id` bigint unsigned NOT NULL COMMENT '关联策略基本信息表id',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '时间',
    `power`       decimal(20, 6) DEFAULT NULL COMMENT '功率预测值',
    `factor`      decimal(10, 2) DEFAULT NULL COMMENT '因数',
    UNIQUE KEY `strategy_data_unique` (`strategy_id`,`time`),
    KEY           `strategy_data_strategy_id_IDX` (`strategy_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略数据表';


-- bi_js_data.strategy_declaration_record definition

CREATE TABLE IF NOT EXISTS `strategy_declaration_record`
(
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '电站id',
    `declaration_date` date         DEFAULT NULL COMMENT '申报日期',
    `strategy_id`      bigint unsigned DEFAULT NULL COMMENT '申报的策略id',
    `create_time`      datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256) DEFAULT NULL COMMENT '更新人',
    KEY                `strategy_declaration_record_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略申报记录表';


-- bi_js_data.strategy_info definition

CREATE TABLE IF NOT EXISTS `strategy_info`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `strategy_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '策略名称',
    `type`             tinyint unsigned DEFAULT NULL COMMENT '策略类型',
    `declaration_date` date                                                          DEFAULT NULL COMMENT '策略关联的申报日期',
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `create_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `strategy_info_unique` (`declaration_date`,`station_id`,`type`),
    KEY                `strategy_info_declaration_date_IDX` (`declaration_date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略基本信息表';


-- bi_js_data.strategy_quotation_price_curve definition

CREATE TABLE IF NOT EXISTS `strategy_quotation_price_curve`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`       bigint unsigned NOT NULL COMMENT '电站id',
    `declaration_date` date NOT NULL COMMENT '申报日期',
    `start_power`      decimal(10, 2) DEFAULT NULL COMMENT '开始出力',
    `end_power`        decimal(10, 2) DEFAULT NULL COMMENT '结束出力',
    `price`            decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `price_cap`        decimal(10, 2) DEFAULT NULL COMMENT '价格上限',
    `price_floor`      decimal(10, 2) DEFAULT NULL COMMENT '价格下限',
    `create_time`      datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256)   DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY                `strategy_quotation_price_curve_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略报价曲线';


-- bi_js_data.system_load_daily definition

CREATE TABLE IF NOT EXISTS `system_load_daily`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date           DEFAULT NULL,
    `time`        varchar(10)    DEFAULT NULL,
    `value`       decimal(10, 2) DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `system_load_daily_unique` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统负荷预测（日）情况';


-- bi_js_data.trade_calendar_record definition

CREATE TABLE IF NOT EXISTS `trade_calendar_record`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_id`     int unsigned DEFAULT NULL COMMENT '关联交易类型id',
    `trade_date`  date         DEFAULT NULL COMMENT '交易日期',
    `start_time`  varchar(256) DEFAULT NULL COMMENT '开始时间（HH:mm）',
    `end_time`    varchar(256) DEFAULT NULL COMMENT '结束时间（HH:mm）',
    `remark`      text COMMENT '备注',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易日历任务记录表';


-- bi_js_data.trade_type definition

CREATE TABLE IF NOT EXISTS `trade_type`
(
    `id`    int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `label` varchar(256) DEFAULT NULL COMMENT '交易类型名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易类型';


-- bi_js_data.upload_ali_file_record definition

CREATE TABLE IF NOT EXISTS `upload_ali_file_record`
(
    `file_id`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '上传到阿里百炼平台上的文件id',
    `file_name`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件名',
    `file_url`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件上传到minio的路径',
    `file_md5`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '文件md5值',
    `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误信息',
    `has_success`   tinyint unsigned DEFAULT '1' COMMENT '是否解析成功0失败 1成功',
    `has_delete`    tinyint unsigned DEFAULT '0' COMMENT '是否在阿里云百炼oss上删除 0未删除 1已删除',
    `create_time`   datetime                                                       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    PRIMARY KEY (`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传到阿里百炼平台的文件记录表';


-- bi_js_data.yearly_power_forecast definition

CREATE TABLE IF NOT EXISTS `yearly_power_forecast`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`     bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `year`           varchar(256)   DEFAULT NULL COMMENT '年份',
    `month`          varchar(256)   DEFAULT NULL COMMENT '月份',
    `forecast_value` decimal(20, 6) DEFAULT NULL COMMENT '预测发电值',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(256)   DEFAULT NULL,
    `update_by`      varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY              `yearly_power_plan_year_IDX` (`year`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度发电量预测';


-- bi_js_data.yearly_power_plan definition

CREATE TABLE IF NOT EXISTS `yearly_power_plan`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `year`         varchar(256)   DEFAULT NULL COMMENT '年份',
    `month`        varchar(256)   DEFAULT NULL COMMENT '月份',
    `plan_value`   decimal(20, 6) DEFAULT NULL COMMENT '计划发电值',
    `actual_value` decimal(20, 6) DEFAULT NULL COMMENT '实际发电值',
    `create_time`  datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    varchar(256)   DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY            `yearly_power_plan_year_IDX` (`year`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度发电计划';

INSERT IGNORE INTO bi_js_data.contract_period
(id, period_name)
VALUES(1, '年度合同');
INSERT IGNORE INTO bi_js_data.contract_period
(id, period_name)
VALUES(2, '月度合同');
INSERT IGNORE INTO bi_js_data.contract_period
(id, period_name)
VALUES(3, '月内合同');

INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(1, '电能量-年度挂牌');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(2, '电能量-年度双边协商');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(3, '电能量-月度集中竞价');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(4, '电能量-月内连续挂牌');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(5, '电能量-月度合同转让');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(6, '电能量-月内合同转让');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(7, '绿电年度双边协商');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(8, '绿电月度双边协商');
INSERT IGNORE INTO bi_js_data.contract_type
(id, type_name)
VALUES(9, '绿电月内双边协商');

INSERT IGNORE INTO bi_js_data.notice_type
(id, type_name, create_time, update_time, create_by, update_by)
VALUES(1, '公众', '2025-04-24 10:32:49', '2025-04-24 10:32:49', NULL, NULL);
INSERT IGNORE INTO bi_js_data.notice_type
(id, type_name, create_time, update_time, create_by, update_by)
VALUES(2, '公开', '2025-04-24 10:32:49', '2025-04-24 10:32:49', NULL, NULL);

INSERT IGNORE INTO bi_js_data.trade_type
(id, label)
VALUES(1, '月内连续交易');
INSERT IGNORE INTO bi_js_data.trade_type
(id, label)
VALUES(2, '双边协商交易');
INSERT IGNORE INTO bi_js_data.trade_type
(id, label)
VALUES(3, '集中竞价交易');

-- bi_anhui.strategy_adjust_data definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_data` (
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `curve_code` varchar(50) DEFAULT NULL COMMENT '引用数据code',
    UNIQUE KEY `strategy_adjust_data_unique` (`strategy_adjust_id`,`curve_code`),
    KEY `strategy_adjust_data_strategy_adjust_id_IDX` (`strategy_adjust_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议引用数据表';


-- bi_anhui.strategy_adjust_file definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_file` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `file_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '附件url',
    `file_name` varchar(100) DEFAULT NULL COMMENT '附件名称',
    PRIMARY KEY (`id`),
    KEY `strategy_adjust_file_strategy_adjust_id_IDX` (`strategy_adjust_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议关联附件表';


-- bi_anhui.strategy_adjust_info definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_info` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `target_date` date DEFAULT NULL COMMENT '标的日',
    `type` varchar(10) DEFAULT NULL COMMENT '策略建议类型 0 日前申报 1 滚动撮合',
    `strategy_content` text COMMENT '策略建议内容',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(10) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(10) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `strategy_adjust_info_target_date_IDX` (`target_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议信息';


-- bi_anhui.strategy_adjust_station definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_station` (
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `target_date` date DEFAULT NULL COMMENT '标的日',
    `type` varchar(10) DEFAULT NULL COMMENT '策略建议类型',
    UNIQUE KEY `strategy_adjust_station_unique` (`target_date`,`station_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站关联策略建议表';