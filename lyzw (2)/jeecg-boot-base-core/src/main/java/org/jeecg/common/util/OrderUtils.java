package org.jeecg.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

public class OrderUtils {
    // 使用原子计数器确保同一毫秒内的唯一性
    private static final AtomicInteger counter = new AtomicInteger(0);

    // 日期时间格式
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter TIME_FORMAT = DateTimeFormatter.ofPattern("HHmmssSSS");

    // 字符集用于生成更短的订单号
    private static final char[] BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();

    /**
     * 生成订单号
     *
     * @param userId 用户ID
     * @return 唯一订单号
     */
    public static String generateOrderNo(String userId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String timePart = LocalDateTime.now().format(formatter);

        // 2. 生成6位随机数（范围：000000-999999）
        String randomPart = generateRandomDigits(6);

        // 3. 组合成订单号
        return "wx" + timePart + randomPart;
    }

    /**
     * 生成指定位数的随机数字字符串
     *
     * @param length 随机数长度
     * @return 指定位数的随机数字字符串
     */
    private static String generateRandomDigits(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("长度必须大于0");
        }

        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10)); // 生成0-9的随机数
        }

        return sb.toString();
    }

    /**
     * 格式1: 日期 + 时间 + 用户ID + 随机数 (22位)
     * 示例: 20231031-153045123-10001-3847
     */
    private static String generateType1(LocalDateTime now, long userId) {
        String datePart = now.format(DATE_FORMAT); // 8位日期
        String timePart = now.format(TIME_FORMAT); // 9位时间(含毫秒)
        String userPart = String.format("%05d", userId % 100000); // 5位用户ID
        String randomPart = String.format("%04d", ThreadLocalRandom.current().nextInt(10000)); // 4位随机数

        return datePart + timePart + userPart + randomPart;
    }

    /**
     * 格式2: 时间戳 + 用户ID + 计数器 (18-20位)
     * 示例: 231031153045123-10001-42
     */
    private static String generateType2(LocalDateTime now, long userId) {
        // 缩短的日期时间格式: 年(后2位)月日时分秒毫秒
        String timestamp = String.format("%02d%02d%02d%02d%02d%03d",
                now.getYear() % 100,
                now.getMonthValue(),
                now.getDayOfMonth(),
                now.getHour(),
                now.getMinute(),
                now.getSecond(),
                now.getNano() / 1_000_000);

        // 用户ID取后5位
        String userPart = String.format("%05d", userId % 100000);

        // 重置计数器如果日期变化了
        resetCounterIfNeeded(now);

        // 获取序列号并递增
        int seq = counter.getAndIncrement() % 10000;

        return timestamp + userPart + String.format("%04d", seq);
    }

    /**
     * 格式3: 短格式订单号 (12位)
     * 示例: A3b9K7x2Y5z8
     */
    private static String generateShortFormat(LocalDateTime now, String userId) {
        // 获取当前时间戳(毫秒)
        long timestamp = System.currentTimeMillis();

        // 计算用户ID的哈希码
        int userIdHash = userId.hashCode();

        // 生成随机数
        int randomInt = ThreadLocalRandom.current().nextInt();

        // 混合
        long mixed = timestamp;
        mixed = mixed * 31 + userIdHash;
        mixed = mixed * 31 + randomInt;

        // 转换为62进制缩短长度
        return toBase62(mixed);
    }

    /**
     * 将长整型转换为62进制字符串
     */
    private static String toBase62(long value) {
        StringBuilder sb = new StringBuilder(12);
        do {
            int remainder = (int) (value % 62);
            sb.append(BASE62_CHARS[remainder]);
            value /= 62;
        } while (value > 0);

        // 反转字符串并填充到固定长度
        String result = sb.reverse().toString();
        return String.format("%1$12s", result).replace(' ', '0');
    }

    /**
     * 检查是否需要重置计数器(每天重置)
     */
    private static void resetCounterIfNeeded(LocalDateTime now) {
        // 每天凌晨重置计数器
        if (now.getHour() == 0 && now.getMinute() == 0 && now.getSecond() == 0) {
            counter.set(0);
        }
    }
}
