package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: Entity基类
 * @Author: <EMAIL>
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SpeakNaturalPhonics implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字母顺序")
    private Integer sequence;
    @ApiModelProperty(value = "字母")
    private String letter;
    @ApiModelProperty(value = "字母的音标")
    private String phonics;
    @ApiModelProperty(value = "发音的要点")
    private String tip;
    @ApiModelProperty(value = "发音的要点")
    private String lips;
    @ApiModelProperty(value = "发音的要点")
    private String tongue;
    @ApiModelProperty(value = "音频文件")
    private String audioUrl;
}
