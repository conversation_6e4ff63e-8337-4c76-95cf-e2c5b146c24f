package org.jeecg.common.util;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ClearUtils {

    /**
     * 通用电量聚合方法
     *
     * @param list           数据明细
     * @param dimension      维度(day/month/year)
     * @param dateExtractor  获取日期的方法
     * @param valueExtractor 获取电量的方法
     * @return 聚合结果
     */
    public static <T> Object aggregate(List<T> list, String dimension, Function<T, Date> dateExtractor, Function<T, BigDecimal> valueExtractor) {
        if ("day".equalsIgnoreCase(dimension)) {
            // 直接返回明细
            return list;
        } else if ("month".equalsIgnoreCase(dimension)) {
            // 按天分组求和
            Map<String, Double> daySum = list.stream()
                    .collect(Collectors.groupingBy(
                            e -> new SimpleDateFormat("yyyy-MM-dd").format(dateExtractor.apply(e)),
                            Collectors.summingDouble(e -> Optional.ofNullable(valueExtractor.apply(e)).orElse(BigDecimal.ZERO).doubleValue())
                    ));
            return daySum.entrySet().stream()
                    .map(e -> {
                        Map<String, Object> m = new HashMap<>();
                        m.put("date", e.getKey());
                        m.put("totalValue", e.getValue());
                        return m;
                    })
                    .sorted(Comparator.comparing(m -> (String) m.get("date")))
                    .collect(Collectors.toList());
        } else if ("year".equalsIgnoreCase(dimension)) {
            // 按月分组求和
            Map<String, Double> monthSum = list.stream()
                    .collect(Collectors.groupingBy(
                            e -> new SimpleDateFormat("yyyy-MM").format(dateExtractor.apply(e)),
                            Collectors.summingDouble(e -> Optional.ofNullable(valueExtractor.apply(e)).orElse(BigDecimal.ZERO).doubleValue())
                    ));
            return monthSum.entrySet().stream()
                    .map(e -> {
                        Map<String, Object> m = new HashMap<>();
                        m.put("month", e.getKey());
                        m.put("totalValue", e.getValue());
                        return m;
                    })
                    .sorted(Comparator.comparing(m -> (String) m.get("month")))
                    .collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("不支持的维度参数: " + dimension);
        }
    }
}
