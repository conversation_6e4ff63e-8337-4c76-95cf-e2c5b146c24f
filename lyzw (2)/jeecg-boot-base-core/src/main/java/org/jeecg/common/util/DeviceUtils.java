package org.jeecg.common.util;

import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.jeecg.common.system.base.entity.DeviceInfo;

public class DeviceUtils {

    // 单例模式初始化（高性能）
    private static final UserAgentAnalyzer analyzer = UserAgentAnalyzer
            .newBuilder()
            .hideMatcherLoadStats()
            .withCache(10000)
            .build();

    public static DeviceInfo parse(String userAgent) {
        UserAgent agent = analyzer.parse(userAgent);
        return new DeviceInfo(
                agent.getValue("DeviceName"),        // 设备名称（如 "iPhone 15 Pro"）
                agent.getValue("OperatingSystemNameVersion"), // 操作系统（如 "iOS 17.4.1"）
                agent.getValue("AgentNameVersion")   // 浏览器（如 "Chrome 123.0.6312.87"）
        );
    }

}
