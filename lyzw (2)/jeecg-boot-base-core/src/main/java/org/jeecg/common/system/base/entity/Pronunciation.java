package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Pronunciation implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "英音")
    private String en;
    @ApiModelProperty(value = "美音")
    private String us;
}
