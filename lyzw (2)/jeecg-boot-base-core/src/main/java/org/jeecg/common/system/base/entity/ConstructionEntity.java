package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ConstructionEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文章")
    private String article;

    @ApiModelProperty(value = "中文释义")
    private String chMeaning;

    @ApiModelProperty(value = "问题列表")
    private List<QuestionEntity> questionList;

}
