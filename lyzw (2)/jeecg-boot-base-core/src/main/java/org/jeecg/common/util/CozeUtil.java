package org.jeecg.common.util;

import com.alibaba.fastjson.JSON;
import org.apache.http.Header;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.entity.mime.ByteArrayBody;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.HttpEntity;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import java.io.File;
import java.net.URI;
import java.io.InputStream;

@Slf4j
public class CozeUtil {

    // Coze AI API 基础配置
    private static final String BASE_URL_V1 = "https://api.coze.cn/v1/";
    private static final String BASE_URL_V3 = "https://api.coze.cn/v3/";

    /**
     * 获取智能体配置
     *
     * @param token       访问令牌，如果为空则使用默认令牌
     * @param isPublished 是否获取已发布版本
     * @return 智能体配置信息
     */
    public static JSONObject getBotConfig(String botId, String token, Boolean isPublished) {

        String url = BASE_URL_V1 + "bots/" + botId;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);

            // 设置请求头
            httpGet.setHeader("Authorization", "Bearer " + token);
            httpGet.setHeader("Content-Type", "application/json");

            // 添加查询参数
            if (isPublished != null && isPublished) {
                httpGet.setURI(URI.create(url + "?is_published=true"));
            }

            log.info("正在获取智能体配置，URL: {}", httpGet.getURI());

            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                log.info("成功获取智能体配置，响应长度: {}", responseBody.length());
                return JSONObject.parseObject(responseBody);

            } else {
                HttpEntity entity = response.getEntity();
                String errorBody = entity != null ? EntityUtils.toString(entity, StandardCharsets.UTF_8) : "";
                log.error("获取智能体配置失败，状态码: {}, 错误信息: {}", statusCode, errorBody);
                return null;
            }

        } catch (Exception e) {
            log.error("获取智能体配置时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取智能体配置 - 简化方法
     *
     * @param botId 智能体ID
     * @return 智能体配置信息
     */
    public static JSONObject getBotConfig(String botId) {
        return getBotConfig(botId, null, null);
    }

    /**
     * 创建会话
     *
     * @param token 访问令牌
     * @return 会话信息
     */
    public static JSONObject createConversation(String token) {
        String url = BASE_URL_V1 + "conversation/create";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json");

            // 设置请求体
            JSONObject requestBody = new JSONObject();
            // 可以添加其他参数，如果API需要的话

            StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            log.info("正在创建会话，URL: {}", url);

            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("成功创建会话，响应: {}", responseBody);
                return JSONObject.parseObject(responseBody);

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ? EntityUtils.toString(responseEntity, StandardCharsets.UTF_8)
                        : "";
                log.error("创建会话失败，状态码: {}, 错误信息: {}", statusCode, errorBody);
                return null;
            }

        } catch (Exception e) {
            log.error("创建会话时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查API响应是否成功
     *
     * @param response API响应
     * @return 是否成功
     */
    public static boolean isResponseSuccess(JSONObject response) {
        if (response == null) {
            return false;
        }

        // 检查是否有错误码
        if (response.containsKey("code")) {
            int code = response.getIntValue("code");
            return code == 0; // 通常0表示成功
        }

        // 检查是否有错误信息
        if (response.containsKey("error")) {
            return false;
        }

        return true;
    }

    /**
     * 发起对话 (V3 API)
     *
     * @param botId  智能体ID
     * @param userId 用户ID
     * @param token  访问令牌
     * @return 对话响应
     */
    public static JSONObject startChatV3(String botId, String userId, String token) {
        String methodTrace = "startChatV3_" + System.currentTimeMillis();

        log.info("[{}] === 开始调用startChatV3 ===", methodTrace);
        log.info("[{}] 输入参数 - botId: {}, userId: {}, token: {}",
                methodTrace, botId, userId, token != null ? "***已提供***" : "使用默认");

        if (botId == null || botId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 智能体ID为空或空字符串", methodTrace);
            return createErrorResponse("INVALID_BOT_ID", "智能体ID不能为空");
        }

        if (userId == null || userId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 用户ID为空或空字符串", methodTrace);
            return createErrorResponse("INVALID_USER_ID", "用户ID不能为空");
        }

        String url = BASE_URL_V3 + "chat";

        log.info("[{}] 请求配置 - URL: {}, Token长度: {}",
                methodTrace, url, token.length());

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("User-Agent", "CozeUtil/1.0");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("bot_id", botId);
            requestBody.put("user_id", userId);

            StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            log.info("[{}] 发送HTTP请求 - Method: POST, URL: {}", methodTrace, url);
            log.info("[{}] 请求头 - Authorization: Bearer ***, Content-Type: application/json", methodTrace);
            log.info("[{}] 请求体: {}", methodTrace, JSONObject.toJSONString(requestBody));

            long requestStart = System.currentTimeMillis();
            HttpResponse response = httpClient.execute(httpPost);
            long requestDuration = System.currentTimeMillis() - requestStart;

            int statusCode = response.getStatusLine().getStatusCode();
            String reasonPhrase = response.getStatusLine().getReasonPhrase();

            log.info("[{}] HTTP响应 - 状态码: {}, 原因: {}, 耗时: {}ms",
                    methodTrace, statusCode, reasonPhrase, requestDuration);

            // 记录响应头信息
            Header[] headers = response.getAllHeaders();
            for (Header header : headers) {
                log.debug("[{}] 响应头 - {}: {}", methodTrace, header.getName(), header.getValue());
            }

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("[{}] 请求成功 - 响应长度: {} 字符", methodTrace, responseBody.length());
                log.debug("[{}] 响应内容: {}", methodTrace, responseBody);

                try {
                    JSONObject jsonResponse = JSONObject.parseObject(responseBody);
                    log.info("[{}] JSON解析成功", methodTrace);

                    // 验证响应结构
                    if (jsonResponse.containsKey("data")) {
                        JSONObject data = jsonResponse.getJSONObject("data");
                        String conversationId = data.getString("conversation_id");
                        String chatId = data.getString("id");
                        log.info("[{}] 响应数据验证 - conversationId: {}, chatId: {}",
                                methodTrace, conversationId, chatId);
                    } else {
                        log.warn("[{}] 响应中缺少data字段", methodTrace);
                    }

                    return jsonResponse;

                } catch (Exception parseEx) {
                    log.error("[{}] JSON解析失败，原始响应: {}", methodTrace, responseBody, parseEx);
                    return createErrorResponse("JSON_PARSE_ERROR", "响应解析失败: " + parseEx.getMessage());
                }

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ?
                        EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : "";

                log.error("[{}] HTTP请求失败 - 状态码: {}, 原因: {}", methodTrace, statusCode, reasonPhrase);
                log.error("[{}] 错误响应体: {}", methodTrace, errorBody);

                // 尝试解析错误响应
                try {
                    if (!errorBody.isEmpty()) {
                        JSONObject errorJson = JSONObject.parseObject(errorBody);
                        log.error("[{}] 解析后的错误信息: {}", methodTrace, errorJson.toJSONString());
                        return errorJson;
                    }
                } catch (Exception e) {
                    log.error("[{}] 错误响应解析失败: {}", methodTrace, e.getMessage());
                }

                return createErrorResponse("HTTP_ERROR_" + statusCode,
                        "HTTP请求失败: " + statusCode + " " + reasonPhrase);
            }

        } catch (IOException e) {
            log.error("[{}] 网络IO异常: {}", methodTrace, e.getMessage(), e);
            return createErrorResponse("NETWORK_ERROR", "网络连接异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("[{}] 未知异常: {}", methodTrace, e.getMessage(), e);
            return createErrorResponse("UNKNOWN_ERROR", "未知异常: " + e.getMessage());
        }
    }

    /**
     * 发起对话并发送消息 (V3 API)
     * 这是CozeAI推荐的方式：在创建对话的同时发送用户消息
     *
     * @param botId  智能体ID
     * @param userId 用户ID
     * @param message 用户消息
     * @param token  访问令牌
     * @return 对话响应
     */
    public static JSONObject startChatWithMessage(String botId, String userId, String message, String conversationId, String token) {
        String methodTrace = "startChatWithMessage_" + System.currentTimeMillis();

        log.info("[{}] === 开始调用startChatWithMessage ===", methodTrace);
        log.info("[{}] 输入参数 - botId: {}, userId: {}, messageLength: {}",
                methodTrace, botId, userId, message != null ? message.length() : 0);

        if (botId == null || botId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 智能体ID为空", methodTrace);
            return createErrorResponse("INVALID_BOT_ID", "智能体ID不能为空");
        }

        if (userId == null || userId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 用户ID为空", methodTrace);
            return createErrorResponse("INVALID_USER_ID", "用户ID不能为空");
        }

        if (message == null || message.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 消息内容为空", methodTrace);
            return createErrorResponse("INVALID_MESSAGE", "消息内容不能为空");
        }

        String accessToken = token != null ? token : token;
        String url = BASE_URL_V3 + "chat" + "?conversation_id=" + conversationId;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Authorization", "Bearer " + accessToken);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("User-Agent", "CozeUtil/1.0");

            // 构建请求体 - 包含用户消息
            JSONObject requestBody = new JSONObject();
            requestBody.put("bot_id", botId);
            requestBody.put("user_id", userId);
            requestBody.put("stream", false);

            // 构建用户消息
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", message);
            userMessage.put("content_type", "text");

            // 添加消息到请求体
            com.alibaba.fastjson.JSONArray messages = new com.alibaba.fastjson.JSONArray();
            messages.add(userMessage);
            requestBody.put("additional_messages", messages);

            StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            log.info("[{}] 发送HTTP请求 - URL: {}", methodTrace, url);
            log.info("[{}] 请求体: {}", methodTrace, requestBody.toString());

            long requestStart = System.currentTimeMillis();
            HttpResponse response = httpClient.execute(httpPost);
            long requestDuration = System.currentTimeMillis() - requestStart;

            int statusCode = response.getStatusLine().getStatusCode();
            String reasonPhrase = response.getStatusLine().getReasonPhrase();

            log.info("[{}] HTTP响应 - 状态码: {}, 耗时: {}ms", methodTrace, statusCode, requestDuration);

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("[{}] 请求成功 - 响应长度: {} 字符", methodTrace, responseBody.length());
                log.debug("[{}] 响应内容: {}", methodTrace, responseBody);

                try {
                    JSONObject jsonResponse = JSONObject.parseObject(responseBody);
                    log.info("[{}] JSON解析成功", methodTrace);

                    // 验证响应结构
                    if (jsonResponse.containsKey("data")) {
                        JSONObject data = jsonResponse.getJSONObject("data");
                        String chatId = data.getString("id");
                        log.info("[{}] 响应数据验证 - conversationId: {}, chatId: {}",
                                methodTrace, conversationId, chatId);
                    }

                    return jsonResponse;

                } catch (Exception parseEx) {
                    log.error("[{}] JSON解析失败: {}", methodTrace, parseEx.getMessage(), parseEx);
                    return createErrorResponse("JSON_PARSE_ERROR", "响应解析失败: " + parseEx.getMessage());
                }

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ?
                        EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : "";

                log.error("[{}] HTTP请求失败 - 状态码: {}, 错误: {}", methodTrace, statusCode, errorBody);

                try {
                    if (!errorBody.isEmpty()) {
                        JSONObject errorJson = JSONObject.parseObject(errorBody);
                        return errorJson;
                    }
                } catch (Exception e) {
                    log.error("[{}] 错误响应解析失败", methodTrace);
                }

                return createErrorResponse("HTTP_ERROR_" + statusCode,
                        "HTTP请求失败: " + statusCode + " " + reasonPhrase);
            }

        } catch (Exception e) {
            log.error("[{}] 请求异常: {}", methodTrace, e.getMessage(), e);
            return createErrorResponse("EXCEPTION", "请求异常: " + e.getMessage());
        }
    }

    /**
     * 调用CozeAI流式API，返回流式响应InputStream
     */
    public static InputStream streamChatWithMessage(String botId, String userId, String message, String conversationId, String token) throws Exception {
        String url = BASE_URL_V3 + "chat?stream=true";
        if (conversationId != null && !conversationId.trim().isEmpty()) {
            url += "&conversation_id=" + conversationId;
        }
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);

        httpPost.setHeader("Authorization", "Bearer " + token);
        httpPost.setHeader("Content-Type", "application/json");

        JSONObject requestBody = new JSONObject();
        requestBody.put("bot_id", botId);
        requestBody.put("user_id", userId);

        // 构建用户消息
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", message);
        userMessage.put("content_type", "text");
        com.alibaba.fastjson.JSONArray messages = new com.alibaba.fastjson.JSONArray();
        messages.add(userMessage);
        requestBody.put("additional_messages", messages);

        httpPost.setEntity(new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8));

        HttpResponse response = httpClient.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            throw new RuntimeException("CozeAI流式API请求失败，状态码: " + statusCode);
        }
        // 返回流式响应体
        return response.getEntity().getContent();
    }


    /**
     * 创建消息 (V1 API)
     *
     * @param conversationId 会话ID
     * @param role           角色 (user/assistant)
     * @param contentType    内容类型 (text/image等)
     * @param content        消息内容
     * @param token          访问令牌
     * @return 创建消息的响应
     */
    public static JSONObject createMessage(String conversationId, String role, String contentType,
                                           String content, String token) {
        String methodTrace = "createMessage_" + System.currentTimeMillis();

        log.info("[{}] === 开始调用createMessage ===", methodTrace);
        log.info("[{}] 输入参数 - conversationId: {}, role: {}, contentType: {}, contentLength: {}",
                methodTrace, conversationId, role, contentType, content != null ? content.length() : 0);

        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 会话ID为空", methodTrace);
            return createErrorResponse("INVALID_CONVERSATION_ID", "会话ID不能为空");
        }

        if (content == null || content.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 消息内容为空", methodTrace);
            return createErrorResponse("INVALID_CONTENT", "消息内容不能为空");
        }

        String url = BASE_URL_V1 + "conversation/message/create?conversation_id=" + conversationId;

        log.info("[{}] 请求配置 - URL: {}", methodTrace, url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);

            // 设置请求头
            httpPost.setHeader("Authorization", "Bearer " + token);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("role", role != null ? role : "user");
            requestBody.put("content_type", contentType != null ? contentType : "text");
            requestBody.put("content", content);

            StringEntity entity = new StringEntity(requestBody.toString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            log.info("[{}] 发送请求 - 请求体: {}", methodTrace, JSONObject.toJSONString(requestBody));

            long requestStart = System.currentTimeMillis();
            HttpResponse response = httpClient.execute(httpPost);
            long requestDuration = System.currentTimeMillis() - requestStart;

            int statusCode = response.getStatusLine().getStatusCode();

            log.info("[{}] HTTP响应 - 状态码: {}, 耗时: {}ms", methodTrace, statusCode, requestDuration);

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("[{}] 消息创建成功 - 响应长度: {}", methodTrace, responseBody.length());
                log.debug("[{}] 响应内容: {}", methodTrace, responseBody);

                try {
                    return JSONObject.parseObject(responseBody);
                } catch (Exception parseEx) {
                    log.error("[{}] 响应解析失败: {}", methodTrace, parseEx.getMessage(), parseEx);
                    return createErrorResponse("PARSE_ERROR", "响应解析失败");
                }

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ?
                        EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : "";

                log.error("[{}] 消息创建失败 - 状态码: {}, 错误内容: {}", methodTrace, statusCode, errorBody);

                try {
                    if (!errorBody.isEmpty()) {
                        return JSONObject.parseObject(errorBody);
                    }
                } catch (Exception e) {
                    log.error("[{}] 错误响应解析失败", methodTrace);
                }

                return createErrorResponse("HTTP_ERROR_" + statusCode, "HTTP错误: " + statusCode);
            }

        } catch (Exception e) {
            log.error("[{}] 创建消息异常: {}", methodTrace, e.getMessage(), e);
            return createErrorResponse("EXCEPTION", "创建消息异常: " + e.getMessage());
        }
    }

    /**
     * 创建用户消息 - 简化方法
     *
     * @param conversationId 会话ID
     * @param content        消息内容
     * @return 创建消息的响应
     */
    public static JSONObject createUserMessage(String conversationId, String content,String token) {
        return createMessage(conversationId, "user", "text", content, token);
    }

    /**
     * 创建助手消息
     *
     * @param conversationId 会话ID
     * @param content        消息内容
     * @return 创建消息的响应
     */
    public static JSONObject createAssistantMessage(String conversationId, String content,String token) {
        return createMessage(conversationId, "assistant", "text", content, token);
    }

    /**
     * 获取会话历史
     *
     * @param conversationId 会话ID
     * @param token          访问令牌
     * @return 会话历史信息
     */
    public static JSONObject getConversationHistory(String conversationId, String token) {
        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.error("会话ID不能为空");
            return null;
        }

        String url = BASE_URL_V1 + "conversation/message/list?conversation_id=" + conversationId;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);

            // 设置请求头
            httpGet.setHeader("Authorization", "Bearer " + token);
            httpGet.setHeader("Content-Type", "application/json");

            log.info("正在获取会话历史，URL: {}", url);

            HttpResponse response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.info("成功获取会话历史，响应长度: {}", responseBody.length());
                return JSONObject.parseObject(responseBody);

            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ? EntityUtils.toString(responseEntity, StandardCharsets.UTF_8)
                        : "";
                log.error("获取会话历史失败，状态码: {}, 错误信息: {}", statusCode, errorBody);
                return null;
            }

        } catch (Exception e) {
            log.error("获取会话历史时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查看对话详情 - V3 API
     *
     * @param conversationId 会话ID
     * @param chatId         对话ID
     * @param token          访问令牌
     * @return 对话详情
     */
    public static JSONObject retrieveChatDetails(String conversationId, String chatId, String token) {
        String methodTrace = "retrieveChatDetails_" + System.currentTimeMillis();

        log.debug("[{}] === 开始调用retrieveChatDetails ===", methodTrace);
        log.debug("[{}] 输入参数 - conversationId: {}, chatId: {}", methodTrace, conversationId, chatId);

        // 参数验证
        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 会话ID为空", methodTrace);
            return createErrorResponse("INVALID_CONVERSATION_ID", "会话ID不能为空");
        }
        if (chatId == null || chatId.trim().isEmpty()) {
            log.error("[{}] 参数验证失败: 对话ID为空", methodTrace);
            return createErrorResponse("INVALID_CHAT_ID", "对话ID不能为空");
        }

        String url = BASE_URL_V3 + "chat/retrieve";

        log.debug("[{}] 请求URL: {}", methodTrace, url);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("conversation_id", conversationId)
                    .addParameter("chat_id", chatId);
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            httpGet.setHeader("Authorization", "Bearer " + token);
            httpGet.setHeader("Content-Type", "application/json");

            log.debug("[{}] 最终请求URL: {}", methodTrace, httpGet.getURI().toString());

            long requestStart = System.currentTimeMillis();
            HttpResponse response = httpClient.execute(httpGet);
            long requestDuration = System.currentTimeMillis() - requestStart;

            int statusCode = response.getStatusLine().getStatusCode();

            log.debug("[{}] HTTP响应 - 状态码: {}, 耗时: {}ms", methodTrace, statusCode, requestDuration);

            if (statusCode == 200) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);

                log.debug("[{}] 获取对话详情成功 - 响应长度: {}", methodTrace, responseBody.length());

                try {
                    JSONObject result = JSONObject.parseObject(responseBody);

                    // 记录关键状态信息
                    if (result.containsKey("data")) {
                        JSONObject data = result.getJSONObject("data");
                        String status = data.getString("status");
                        log.debug("[{}] 对话状态: {}", methodTrace, status);
                    }

                    return result;
                } catch (Exception parseEx) {
                    log.error("[{}] 响应解析失败: {}", methodTrace, parseEx.getMessage());
                    return createErrorResponse("PARSE_ERROR", "响应解析失败");
                }
            } else {
                HttpEntity responseEntity = response.getEntity();
                String errorBody = responseEntity != null ?
                        EntityUtils.toString(responseEntity, StandardCharsets.UTF_8) : "";

                log.error("[{}] 获取对话详情失败 - 状态码: {}, 错误: {}", methodTrace, statusCode, errorBody);

                try {
                    if (!errorBody.isEmpty()) {
                        return JSONObject.parseObject(errorBody);
                    }
                } catch (Exception e) {
                    log.error("[{}] 错误响应解析失败", methodTrace);
                }

                return createErrorResponse("HTTP_ERROR_" + statusCode, "HTTP错误: " + statusCode);
            }
        } catch (Exception e) {
            log.error("[{}] 获取对话详情异常: {}", methodTrace, e.getMessage(), e);
            return createErrorResponse("EXCEPTION", "获取对话详情异常: " + e.getMessage());
        }
    }

    /**
     * 创建统一的错误响应格式
     */
    private static JSONObject createErrorResponse(String errorCode, String errorMessage) {
        JSONObject errorResponse = new JSONObject();
        errorResponse.put("code", errorCode);
        errorResponse.put("msg", errorMessage);
        errorResponse.put("success", false);
        errorResponse.put("timestamp", System.currentTimeMillis());
        return errorResponse;
    }
}
