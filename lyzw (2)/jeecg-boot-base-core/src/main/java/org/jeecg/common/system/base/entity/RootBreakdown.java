package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RootBreakdown implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "拆分单词")
    private String letter;

    @ApiModelProperty(value = "中文含义")
    private String type;

    @ApiModelProperty(value = "原型")
    private String origin;

    @ApiModelProperty(value = "中文含义")
    private String function;
}
