package org.jeecg.common.exception;

/**
 * @Description: jeecg-boot自定义401异常
 * @author: jeecg-boot
 */
public class JeecgBoot1001Exception extends RuntimeException {
	private static final long serialVersionUID = 1L;

	public JeecgBoot1001Exception(String message){
		super(message);
	}

	public JeecgBoot1001Exception(Throwable cause)
	{
		super(cause);
	}

	public JeecgBoot1001Exception(String message, Throwable cause)
	{
		super(message,cause);
	}
}
