package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Collocation implements Serializable {
    @ApiModelProperty(value = "字母")
    private String english;
    @ApiModelProperty(value = "字母")
    private String chinese;
    @ApiModelProperty(value = "音频地址")
    private String audioUrl;
}
