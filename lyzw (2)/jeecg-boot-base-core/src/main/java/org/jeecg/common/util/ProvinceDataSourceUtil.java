package org.jeecg.common.util;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

public class ProvinceDataSourceUtil {
    private static final Map<Integer, String> provinceDataSourceMap = new HashMap<>();
    private static final Map<Integer, String> provinceNameMap = new HashMap<>();

    String originalDataSource = DynamicDataSourceContextHolder.peek();

    static {
        provinceDataSourceMap.put(0, "master"); //0-全国
        provinceDataSourceMap.put(1, "jiangsu"); // 1-江苏
        provinceDataSourceMap.put(2, "anhui");   // 2-安徽
        // 后续添加其他省份
        provinceNameMap.put(0, "全国");
        provinceNameMap.put(1, "江苏");
        provinceNameMap.put(2, "安徽");
    }

    public static String getDataSourceKey(Integer provinceId) {
        return provinceDataSourceMap.get(provinceId);
    }

    public static Map<Integer, String> getAllProvinceDataSource() {
        return provinceDataSourceMap;
    }

    public static String getProvinceName(Integer provinceId) {
        return provinceNameMap.get(provinceId);
    }

    public static <T> List<T> queryAllProvinces(Supplier<List<T>> queryFunc) {
        List<T> result = new ArrayList<>();
        Map<Integer, String> provinceDataSourceMap = getAllProvinceDataSource();
        for (Map.Entry<Integer, String> entry : provinceDataSourceMap.entrySet()) {
            String dsKey = entry.getValue();
            DynamicDataSourceContextHolder.push(dsKey);
            try {
                result.addAll(queryFunc.get());
            } finally {
                DynamicDataSourceContextHolder.clear();
            }
        }
        return result;
    }
}
