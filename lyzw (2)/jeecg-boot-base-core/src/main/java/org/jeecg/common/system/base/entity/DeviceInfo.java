package org.jeecg.common.system.base.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
public class DeviceInfo {
    private String deviceModel;
    private String os;
    private String browser;
}
