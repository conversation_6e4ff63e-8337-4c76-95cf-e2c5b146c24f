package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: Entity基类
 * @Author: dang<PERSON><PERSON><PERSON>@163.com
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class NaturalPhonics implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字母顺序")
    private Integer sequence;
    @ApiModelProperty(value = "字母")
    private String letter;
    @ApiModelProperty(value = "字母的音标")
    private String phonics;
    @ApiModelProperty(value = "发音类型")
    private String type;
}
