package org.jeecg.common.util;

/**
 * <AUTHOR>
 * @date : 2025/7/14
 * @Describe :
 */

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class LocalCacheUtil {
    private  final Map<String, CacheItem> cache = new ConcurrentHashMap<>();
    private  final ScheduledExecutorService cleaner = Executors.newSingleThreadScheduledExecutor();

     {
        cleaner.scheduleAtFixedRate(() -> {
            long now = System.currentTimeMillis();
            cache.entrySet().removeIf(entry -> entry.getValue().expireAt > 0 && entry.getValue().expireAt < now);
        }, 1, 1, TimeUnit.MINUTES);
    }

    public  void set(String key, Object value) {
        cache.put(key, new CacheItem(value, -1));
    }

    public  void set(String key, Object value, long timeoutSeconds) {
        long expireAt = System.currentTimeMillis() + timeoutSeconds * 1000;
        cache.put(key, new CacheItem(value, expireAt));
    }

    public  Object get(String key) {
        CacheItem item = cache.get(key);
        if (item == null || (item.expireAt > 0 && item.expireAt < System.currentTimeMillis())) {
            cache.remove(key);
            return null;
        }
        return item.value;
    }

    public  void del(String key) {
        cache.remove(key);
    }

    public  boolean hasKey(String key) {
        return get(key) != null;
    }

    private  class CacheItem {
        Object value;
        long expireAt;

        CacheItem(Object value, long expireAt) {
            this.value = value;
            this.expireAt = expireAt;
        }
    }
    public  boolean expire(String key, long timeoutSeconds) {
        CacheItem item = cache.get(key);
        if (item == null) {
            return false;
        }
        item.expireAt = System.currentTimeMillis() + timeoutSeconds * 1000;
        return true;
    }

    public  void removeAll(String prefix) {
        for (String key : cache.keySet()) {
            if (key.startsWith(prefix)) {
                cache.remove(key);
            }
        }
    }

    public  long getExpire(String key) {
        CacheItem item = cache.get(key);
        if (item == null) {
            return -2; // key 不存在
        }
        if (item.expireAt < 0) {
            return -1; // 永不过期
        }
        long remain = (item.expireAt - System.currentTimeMillis()) / 1000;
        return Math.max(remain, 0); // 避免返回负数
    }

}