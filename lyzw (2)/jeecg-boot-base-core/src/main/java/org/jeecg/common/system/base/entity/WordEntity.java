package org.jeecg.common.system.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: Entity基类
 * @Author: <EMAIL>
 * @Date: 2019-4-28
 * @Version: 1.1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WordEntity implements Serializable {
    private static final long serialVersionUID = 1L;



    @ApiModelProperty(value = "")
    private String word;

    @ApiModelProperty(value = "")
    private Pronunciation pronunciation;

    @ApiModelProperty(value = "")
    private List<String> pronunciationGuide;

    @ApiModelProperty(value = "")
    private List<NaturalPhonics> naturalPhonics;

    @ApiModelProperty(value = "")
    private List<SpeakNaturalPhonics> speakNaturalPhonics;

    @ApiModelProperty(value = "")
    private Map<String,String> rootParticles;

    @ApiModelProperty(value = "")
    private List<RootBreakdown> rootBreakdown;

    @ApiModelProperty(value = "")
    private String rootParticlesMean;

    @ApiModelProperty(value = "")
    private List<PartOfSpeech> partOfSpeech;

    @ApiModelProperty(value = "")
    private List<WordTransformation> wordTransformation;

    @ApiModelProperty(value = "")
    private List<Collocation> collocations;

    @ApiModelProperty(value = "")
    private List<Example> examples;

    @ApiModelProperty(value = "")
    private List<Etymology> etymology;

    @ApiModelProperty(value = "视频链接地址")
    private String audioUrl;

    @ApiModelProperty(value = "")
    private String homophonic;

    @ApiModelProperty(value = "")
    private String usAudioUrl;

    @ApiModelProperty(value = "")
    private String naturalAudioUrl;

    @ApiModelProperty(value = "")
    private String breakdownAudioUrl;

}
