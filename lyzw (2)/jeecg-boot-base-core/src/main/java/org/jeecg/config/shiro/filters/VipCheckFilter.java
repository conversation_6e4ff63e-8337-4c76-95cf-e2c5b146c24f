package org.jeecg.config.shiro.filters;

import org.apache.shiro.web.filter.authc.BasicHttpAuthenticationFilter;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.TokenUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

public class VipCheckFilter extends BasicHttpAuthenticationFilter {

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue){
        try {
            TokenUtils.verifyVipTime();
            return true;
        } catch (Exception e) {
            JwtUtil.responseError(response, 402, e.getMessage());
            return false;
        }
    }
}