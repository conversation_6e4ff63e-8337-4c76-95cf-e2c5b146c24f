CREATE
DATABASE IF NOT EXISTS `forecast_power_report`;

USE
`forecast_power_report`;

-- forecast_power_report.bsfcstdata_15m_gn definition

CREATE TABLE IF NOT EXISTS `bsfcstdata_15m_gn`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `data_time`  datetime       DEFAULT NULL COMMENT '预测数据时间',
    `date`       date           DEFAULT NULL COMMENT '日期',
    `time`       varchar(10)    DEFAULT NULL COMMENT '时间',
    `value`      decimal(10, 2) DEFAULT NULL COMMENT '超短期预测功率',
    `version`    bigint unsigned DEFAULT NULL COMMENT '版本',
    PRIMARY KEY (`id`),
    UNIQUE KEY `bsfcstdata_15m_gn_unique` (`data_time`,`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='超短期数据国能';


-- forecast_power_report.dq definition

CREATE TABLE IF NOT EXISTS `dq`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                         DEFAULT NULL COMMENT '日期',
    `time`         varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间',
    `value`        decimal(10, 2)                                               DEFAULT NULL COMMENT '数据值',
    `update_value` decimal(10, 2)                                               DEFAULT NULL COMMENT '修改后数据',
    `station_id`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场站编号',
    `version`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '当前数据版本',
    `update_time`  datetime                                                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `dq_unique` (`date`,`time`,`station_id`,`version`),
    KEY            `dq_date_IDX` (`date`,`station_id`,`version`) USING BTREE,
    KEY            `dq_idx_time` (`time`),
    KEY            `idx_station_date_version` (`station_id`,`date`,`version`),
    KEY            `idx_station_date_version_time` (`station_id`,`date`,`version`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短期';


-- forecast_power_report.env definition

CREATE TABLE IF NOT EXISTS `env`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(256)   DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '数据值',
    `station_id`  varchar(256)   DEFAULT NULL COMMENT '场站编号',
    `version`     varchar(256)   DEFAULT NULL COMMENT '当前数据版本',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='环境';


-- forecast_power_report.expection_MWH definition

CREATE TABLE IF NOT EXISTS `expection_MWH`
(
    `station_id`   bigint unsigned DEFAULT NULL,
    `station_name` varchar(256) DEFAULT NULL,
    `date`         date         DEFAULT NULL,
    `time`         varchar(256) DEFAULT NULL,
    `value`        int          DEFAULT NULL COMMENT '预期值',
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- forecast_power_report.forecast_job_status definition

CREATE TABLE IF NOT EXISTS `forecast_job_status`
(
    `id`            int  NOT NULL AUTO_INCREMENT,
    `job_date`      date NOT NULL,
    `last_run_date` date NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `job_date` (`job_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- forecast_power_report.lfarmdata_15m_gn definition

CREATE TABLE IF NOT EXISTS `lfarmdata_15m_gn`
(
    `id`                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`         bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`               date           DEFAULT NULL COMMENT '日期',
    `time`               varchar(10)    DEFAULT NULL COMMENT '时间',
    `lfp`                decimal(10, 2) DEFAULT NULL COMMENT '电场功率',
    `irradiance`         decimal(10, 2) DEFAULT NULL COMMENT '辐照度',
    `instant_farm_power` decimal(10, 2) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `lfarmdata_15m_gn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电场平均辐照度国能';


-- forecast_power_report.lfarmdatatheory_1m_gn definition

CREATE TABLE IF NOT EXISTS `lfarmdatatheory_1m_gn`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`       date           DEFAULT NULL COMMENT '日期',
    `time`       varchar(10)    DEFAULT NULL COMMENT '时间',
    `mtheoryp`   decimal(10, 2) DEFAULT NULL COMMENT '样板法理论',
    `itheoryp`   decimal(10, 2) DEFAULT NULL COMMENT '测光法理论',
    `mdtheoryp`  decimal(10, 2) DEFAULT NULL COMMENT '样板法可用',
    `idtheoryp`  decimal(10, 2) DEFAULT NULL COMMENT '测光法可用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `lfarmdatatheory_1m_gn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='理论功率光伏国能';


-- forecast_power_report.light_power_gn definition

CREATE TABLE IF NOT EXISTS `light_power_gn`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`          date                                                         DEFAULT NULL COMMENT '日期',
    `time`          varchar(10)                                                  DEFAULT NULL COMMENT '时间',
    `version`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本号',
    `station_id`    bigint unsigned DEFAULT NULL COMMENT '电站id',
    `power`         decimal(10, 6)                                               DEFAULT NULL,
    `radiate`       decimal(10, 6)                                               DEFAULT NULL,
    `wind_speed`    decimal(10, 6)                                               DEFAULT NULL,
    `temperature`   decimal(10, 6)                                               DEFAULT NULL,
    `humidity`      decimal(10, 6)                                               DEFAULT NULL,
    `pressure`      decimal(10, 6)                                               DEFAULT NULL,
    `low_radiation` decimal(10, 6)                                               DEFAULT NULL,
    `scatter`       decimal(10, 6)                                               DEFAULT NULL,
    `wind_dir`      decimal(10, 6)                                               DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `light_power_gn_unique` (`date`,`time`,`version`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='国能光伏功率预测';


-- forecast_power_report.light_power_gn_copy1 definition

CREATE TABLE IF NOT EXISTS `light_power_gn_copy1`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`          date                                                         DEFAULT NULL COMMENT '日期',
    `time`          varchar(10)                                                  DEFAULT NULL COMMENT '时间',
    `version`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本号',
    `station_id`    bigint unsigned DEFAULT NULL COMMENT '电站id',
    `power`         decimal(10, 6)                                               DEFAULT NULL,
    `radiate`       decimal(10, 6)                                               DEFAULT NULL,
    `wind_speed`    decimal(10, 6)                                               DEFAULT NULL,
    `temperature`   decimal(10, 6)                                               DEFAULT NULL,
    `humidity`      decimal(10, 6)                                               DEFAULT NULL,
    `pressure`      decimal(10, 6)                                               DEFAULT NULL,
    `low_radiation` decimal(10, 6)                                               DEFAULT NULL,
    `scatter`       decimal(10, 6)                                               DEFAULT NULL,
    `wind_dir`      decimal(10, 6)                                               DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `light_power_gn_unique` (`date`,`time`,`version`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='国能光伏功率预测';


-- forecast_power_report.manual_upload_forecast_power definition

CREATE TABLE IF NOT EXISTS `manual_upload_forecast_power`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(10)    DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '预测值',
    `version`     varchar(10)    DEFAULT NULL COMMENT '版本',
    `create_time` datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `manual_upload_forecast_power_unique` (`date`,`time`,`station_id`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='手动上传功率预测';


-- forecast_power_report.meteorology definition

CREATE TABLE IF NOT EXISTS `meteorology`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `station_id`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '站点ID',
    `date`              date                                                         DEFAULT NULL COMMENT '日期',
    `time`              varchar(10)                                                  DEFAULT NULL COMMENT '时间',
    `version`           varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据版本（接受日期）',
    `swr`               decimal(10, 2)                                               DEFAULT NULL COMMENT '总辐射（W/m²）',
    `direct_radiation`  decimal(10, 2)                                               DEFAULT NULL COMMENT '直辐射（W/m²）',
    `diffuse_radiation` decimal(10, 2)                                               DEFAULT NULL COMMENT '散辐射（W/m²）',
    `t`                 decimal(5, 2)                                                DEFAULT NULL COMMENT '温度（℃）',
    `p`                 decimal(7, 2)                                                DEFAULT NULL COMMENT '气压（hPa）',
    `rh`                decimal(5, 2)                                                DEFAULT NULL COMMENT '湿度（%）',
    `ws10`              decimal(6, 2)                                                DEFAULT NULL COMMENT '10米风速（m/s）',
    `ws30`              decimal(6, 2)                                                DEFAULT NULL COMMENT '30米风速（m/s）',
    `ws50`              decimal(6, 2)                                                DEFAULT NULL COMMENT '50米风速（m/s）',
    `ws70`              decimal(6, 2)                                                DEFAULT NULL COMMENT '70米风速（m/s）',
    `ws80`              decimal(6, 2)                                                DEFAULT NULL COMMENT '80米风速（m/s）',
    `ws90`              decimal(6, 2)                                                DEFAULT NULL COMMENT '90米风速（m/s）',
    `ws100`             decimal(6, 2)                                                DEFAULT NULL COMMENT '100米风速（m/s）',
    `ws170`             decimal(6, 2)                                                DEFAULT NULL COMMENT '170米风速（m/s）',
    `wd10`              decimal(5, 2)                                                DEFAULT NULL COMMENT '10米风向（°）',
    `wd30`              decimal(5, 2)                                                DEFAULT NULL COMMENT '30米风向（°）',
    `wd50`              decimal(5, 2)                                                DEFAULT NULL COMMENT '50米风向（°）',
    `wd70`              decimal(5, 2)                                                DEFAULT NULL COMMENT '70米风向（°）',
    `wd80`              decimal(5, 2)                                                DEFAULT NULL COMMENT '80米风向（°）',
    `wd90`              decimal(5, 2)                                                DEFAULT NULL COMMENT '90米风向（°）',
    `wd100`             decimal(5, 2)                                                DEFAULT NULL COMMENT '100米风向（°）',
    `wd170`             decimal(5, 2)                                                DEFAULT NULL COMMENT '170米风向（°）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `meteorology_unique` (`station_id`,`date`,`time`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='气象数据';


-- forecast_power_report.mid_long_term_ratio definition

CREATE TABLE IF NOT EXISTS `mid_long_term_ratio`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)   DEFAULT NULL COMMENT '电站名称',
    `month`        varchar(100)   DEFAULT NULL COMMENT '月份',
    `value`        decimal(20, 6) DEFAULT NULL COMMENT '中长期合约占比值',
    `start_date`   date           DEFAULT NULL COMMENT '每月计算结果的开始日期',
    `end_date`     date           DEFAULT NULL COMMENT '每月计算结果的结束日期',
    `summary_date` varchar(100)   DEFAULT NULL COMMENT '起始-结束日期',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中长期合约占比';


-- forecast_power_report.mstationdata_15m_gn definition

CREATE TABLE IF NOT EXISTS `mstationdata_15m_gn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(10)    DEFAULT NULL COMMENT '时间',
    `mstationid`  varchar(50)    DEFAULT NULL COMMENT '气象站id',
    `temperature` decimal(10, 2) DEFAULT NULL COMMENT '温度',
    `humidity`    decimal(10, 2) DEFAULT NULL COMMENT '湿度',
    `pressure`    decimal(10, 2) DEFAULT NULL COMMENT '压力',
    `irradiance`  decimal(10, 2) DEFAULT NULL COMMENT '倾斜辐射',
    `directr`     decimal(10, 2) DEFAULT NULL COMMENT '直辐射',
    `diffuser`    decimal(10, 2) DEFAULT NULL COMMENT '散辐射',
    `radiation`   decimal(10, 2) DEFAULT NULL COMMENT '总辐射',
    PRIMARY KEY (`id`),
    UNIQUE KEY `mstationdata_15m_gn_unique` (`date`,`time`,`station_id`,`mstationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='气象站数据光伏国能';


-- forecast_power_report.power_limit_rate_pred definition

CREATE TABLE IF NOT EXISTS `power_limit_rate_pred`
(
    `id`                  bigint unsigned NOT NULL AUTO_INCREMENT,
    `station_id`          bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电站名称',
    `date`                date                                                          DEFAULT NULL COMMENT '日期',
    `new_energy_forecast` decimal(20, 6)                                                DEFAULT NULL COMMENT '新能源预测值',
    `is_weekly`           tinyint unsigned DEFAULT NULL COMMENT '是否工作日',
    `t`                   decimal(5, 2)                                                 DEFAULT NULL COMMENT '温度（℃）',
    `p`                   decimal(7, 2)                                                 DEFAULT NULL COMMENT '气压（hPa）',
    `swr`                 decimal(10, 2)                                                DEFAULT NULL COMMENT '总辐射（W/m²）',
    `rh`                  decimal(5, 2)                                                 DEFAULT NULL COMMENT '湿度（%）',
    `ws100`               decimal(6, 2)                                                 DEFAULT NULL COMMENT '100米风速（m/s）',
    `power_limit_rate` double DEFAULT NULL COMMENT '限电率',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- forecast_power_report.realpower_15m_gn definition

CREATE TABLE IF NOT EXISTS `realpower_15m_gn`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`       date           DEFAULT NULL COMMENT '日期',
    `time`       varchar(10)    DEFAULT NULL COMMENT '时间',
    `value`      decimal(10, 2) DEFAULT NULL COMMENT '并网功率',
    PRIMARY KEY (`id`),
    UNIQUE KEY `realpower_15m_gn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实发数据国能';


-- forecast_power_report.rp definition

CREATE TABLE IF NOT EXISTS `rp`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(256)   DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '数据值',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型 0平均值 1瞬时值',
    `station_id`  varchar(256)   DEFAULT NULL COMMENT '场站编号',
    `version`     varchar(256)   DEFAULT NULL COMMENT '当前数据版本',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `rp_date_IDX` (`date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际功率';


-- forecast_power_report.st definition

CREATE TABLE IF NOT EXISTS `st`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(256)   DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '数据值',
    `station_id`  varchar(256)   DEFAULT NULL COMMENT '场站编号',
    `version`     varchar(256)   DEFAULT NULL COMMENT '当前数据版本',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `st_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='短期';


-- forecast_power_report.station definition

CREATE TABLE IF NOT EXISTS `station`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `name`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电站名称',
    `dispatch_name` varchar(256)                                                  DEFAULT NULL COMMENT '调度名称',
    `province_id`   bigint unsigned DEFAULT NULL COMMENT '省份id',
    `station_no`    varchar(256)                                                  DEFAULT NULL COMMENT '沈阳嘉越提供的场站编号',
    `capacity`      decimal(10, 2)                                                DEFAULT NULL COMMENT '装机容量',
    `type`          tinyint unsigned DEFAULT NULL COMMENT '电站类型 0 光伏 1 风电',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站';


-- forecast_power_report.ta definition

CREATE TABLE IF NOT EXISTS `ta`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(256)   DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '数据值',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型 0理论功率 1可用功率',
    `station_id`  varchar(256)   DEFAULT NULL COMMENT '场站编号',
    `version`     varchar(256)   DEFAULT NULL COMMENT '当前数据版本',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='理论可用数据';


-- forecast_power_report.ust definition

CREATE TABLE IF NOT EXISTS `ust`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(256)   DEFAULT NULL COMMENT '时间',
    `value`       decimal(10, 2) DEFAULT NULL COMMENT '数据值',
    `station_id`  varchar(256)   DEFAULT NULL COMMENT '场站编号',
    `version`     varchar(256)   DEFAULT NULL COMMENT '当前数据版本',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='超短期';


-- forecast_power_report.wfarmdata_15m_gn definition

CREATE TABLE IF NOT EXISTS `wfarmdata_15m_gn`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`       date           DEFAULT NULL COMMENT '日期',
    `time`       varchar(10)    DEFAULT NULL COMMENT '时间',
    `wfp`        decimal(10, 2) DEFAULT NULL COMMENT '电场功率',
    `wind_speed` decimal(10, 2) DEFAULT NULL COMMENT '电场平均风速',
    PRIMARY KEY (`id`),
    UNIQUE KEY `wfarmdata_15m_gn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电场平均风速国能';


-- forecast_power_report.wfarmdatatheory_1m_gn definition

CREATE TABLE IF NOT EXISTS `wfarmdatatheory_1m_gn`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`         date           DEFAULT NULL COMMENT '日期',
    `time`         varchar(10)    DEFAULT NULL COMMENT '时间',
    `mtheoryp`     decimal(10, 2) DEFAULT NULL COMMENT '样板法理论',
    `wtheoryp`     decimal(10, 2) DEFAULT NULL COMMENT '测风塔外推法理论',
    `wturatheoryp` decimal(10, 2) DEFAULT NULL COMMENT '机头风速法理论',
    `wturtheoryp`  decimal(10, 2) DEFAULT NULL COMMENT '机头风速法可用',
    `mdtheoryp`    decimal(10, 2) DEFAULT NULL COMMENT '样板法可用',
    `wdtheoryp`    decimal(10, 2) DEFAULT NULL COMMENT '测风塔外推法可用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `wfarmdatatheory_1m_gn_unique` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='理论功率风电国能';


-- forecast_power_report.wind_power_gn definition

CREATE TABLE IF NOT EXISTS `wind_power_gn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `time`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '时间',
    `version`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '版本号',
    `station_id`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电站id',
    `power`       decimal(10, 6)                                                DEFAULT NULL,
    `wind_speed`  decimal(10, 6)                                                DEFAULT NULL,
    `wind_dir`    decimal(10, 6)                                                DEFAULT NULL,
    `temperature` decimal(10, 6)                                                DEFAULT NULL,
    `humidity`    decimal(10, 6)                                                DEFAULT NULL,
    `pressure`    decimal(10, 6)                                                DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `wind_power_gn_unique` (`date`,`time`,`version`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='国能风电功率预测';


-- forecast_power_report.wtowerdata_15m_gn definition

CREATE TABLE IF NOT EXISTS `wtowerdata_15m_gn`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `date`        date           DEFAULT NULL COMMENT '日期',
    `time`        varchar(10)    DEFAULT NULL COMMENT '时间',
    `wtowerid`    varchar(50)    DEFAULT NULL COMMENT '测风塔id',
    `uavg`        decimal(10, 2) DEFAULT NULL COMMENT '测风塔风速',
    `vavg`        decimal(10, 2) DEFAULT NULL COMMENT '测风塔风向',
    `temperature` decimal(10, 2) DEFAULT NULL COMMENT '测风塔温度',
    `humidity`    decimal(10, 2) DEFAULT NULL COMMENT '测风塔湿度',
    `pressure`    decimal(10, 2) DEFAULT NULL COMMENT '测风塔压力',
    PRIMARY KEY (`id`),
    UNIQUE KEY `wtowerdata_15m_gn_unique` (`date`,`time`,`station_id`,`wtowerid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='测风塔数据风电国能';