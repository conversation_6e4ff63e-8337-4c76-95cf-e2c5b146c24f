CREATE
DATABASE IF NOT EXISTS `bi`;

USE
`bi`;

-- bi.chart_data_info definition

CREATE TABLE IF NOT EXISTS `chart_data_info`
(
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标编码',
    `curve_code`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '曲线编码',
    `province_id` int unsigned DEFAULT NULL COMMENT '省份id',
    `table_name`  varchar(100) DEFAULT NULL COMMENT '数据表名',
    `data_name`   varchar(100) DEFAULT NULL COMMENT '数据名称',
    `column_list` varchar(500) DEFAULT NULL COMMENT '参与筛选条件字段名列表',
    `area`        varchar(10) DEFAULT NULL COMMENT '当前曲线所属区域 空表示没有区域',
    UNIQUE KEY `chart_data_info_unique` (`code`,`curve_code`,`table_name`,`data_name`,`province_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='图表数据信息';

-- bi.curve_data_info definition

CREATE TABLE IF NOT EXISTS `curve_data_info` (
    `curve_code` varchar(50) DEFAULT NULL COMMENT '曲线code',
    `curve_name` varchar(50) DEFAULT NULL COMMENT '曲线名称',
    `table_name` varchar(50) DEFAULT NULL COMMENT '曲线对应表名',
    `province_id` int unsigned DEFAULT NULL COMMENT '省份id',
    `column_list` varchar(100) DEFAULT NULL COMMENT '参与筛选条件字段名列表',
    `date_column` varchar(10) DEFAULT NULL COMMENT '日期列',
    `time_column` varchar(10) DEFAULT NULL COMMENT '时间列',
    `value_column` varchar(10) DEFAULT NULL COMMENT '值列',
    UNIQUE KEY `curve_data_info_unique` (`curve_code`,`province_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='曲线数据信息';

-- bi.exam_option definition

CREATE TABLE IF NOT EXISTS `exam_option`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `question_id`  bigint unsigned DEFAULT NULL COMMENT '关联试卷题目id',
    `content`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '选项内容',
    `correct_flag` tinyint unsigned DEFAULT NULL COMMENT '是否正确答案 0错误 1正确',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷选项表';


-- bi.exam_paper definition

CREATE TABLE IF NOT EXISTS `exam_paper`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `ep_name`     varchar(100) DEFAULT NULL COMMENT '培训名称',
    `exam_name`   varchar(100) DEFAULT NULL COMMENT '考试名称',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `paper_id`    varchar(50)  DEFAULT NULL COMMENT '题库中试卷唯一id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷信息表';


-- bi.exam_partition definition

CREATE TABLE IF NOT EXISTS `exam_partition`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `paper_id`       bigint unsigned DEFAULT NULL COMMENT '关联试卷id',
    `partition_name` varchar(50) DEFAULT NULL COMMENT '试卷分区名称（单选题/多选题/判断题）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷分区表';


-- bi.exam_question definition

CREATE TABLE IF NOT EXISTS `exam_question`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `partition_id` bigint unsigned DEFAULT NULL COMMENT '关联试卷分区id',
    `type_code`    varchar(100)  DEFAULT NULL COMMENT '题型编码',
    `content`      text COMMENT '题干内容',
    `score`        decimal(5, 2) DEFAULT NULL COMMENT '题目分值',
    `analysis`     text COMMENT '答案解析',
    `sort_num`     bigint unsigned DEFAULT NULL COMMENT '题目排序号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='试卷题目表';


-- bi.export_task definition

CREATE TABLE IF NOT EXISTS `export_task`
(
    `id`                 bigint                                                       NOT NULL AUTO_INCREMENT,
    `person_number`      varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工号',
    `province_id`        int unsigned DEFAULT NULL COMMENT '省份id',
    `type`               int                                                          NOT NULL COMMENT '任务类型 1：文件  2：通知  3：公告',
    `code`               varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '任务编号（导出文件编号）',
    `task_template_type` int                                                                   DEFAULT NULL COMMENT '模板类型',
    `task_name`          varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
    `status`             int                                                          NOT NULL DEFAULT '2' COMMENT '任务状态 1：已完成  2：进行中  3：失败  4：失效',
    `context`            varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci        DEFAULT NULL COMMENT '任务描述',
    `create_time`        datetime                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `freeze_time`        datetime                                                              DEFAULT NULL COMMENT '结束时间',
    `read_status`        int                                                                   DEFAULT '0' COMMENT '消息读取状态  0：未读   1：已读',
    `stop_time`          datetime                                                              DEFAULT NULL COMMENT '消息推送截止日期',
    `source`             varchar(10)                                                           DEFAULT NULL COMMENT '来源 web  ',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;


-- bi.forecast_power_factory definition

CREATE TABLE IF NOT EXISTS `forecast_power_factory`
(
    `id`           int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `factory_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '厂家名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='功率预测厂家信息';


-- bi.import_dict_ah definition

CREATE TABLE IF NOT EXISTS `import_dict_ah`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `code`        varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '导入数据的code',
    `time_diff`   int                                                          NOT NULL COMMENT '导入日期和数据日期之间的时间差,30为每月一次',
    `name`        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '导入数据的描述',
    `table_name`  varchar(60)                                                  NOT NULL COMMENT '表名',
    `column_name` varchar(60) DEFAULT NULL COMMENT '字段名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='安徽插件导入字典';


-- bi.import_dict_station_ah definition

CREATE TABLE IF NOT EXISTS `import_dict_station_ah`
(
    `id`         int NOT NULL AUTO_INCREMENT,
    `dict_id`    int    DEFAULT NULL COMMENT '导入字典表id',
    `station_id` bigint DEFAULT NULL COMMENT '关联电站',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- bi.settle_main definition

CREATE TABLE IF NOT EXISTS `settle_main`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `b_unit_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '结算单元名称',
    `province_id` int unsigned DEFAULT NULL COMMENT '省份id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- bi.settle_station definition

CREATE TABLE IF NOT EXISTS `settle_station`
(
    `settle_id`  bigint NOT NULL COMMENT '结算单元名称',
    `station_id` bigint NOT NULL COMMENT '电站名称',
    PRIMARY KEY (`settle_id`, `station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- bi.station definition

CREATE TABLE IF NOT EXISTS `station`
(
    `id`                           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `name`                         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '电站名称',
    `province_id`                  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `station_no`                   varchar(256)                                                  DEFAULT NULL COMMENT '沈阳嘉越提供的场站编号',
    `trade_status`                 tinyint unsigned DEFAULT '0' COMMENT '是否开启电力交易 0 未开启 1 开启',
    `power_status`                 tinyint unsigned DEFAULT '0' COMMENT '是否有电力预测 0否 1是',
    `day_ahead_declaration_status` tinyint unsigned DEFAULT '0' COMMENT '是否参与日前申报 0 未开启 1 开启',
    `type`                         tinyint unsigned DEFAULT NULL COMMENT '电站类型 1 风电 2 光伏 3储能',
    `dispatch_name`                varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '调度名称',
    `capacity`                     decimal(10, 2)                                                DEFAULT NULL COMMENT '容量',
    `power`                        decimal(10, 2)                                                DEFAULT NULL COMMENT '储能电站电能 单位MWh',
    `longitude`                    decimal(10, 2)                                                DEFAULT NULL COMMENT '经度',
    `latitude`                     decimal(10, 2)                                                DEFAULT NULL COMMENT '纬度',
    `sort_no`                      int unsigned DEFAULT NULL COMMENT '排序字段',
    `create_time`                  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`                    varchar(256)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`                    varchar(256)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站';


-- bi.station_director definition

CREATE TABLE IF NOT EXISTS `station_director`
(
    `station_id`    bigint unsigned DEFAULT NULL COMMENT '电站id',
    `province_id`   int unsigned DEFAULT NULL COMMENT '省份id',
    `person_number` varchar(100) DEFAULT NULL COMMENT '负责人工号',
    `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`     varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_by`     varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`station_id`,`province_id`,`person_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站负责人表';


-- bi.station_forecast_factory definition

CREATE TABLE IF NOT EXISTS `station_forecast_factory`
(
    `station_id` bigint unsigned DEFAULT NULL,
    `factory_id` int unsigned DEFAULT NULL COMMENT '厂家id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站功率预测厂家关联表';


-- bi.sys_log definition

CREATE TABLE IF NOT EXISTS `sys_log`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_type` varchar(256) DEFAULT NULL COMMENT '操作类型',
    `username`       varchar(256) DEFAULT NULL COMMENT '操作人工号',
    `ip_address`     varchar(256) DEFAULT NULL COMMENT '操作人ip地址',
    `operate_time`   datetime     DEFAULT NULL COMMENT '操作时间',
    `params`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '请求参数',
    `result`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '执行结果',
    `class_name`     varchar(256) DEFAULT NULL,
    `method_name`    varchar(256) DEFAULT NULL COMMENT '方法名',
    `status`         varchar(256) DEFAULT NULL COMMENT '执行状态',
    `error_msg`      text COMMENT '异常信息',
    `execute_time`   bigint unsigned DEFAULT NULL COMMENT '执行耗时（ms）',
    `station_id`     bigint       DEFAULT NULL COMMENT '电站id（校验导入数据，可能为空）',
    `param_code`     varchar(15)  DEFAULT NULL COMMENT '导入数据的code，非导入记录为空',
    `data_date`      date         DEFAULT NULL COMMENT '数据日期',
    PRIMARY KEY (`id`),
    KEY              `sys_log_operation_type_IDX` (`operation_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统操作日志';


-- bi.sys_log_js definition

CREATE TABLE IF NOT EXISTS `sys_log_js`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `operation_type` varchar(256) DEFAULT NULL COMMENT '操作类型',
    `username`       varchar(256) DEFAULT NULL COMMENT '操作人工号',
    `ip_address`     varchar(256) DEFAULT NULL COMMENT '操作人ip地址',
    `operate_time`   datetime     DEFAULT NULL COMMENT '操作时间',
    `params`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '请求参数',
    `result`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '执行结果',
    `class_name`     varchar(256) DEFAULT NULL,
    `method_name`    varchar(256) DEFAULT NULL COMMENT '方法名',
    `status`         varchar(256) DEFAULT NULL COMMENT '执行状态',
    `error_msg`      text COMMENT '异常信息',
    `execute_time`   bigint unsigned DEFAULT NULL COMMENT '执行耗时（ms）',
    PRIMARY KEY (`id`),
    KEY              `sys_log_js_operation_type_IDX` (`operation_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='江苏系统操作日志';


-- bi.sys_menu definition

CREATE TABLE IF NOT EXISTS `sys_menu`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `province_id` int unsigned DEFAULT NULL COMMENT '省份id',
    `menu_name`   varchar(256) NOT NULL COMMENT '菜单名称',
    `parent_id`   bigint unsigned DEFAULT NULL COMMENT '父菜单ID',
    `has_child`   tinyint unsigned DEFAULT '0' COMMENT '是否有子菜单 0否 1是',
    `menu_path`   varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '菜单路径',
    `permission`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '菜单权限',
    `status`      tinyint unsigned DEFAULT '1' COMMENT '状态 0停用 1启用',
    `level`       int unsigned DEFAULT NULL COMMENT '菜单层级',
    `sort_num`    bigint unsigned DEFAULT NULL COMMENT '排序字段',
    `menu_type`   varchar(256)                                                  DEFAULT NULL COMMENT '菜单类型',
    `create_by`   varchar(256)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='菜单表';


-- bi.sys_message_type definition

CREATE TABLE IF NOT EXISTS `sys_message_type`
(
    `id`        int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name` varchar(100) DEFAULT NULL COMMENT '消息类型名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消息类型';


-- bi.sys_role definition

CREATE TABLE IF NOT EXISTS `sys_role`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `role_name`   varchar(50) NOT NULL COMMENT '角色名称',
    `province_id` int unsigned DEFAULT NULL COMMENT '省份id',
    `role_code`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色编码（如admin）',
    `create_by`   varchar(256)                                                 DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256)                                                 DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `sys_role_unique` (`role_code`,`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色表';


-- bi.sys_role_menu definition

CREATE TABLE IF NOT EXISTS `sys_role_menu`
(
    `role_id`   bigint unsigned NOT NULL COMMENT '角色ID',
    `menu_id`   bigint unsigned NOT NULL COMMENT '菜单ID',
    `create_by` varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(256) DEFAULT NULL COMMENT '更新人',
    UNIQUE KEY `sys_role_menu_unique` (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色-菜单关联表';


-- bi.sys_user definition

CREATE TABLE IF NOT EXISTS `sys_user`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `person_number`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户工号',
    `name`              varchar(256) DEFAULT NULL COMMENT '用户姓名',
    `password`          varchar(256) DEFAULT NULL COMMENT '用户密码',
    `status`            tinyint unsigned DEFAULT '1' COMMENT '状态 0 停用 1 启用',
    `pwd_update_status` tinyint unsigned DEFAULT '0' COMMENT '是否修改过密码 0未修改过 1修改过',
    `create_time`       datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`         varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`         varchar(256) DEFAULT NULL COMMENT '更新人',
    `mac_key`           varchar(60)  DEFAULT NULL COMMENT '用户mac地址(首次使用ukey登陆时保存)',
    `public_key`        varbinary(2048) DEFAULT NULL COMMENT '公钥',
    `private_key`       varbinary(2048) DEFAULT NULL COMMENT '私钥',
    PRIMARY KEY (`id`),
    UNIQUE KEY `sys_user_unique` (`person_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户表';


-- bi.sys_user_role definition

CREATE TABLE IF NOT EXISTS `sys_user_role`
(
    `user_id`     bigint unsigned NOT NULL COMMENT '用户ID',
    `role_id`     bigint unsigned NOT NULL COMMENT '角色ID',
    `province_id` int unsigned NOT NULL COMMENT '省份id',
    `create_by`   varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`user_id`, `role_id`, `province_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户-角色关联表';


-- bi.ukey_log definition

CREATE TABLE IF NOT EXISTS `ukey_log`
(
    `id`               int NOT NULL AUTO_INCREMENT,
    `person_number`    varbinary(12) DEFAULT NULL,
    `mac_key`          varchar(60) DEFAULT NULL,
    `login_time`       datetime    DEFAULT NULL COMMENT '登陆时间',
    `login_ip_address` varchar(40) DEFAULT NULL COMMENT '登陆时ip地址',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

truncate table bi.chart_data_info;

INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('predict', 'generation_forecast', '发电总出力预测', 'generation_forecast', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('predict', 'non_market_forecast', '非市场机组总出力预测', 'non_market_forecast', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('predict', 'new_energy_forecast', '日前新能源总出力预测', 'new_energy_forecast', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('predict', 'hydro_forecast', '水电含抽蓄总出力预测', 'hydro_forecast', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('predict', 'system_load_daily', '日前系统负荷预测', 'system_load_daily', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('runningInfo', 'generation_output', '发电总出力', 'generation_output', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('runningInfo', 'non_market_output', '非市场机组总出力', 'non_market_output', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('runningInfo', 'new_energy_output', '新能源总出力', 'new_energy_output', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('runningInfo', 'hydro_output', '水电含抽蓄总出力', 'hydro_output', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('runningInfo', 'actual_load', '实际负荷', 'actual_load', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('clearPower', 'day_ahead_clear_power', '日前各时段出清电量', 'day_ahead_clear_power', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('clearPower', 'realtime_clear_power', '日内各时段出清电量', 'realtime_clear_power', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('marketTotalUsed', 'lastYearMarketUsage', '去年同期市场总用电量', 'lastYearMarketUsage', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthPlanPower', 'bidVolume', '成交电量', 'bidVolume', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthPlanPower', 'bidPrice', '出清价', 'bidPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthGreenPower', 'greenVolume', '成交电量', 'greenVolume', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthGreenPower', 'greenAvgPrice', '成交均价', 'greenAvgPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthInGreenPower', 'intraGreenVolume', '成交电量', 'intraGreenVolume', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthInGreenPower', 'intraGreenAvgPrice', '成交均价', 'intraGreenAvgPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthTradePower', 'monthlyTotal', '月度（内）交易总量', 'monthlyTotal', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthConsecutive', 'listingVolume', '成交电量', 'listingVolume', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthConsecutive', 'listingPrice', '成交均价', 'listingPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('countryAgent', 'statePurchaseVol', '代购电量', 'statePurchaseVol', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('countryAgent', 'statePurchasePrice', '代购价格', 'statePurchasePrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthTransfer', 'genMonthlyVol', '成交电量', 'genMonthlyVol', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthTransfer', 'genMonthlyPrice', '成交均价', 'genMonthlyPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthInTransfer', 'genIntraVol', '成交电量', 'genIntraVol', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('monthInTransfer', 'genIntraPrice', '成交均价', 'genIntraPrice', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('marketTotalUsed', 'annualPlan', '年度分月计划电量', 'annualPlan', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('marketTotalUsed', 'monthlyUsage', '用电量-分月电量', 'monthlyUsage', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'gas_generation_boundary', '燃机固定总出力值', 'gas_generation_boundary', 2, 'area', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'load_forecast_boundary', '短期系统负荷预测', 'load_forecast_boundary', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'power_receive_boundary', '受电计划', 'power_receive_boundary', 2, 'area:huaDong', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'bidding_energy', '火电竞价空间', 'bidding_energy', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'day_clear_power', '出清电力', 'day_clear_power', 2, 'station_id', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'day_clear_price_jn', '江南分区价格', 'day_clear_price_jn', 2, NULL, 'jiangNan');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'day_clear_price_jb', '江北分区价格', 'day_clear_price_jb', 2, NULL, 'jiangBei');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'day_clear_lmp_jn', '江南分区节点边际电价均价', 'day_clear_lmp_jn', 2, 'station_id', 'jiangNan');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'day_clear_lmp_jb', '江北分区节点边际电价均价', 'day_clear_lmp_jb', 2, 'station_id', 'jiangBei');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'gas_generation_clear', '燃机固定出清电力', 'gas_generation_clear', 2, 'area', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'rt_clear_power', '实时出清电力', 'rt_clear_power', 2, 'station_id', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'rt_clear_price_jn', '江南分区价格', 'rt_clear_price_jn', 2, NULL, 'jiangNan');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'rt_clear_price_jb', '江北分区价格', 'rt_clear_price_jb', 2, NULL, 'jiangBei');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'rt_clear_lmp_jn', '江南分区节点边际电价均价', 'rt_clear_lmp_jn', 2, 'station_id', 'jiangNan');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'rt_clear_lmp_jb', '江北分区节点边际电价均价', 'rt_clear_lmp_jb', 2, 'station_id', 'jiangBei');
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'gas_generation_clear', '燃机固定出清电力', 'gas_generation_clear', 2, 'area', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'renewable_forecast_clear_wind', '统调风功率出清电力', 'renewable_forecast_clear', 2, 'area,type:0', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'renewable_forecast_clear_sun', '统调光功率出清电力', 'renewable_forecast_clear', 2, 'area,type:1', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'renewable_forecast_clear_wind', '统调风功率出清电力', 'renewable_forecast_clear', 2, 'area,type:0', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'renewable_forecast_clear_sun', '统调光功率出清电力', 'renewable_forecast_clear', 2, 'area,type:1', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'new_energy', '新能源功率预测', 'new_energy', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('dayAhead', 'new_energy', '新能源出清电力', 'new_energy', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'new_energy', '新能源出清电力', 'new_energy', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'renewable_forecast_boundary_wind', '统调风功率预测', 'renewable_forecast_boundary', 2, 'area,type:0', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('supplyDemandAnalysis', 'renewable_forecast_boundary_sun', '统调光功率预测', 'renewable_forecast_boundary', 2, 'area,type:1', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'load_forecast_clear', '短期系统负荷出清电力', 'load_forecast_clear', 2, NULL, NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'actual_power_receive', '实际受电情况', 'actual_power_receive', 2, 'area:huaDong', NULL);
INSERT INTO bi.chart_data_info
(code, curve_code, data_name, table_name, province_id, column_list, area)
VALUES('realTime', 'bidding_energy', '竞价空间', 'bidding_energy', 2, NULL, NULL);

truncate table bi.curve_data_info;

INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_ahead_foreign_electricity', '日前各交易时段外来(外送)电交易计划', 'daily_ahead_foreign_electricity', 1, NULL, 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_clearing_electricity_day_ahead', '日前各交易时段出清总电量', 'daily_clearing_electricity', 1, NULL, 'date', 'time', 'rq_electricity');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_clearing_electricity_real_time', '实时各交易时段出清总电量', 'daily_clearing_electricity', 1, NULL, 'date', 'time', 'ss_electricity');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_clearing_prices_day_ahead', '日前各交易时段出清均价', 'daily_clearing_prices', 1, NULL, 'business_time', 'data_time', 'rq_price');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_clearing_prices_real_time', '实时各交易时段出清均价', 'daily_clearing_prices', 1, NULL, 'business_time', 'data_time', 'ss_price');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_clearing_price_forecast', '预测全网统一结算点日前电价', 'daily_clearing_price_forecast', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_freq_reg_service_volume_forecast', '各交易时段调频辅助服务出清总量预测', 'daily_freq_reg_service_volume_forecast', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_gen_output_forecast', '日前各交易时段发电总出力预测', 'daily_gen_output_forecast', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_hydro_forecast', '日前水电含抽蓄发电出力预测', 'daily_hydro_forecast', 1, NULL, 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_load_forecast_public', '日前各交易时段负荷预测', 'daily_load_forecast_public', 1, NULL, 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_new_energy_output_forecast_sum', '日前新能源总出力预测', 'daily_new_energy_output_forecast', 1, 'value_type:新能源总出力预测数值', 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_new_energy_output_forecast_wind', '日前风电总出力预测', 'daily_new_energy_output_forecast', 1, 'value_type:风电总出力预测数值', 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_new_energy_output_forecast_sun', '日前光伏总出力预测', 'daily_new_energy_output_forecast', 1, 'value_type:光伏总出力预测数值', 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_non_market_unit_forecast', '日前非市场机组出力预测', 'daily_non_market_unit_forecast', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_renewable_energy_output_sum', '各交易时段新能源总出力预测', 'daily_renewable_energy_output', 1, 'type:1', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_renewable_energy_output_wind', '各交易时段风电总出力预测', 'daily_renewable_energy_output', 1, 'type:2', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_renewable_energy_output_sun', '各交易时段光伏总出力预测', 'daily_renewable_energy_output', 1, 'type:3', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('daily_supply_demand_balance_forecast', '日前电力电量供需平衡预测', 'daily_supply_demand_balance_forecast', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('day_ahead_congestion_price_forecast', '日前阻塞价格预测', 'day_ahead_congestion_price_forecast', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('day_ahead_node_clear_electricity', '日前节点出清电量', 'day_ahead_node_clear_electricity', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('day_ahead_node_clear_price', '日前节点出清电价', 'day_ahead_node_clear_price', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('gen_contract_curve_decompose_price', '日前中长期交易合同分解电价曲线(发电侧)', 'gen_contract_curve_decompose', 1, 'station_id,type:1', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('gen_contract_curve_decompose_power', '日前中长期交易合同分解电量曲线(发电侧)', 'gen_contract_curve_decompose', 1, 'station_id,type:2', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('grid_real_time_backup_info_load', '实际负荷信息', 'grid_real_time_backup_info', 1, 'num_type:负荷', 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('grid_real_time_backup_info_backup', '实际备用信息', 'grid_real_time_backup_info', 1, 'num_type:正备用', 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('hydro_output_period', '各交易时段水电总出力', 'hydro_output_period', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('non_market_unit_output_curve', '非市场化机组实际出力曲线', 'non_market_unit_output_curve', 1, NULL, 'business_time', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('power_on_grid', '机组上网电量', 'power_on_grid', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('real_time_node_clear_electricity', '实时节点出清电量', 'real_time_node_clear_electricity', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('real_time_node_clear_price', '实时节点出清电价', 'real_time_node_clear_price', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('unit_actual_output', '机组实际发电出力记录', 'unit_actual_output', 1, NULL, 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('weekly_new_energy_forecast', '次周新能源出力预测', 'weekly_new_energy_forecast', 1, NULL, 'business_time', 'meas_type', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('node_price_diff_forecast', '日前价差预测', 'node_price_diff_forecast', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('day_ahead_node_price_forecast_v1', '日前价格预测D + 1', 'day_ahead_node_price_forecast_v1', 1, 'station_id', 'date', 'time', 'value');
INSERT INTO bi.curve_data_info
(curve_code, curve_name, table_name, province_id, column_list, date_column, time_column, value_column)
VALUES('day_ahead_node_price_forecast_v3', '日前价格预测D + 4', 'day_ahead_node_price_forecast_v3', 1, 'station_id', 'date', 'time', 'value');

INSERT IGNORE INTO bi.forecast_power_factory
(factory_name)
VALUES('沈阳嘉越');
INSERT IGNORE INTO bi.forecast_power_factory
(factory_name)
VALUES('国能日新');
INSERT IGNORE INTO bi.forecast_power_factory
(factory_name)
VALUES('手动上传');

INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(1, 1, '驾驶舱', 0, 0, '/screen', 'screen', 1, 1, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(2, 1, '概览', 0, 0, '/dashboard', 'dashboard', 1, 1, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(3, 1, '气象', 0, 1, '/predict', 'predict', 1, 1, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(4, 1, '天气预报', 3, 0, '/predict/meteorology', 'meteorology', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(5, 1, '功率预测', 0, 1, '/power-predict', 'power-predict', 1, 1, 4, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(6, 1, '功率预测总览', 5, 1, '/power-predict/overview', 'power-predict-overview', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(7, 1, '年度发电量预测导入', 6, 0, '/power-predict/overview', 'power-predict-overview-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(8, 1, '站端功率预测', 5, 1, '/power-predict/station', 'power-predict-station', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(9, 1, '市场披露数据', 0, 1, '/market-disclosure', 'market-disclosure', 1, 1, 5, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(10, 1, '市场公告信息', 9, 1, '/market-disclosure/notice', 'market-disclosure-notice', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(11, 1, '新增公告', 10, 0, '/market-disclosure/notice', 'market-disclosure-notice-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(12, 1, '查看公告', 10, 0, '/market-disclosure/notice', 'market-disclosure-notice-view', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(13, 1, '编辑公告', 10, 0, '/market-disclosure/notice', 'market-disclosure-notice-edit', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(14, 1, '删除公告', 10, 0, '/market-disclosure/notice', 'market-disclosure-notice-delete', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(15, 1, '市场预测信息', 9, 0, '/market-disclosure/day-ahead', 'market-disclosure-day-ahead', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(16, 1, '市场边界条件', 9, 1, '/market-disclosure/boundary-condition', 'boundary-condition', 1, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(17, 1, '投产情况', 16, 0, '/market-disclosure/boundary-condition/production-data', 'production-data', 1, 3, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(18, 1, '检修计划', 16, 0, '/market-disclosure/boundary-condition/maintenance-plan', 'maintenance-plan', 1, 3, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(19, 1, '机组数据', 16, 0, '/market-disclosure/boundary-condition/unit-data', 'unit-data', 1, 3, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(20, 1, '市场出清信息', 9, 0, '/market-disclosure/province-spot-data', 'province-spot-data', 1, 2, 4, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(21, 1, '中长期交易', 0, 1, '/mtr', 'mtr', 1, 1, 6, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(22, 1, '年度发电计划', 21, 1, '/mtr/year-power', 'year-power', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(23, 1, '发电计划导入', 22, 0, '/mtr/year-power', 'year-power-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(24, 1, '合同分解曲线', 21, 0, '/mtr/contract-line', 'contract-line', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(25, 1, '现货交易', 0, 1, '/spot-trade', 'spot-trade', 1, 1, 7, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(26, 1, '电价预测', 25, 0, '/spot-trade/power-predict', 'price-predict', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(27, 1, '相似日分析', 25, 1, '/spot-trade/similar-day', 'similar-day', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(28, 1, '查看策略', 27, 0, '/spot-trade/similar-day', 'similar-day-view', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(29, 1, '更新策略', 27, 0, '/spot-trade/similar-day', 'similar-day-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(30, 1, '日前申报', 25, 1, '/spot-trade/daily-declaration', 'daily-declaration', 1, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(31, 1, '一键粘贴', 30, 0, '/spot-trade/daily-declaration', 'daily-declaration-copy', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(32, 1, '撤销申报', 30, 0, '/spot-trade/daily-declaration', 'daily-declaration-cancel-declare', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(33, 1, '申报', 30, 0, '/spot-trade/daily-declaration', 'daily-declaration-declare', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(34, 1, '收益分析', 0, 1, '/revenue-analysis', 'revenue-analysis', 1, 1, 8, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(35, 1, '结算总览', 34, 1, '/revenue-analysis/settlement-overview', 'settlement-overview', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(36, 1, '结算单导入', 35, 0, '/revenue-analysis/settlement-overview', 'settlement-overview-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(37, 1, '日清分复盘', 34, 1, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(38, 1, '导入', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(39, 1, '导出', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-export', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(40, 1, '列设置', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-setting', 0, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(41, 1, '中长期策略复盘', 34, 1, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review', 1, 1, 9, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(42, 1, '导入', 41, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-import', 0, 2, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(43, 1, '导出', 41, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-export', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(44, 1, '列设置', 41, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-setting', 0, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(45, 1, '交易日历', 0, 1, '/trade-calendar', 'trade-calendar', 1, 1, 10, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(46, 1, '添加交易任务', 45, 0, '/trade-calendar', 'trade-calendar-add', 1, 2, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(47, 1, '删除交易任务', 45, 0, '/trade-calendar', 'trade-calendar-delete', 1, 2, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(48, 1, '合同管理', 0, 1, '/contract', 'contract', 1, 1, 11, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(49, 1, '年度合同', 48, 1, '/contract/year-contract', 'year-contract', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(50, 1, '查询', 49, 0, '/contract/year-contract', 'year-contract-query', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(51, 1, '新建合同', 49, 0, '/contract/year-contract', 'year-contract-add', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(52, 1, '详情', 49, 0, '/contract/year-contract', 'year-contract-detail', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(53, 1, '删除', 49, 0, '/contract/year-contract', 'year-contract-delete', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(54, 1, '一键导入', 49, 0, '/contract/year-contract', 'year-contract-import', 1, 3, 5, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(55, 1, '编辑', 49, 0, '/contract/year-contract', 'year-contract-edit', 1, 3, 6, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(56, 1, '日滚撮合同', 48, 1, '/contract/day-contract', 'day-contract', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(57, 1, '查询', 56, 0, '/contract/day-contract', 'day-contract-query', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(58, 1, '新建日滚撮合同', 56, 0, '/contract/day-contract', 'day-contract-add', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(59, 1, '删除', 56, 0, '/contract/day-contract', 'day-contract-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(60, 1, '日发电曲线管理', 48, 1, '/contract/curve', 'curve', 1, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(61, 1, '新增曲线', 60, 0, '/contract/curve', 'curve-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(62, 1, '删除曲线', 60, 0, '/contract/curve', 'curve-delete', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(63, 1, '台账管理', 0, 1, '/ledger', 'ledger', 1, 1, 12, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(64, 1, '电站管理', 63, 1, '/ledger/station', 'station', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(65, 1, '新增电站', 64, 0, '/ledger/station', 'station-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(66, 1, '编辑电站', 64, 0, '/ledger/station', 'station-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(67, 1, '删除电站', 64, 0, '/ledger/station', 'station-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(68, 1, '结算主体管理', 63, 1, '/ledger/settle-main', 'settle-main', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(69, 1, '新增结算主体', 68, 0, '/ledger/settle-main', 'settle-main-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(70, 1, '编辑结算主体', 68, 0, '/ledger/settle-main', 'settle-main-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(71, 1, '删除结算主体', 68, 0, '/ledger/settle-main', 'settle-main-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(72, 1, '人员管理', 0, 1, '/person', 'person', 1, 1, 13, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(73, 1, '用户管理', 72, 1, '/person/user', 'user', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(74, 1, '新增用户', 73, 0, '/person/user', 'user-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(75, 1, '编辑用户', 73, 0, '/person/user', 'user-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(76, 1, '删除用户', 73, 0, '/person/user', 'user-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(77, 1, '重置密码', 73, 0, '/person/user', 'user-reset', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(78, 1, '角色管理', 72, 1, '/person/role', 'role', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(79, 1, '新建角色', 78, 0, '/person/role', 'role-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(80, 1, '编辑角色', 78, 0, '/person/role', 'role-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(81, 1, '删除角色', 78, 0, '/person/role', 'role-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(82, 1, '数据处理', 0, 1, '/data-resolve', 'data-resolve', 1, 1, 14, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(83, 1, '数据导入', 82, 0, '/data-resolve/import', 'import', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(84, 1, '中长期持仓明细', 21, 0, '/mtr/position-detail', 'position-detail', 1, 1, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(85, 1, '导入记录', 82, 0, '/data-resolve/history', 'history', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(86, 2, '驾驶舱', 0, 0, '/screen', 'screen', 0, 1, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(87, 2, '概览', 0, 0, '/dashboard', 'dashboard', 1, 1, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(88, 2, '气象', 0, 1, '/predict', 'predict', 1, 1, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(89, 2, '天气预报', 88, 0, '/predict/meteorology', 'meteorology', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(90, 2, '功率预测', 0, 1, '/power-predict', 'power-predict', 1, 1, 4, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(91, 2, '功率预测总览', 90, 1, '/power-predict/overview', 'power-predict-overview', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(92, 2, '年度发电量预测导入', 91, 0, '/power-predict/overview', 'power-predict-overview-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(93, 2, '站端功率预测', 90, 0, '/power-predict/station', 'power-predict-station', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(94, 2, '市场披露数据', 0, 1, '/market-disclosure', 'market-disclosure', 1, 1, 5, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(95, 2, '市场公告信息', 94, 1, '/market-disclosure/notice', 'market-disclosure-notice', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(96, 2, '新增公告', 95, 0, '/market-disclosure/notice', 'market-disclosure-notice-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(97, 2, '查看公告', 95, 0, '/market-disclosure/notice', 'market-disclosure-notice-view', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(98, 2, '编辑公告', 95, 0, '/market-disclosure/notice', 'market-disclosure-notice-edit', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(99, 2, '删除公告', 95, 0, '/market-disclosure/notice', 'market-disclosure-notice-delete', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(100, 2, '预测信息', 94, 0, '/market-disclosure/predict', 'predict', 1, 2, 4, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(101, 2, '运行信息', 94, 0, '/market-disclosure/running-info', 'running-info', 1, 2, 5, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(102, 2, '省内市场信息', 94, 0, '/market-disclosure/market-info', 'market-info', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(103, 2, '检修计划', 16, 0, '/market-disclosure/boundary-condition/maintenance-plan', 'maintenance-plan', 0, 3, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(104, 2, '机组数据', 16, 0, '/market-disclosure/boundary-condition/unit-data', 'unit-data', 0, 3, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(105, 2, '出清信息', 94, 1, '/market-disclosure/settlement', 'settlement', 1, 2, 5, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(106, 2, '中长期交易', 0, 1, '/mtr', 'mtr', 1, 1, 6, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(107, 2, '年度发电计划', 106, 1, '/mtr/year-power', 'year-power', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(108, 2, '发电计划导入', 107, 0, '/mtr/year-power', 'year-power-import', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(109, 2, '合同分解曲线', 21, 0, '/mtr/contract-line', 'contract-line', 0, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(110, 2, '现货交易', 0, 1, '/spot-trade', 'spot-trade', 1, 1, 7, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(111, 2, '电价预测', 25, 0, '/spot-trade/power-predict', 'price-predict', 0, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(112, 2, '相似日分析', 25, 1, '/spot-trade/similar-day', 'similar-day', 0, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(113, 2, '查看策略', 27, 0, '/spot-trade/similar-day', 'similar-day-view', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(114, 2, '更新策略', 27, 0, '/spot-trade/similar-day', 'similar-day-edit', 0, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(115, 2, '日前申报', 110, 1, '/spot-trade/daily-declaration', 'daily-declaration', 1, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(116, 2, '一键粘贴', 115, 0, '/spot-trade/daily-declaration', 'daily-declaration-copy', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(117, 2, '撤销申报', 115, 0, '/spot-trade/daily-declaration', 'daily-declaration-cancel-declare', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(118, 2, '申报', 115, 0, '/spot-trade/daily-declaration', 'daily-declaration-declare', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(119, 2, '收益分析', 0, 1, '/revenue-analysis', 'revenue-analysis', 1, 1, 8, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(120, 2, '结算总览', 119, 1, '/revenue-analysis/settlement-overview', 'settlement-overview', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(121, 2, '结算单导入', 120, 0, '/revenue-analysis/settlement-overview', 'settlement-overview-import', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(122, 2, '日清分复盘', 34, 1, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review', 0, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(123, 2, '导入', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-import', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(124, 2, '导出', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-export', 0, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(125, 2, '列设置', 37, 0, '/revenue-analysis/daily-clearing-review', 'daily-clearing-review-setting', 0, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(126, 2, '中长期策略复盘', 119, 1, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review', 1, 1, 9, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(127, 2, '导入', 126, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-import', 0, 2, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(128, 2, '导出', 126, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-export', 0, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(129, 2, '列设置', 41, 0, '/revenue-analysis/mid-to-long-term-review', 'mid-to-long-term-review-setting', 0, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(130, 2, '交易日历', 0, 1, '/trade-calendar', 'trade-calendar', 1, 1, 10, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(131, 2, '添加交易任务', 130, 0, '/trade-calendar', 'trade-calendar-add', 1, 2, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(132, 2, '删除交易任务', 130, 0, '/trade-calendar', 'trade-calendar-delete', 1, 2, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(133, 2, '合同管理', 0, 1, '/contract', 'contract', 1, 1, 11, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(134, 2, '年度合同', 133, 1, '/contract/year-contract', 'year-contract', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(135, 2, '查询', 134, 0, '/contract/year-contract', 'year-contract-query', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(136, 2, '新建合同', 134, 0, '/contract/year-contract', 'year-contract-add', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(137, 2, '详情', 134, 0, '/contract/year-contract', 'year-contract-detail', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(138, 2, '删除', 134, 0, '/contract/year-contract', 'year-contract-delete', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(139, 2, '一键导入', 134, 0, '/contract/year-contract', 'year-contract-import', 1, 3, 5, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(140, 2, '编辑', 134, 0, '/contract/year-contract', 'year-contract-edit', 1, 3, 6, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(141, 2, '日滚撮合同', 48, 1, '/contract/day-contract', 'day-contract', 0, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(142, 2, '查询', 56, 0, '/contract/day-contract', 'day-contract-query', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(143, 2, '新建日滚撮合同', 56, 0, '/contract/day-contract', 'day-contract-add', 0, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(144, 2, '删除', 56, 0, '/contract/day-contract', 'day-contract-delete', 0, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(145, 2, '日发电曲线管理', 48, 1, '/contract/curve', 'curve', 0, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(146, 2, '新增曲线', 60, 0, '/contract/curve', 'curve-add', 0, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(147, 2, '删除曲线', 60, 0, '/contract/curve', 'curve-delete', 0, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(148, 2, '台账管理', 0, 1, '/ledger', 'ledger', 1, 1, 12, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(149, 2, '电站管理', 148, 1, '/ledger/station', 'station', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(150, 2, '新增电站', 149, 0, '/ledger/station', 'station-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(151, 2, '编辑电站', 149, 0, '/ledger/station', 'station-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(152, 2, '删除电站', 149, 0, '/ledger/station', 'station-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(153, 2, '结算主体管理', 148, 1, '/ledger/settle-main', 'settle-main', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(154, 2, '新增结算主体', 153, 0, '/ledger/settle-main', 'settle-main-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(155, 2, '编辑结算主体', 153, 0, '/ledger/settle-main', 'settle-main-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(156, 2, '删除结算主体', 153, 0, '/ledger/settle-main', 'settle-main-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(157, 2, '人员管理', 0, 1, '/person', 'person', 1, 1, 13, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(158, 2, '用户管理', 157, 1, '/person/user', 'user', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(159, 2, '新增用户', 158, 0, '/person/user', 'user-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(160, 2, '编辑用户', 158, 0, '/person/user', 'user-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(161, 2, '删除用户', 158, 0, '/person/user', 'user-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(162, 2, '重置密码', 158, 0, '/person/user', 'user-reset', 1, 3, 4, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(163, 2, '角色管理', 157, 1, '/person/role', 'role', 1, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(164, 2, '新建角色', 163, 0, '/person/role', 'role-add', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(165, 2, '编辑角色', 163, 0, '/person/role', 'role-edit', 1, 3, 2, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(166, 2, '删除角色', 163, 0, '/person/role', 'role-delete', 1, 3, 3, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(167, 2, '数据处理', 0, 1, '/data-resolve', 'data-resolve', 1, 1, 14, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(168, 2, '数据导入', 167, 0, '/data-resolve/import', 'import', 1, 2, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(169, 2, '中长期持仓明细', 21, 0, '/mtr/position-detail', 'position-detail', 0, 1, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(170, 2, '导入记录', 82, 0, '/data-resolve/history', 'history', 0, 2, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(171, 1, '手动上传预测数据', 8, 0, '/power-predict/station', 'power-predict-manual-upload', 1, 3, 1, 'button', NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(172, 2, '供需分析', 94, 0, '/market-disclosure/supply-demand-analysis', 'supply-demand-analysis', 1, 2, 3, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(173, 2, '出清汇总', 105, 0, '/market-disclosure/settlement-info', 'settlement-info', 1, 3, 1, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(174, 2, '日前出清', 105, 0, '/market-disclosure/day-ahead', 'day-ahead', 1, 3, 2, NULL, NULL, NULL);
INSERT IGNORE INTO bi.sys_menu
(id, province_id, menu_name, parent_id, has_child, menu_path, permission, status, `level`, sort_num, menu_type, create_by, update_by)
VALUES(175, 2, '实时出清', 105, 0, '/market-disclosure/real-time', 'real-time', 1, 3, 3, NULL, NULL, NULL);

INSERT IGNORE INTO bi.sys_role
(id, role_name, province_id, role_code, create_by, update_by)
VALUES(1, '系统管理员', 1, 'sys_admin', NULL, '171685');

INSERT IGNORE INTO bi.sys_user
(id, person_number, name, password, status, pwd_update_status, create_time, update_time, create_by, update_by, mac_key, public_key, private_key)
VALUES(1, '100100', '演示', '$2a$10$AkSKVjXJNCCeKDXJN/RgQupncVEE2mhZBbm4U41JCNlvSOBYHsZOe', 1, 0, '2025-03-24 17:17:17', '2025-06-03 14:11:02', '171553', '100100', '6243504abcdcd585', 0x4D494942496A414E42676B71686B6947397730424151454641414F43415138414D49494243674B43415145416D336A756649313230616A735048544C7162583279624E366F6F31544A776D4256515544786B5242436E6747656D3064614F4B334B742B44333274527A6A7137516E6B68354259626773684B317A6877766C426554575370656150372F6279754D35704C49367272615556492B354A72563638686E766A47746D57566E56654B6F33646D4634457343723373632F31396878677030696B4954597A6B325167665751514B634D61723765377A554B4A42376C6A7A6D4E577A695A554C787A586468353255517656522F483570503650375636744B6F677A517A6B344B4853585465727863476D65422F7655627230636F534B726D2F664B674C71766A577243566876396D4E33436C6F4F5942784750443570505869744F6873545261453165475551495A456F50462B5470694E4D7658707757514B4F7177426A48616F45626F687261534C35645959315756537A645A4F3741794F51494441514142, 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

-- 生成1-300的数字序列，并插入角色菜单表
INSERT IGNORE INTO bi.sys_role_menu (role_id, menu_id, create_by, update_by)
WITH RECURSIVE numbers AS (
    SELECT 1 AS n  -- 起点
    UNION ALL
    SELECT n + 1   -- 递归生成下一个数字
    FROM numbers
    WHERE n < 300  -- 终点
)
SELECT
    1 AS role_id,        -- 固定角色ID
    n AS menu_id,        -- 菜单ID（1-300）
    null AS create_by,
    null AS update_by
FROM numbers;

INSERT IGNORE INTO bi.sys_user_role
(user_id, role_id, province_id, create_by, update_by)
VALUES(1, 1, 2, NULL, NULL);