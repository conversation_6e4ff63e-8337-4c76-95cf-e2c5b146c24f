# 新能源日清分数据查询问题修复文档

## 问题描述

在查询新能源日清分数据时遇到以下问题：
1. 数据库查询返回0条记录
2. 出现"element cannot be mapped to a null key"的500错误

## 问题分析

### 1. 数据库查询问题
- 查询条件：`station_id = 2` 和 `DATE_FORMAT(date, '%Y-%m') = '2025-01'`
- **数据源映射问题**：省份ID=1对应jiangsu数据源(bi_js_data数据库)，但可能数据在其他数据源中
- **数据不存在**：目标数据源中没有2025年1月的测试数据

### 2. "element cannot be mapped to a null key"错误
- 这是MyBatis在处理查询结果时，尝试将null值作为Map的key导致的错误
- 当查询结果为空时，某些聚合操作或数据处理逻辑可能会产生null值

### 3. 数据源配置验证
根据`ProvinceDataSourceUtil`配置：
- provinceId=0 -> master数据源 (bi数据库)
- provinceId=1 -> jiangsu数据源 (bi_js_data数据库)
- provinceId=2 -> anhui数据源 (bi_anhui数据库)

## 解决方案

### 1. Controller层修复
在`StationController.getEnergyNewDailyClean`方法中：
- 添加了数据库查询异常处理
- 确保返回结果不为null，空结果时返回空列表
- 增强了日志记录，便于问题追踪

### 2. Service层修复
在`EnergyNewDailyCleanServiceImpl`中：
- 为每个查询方法添加了异常处理
- 确保所有方法都返回非null的List对象
- 添加了详细的错误日志

### 3. 代码变更详情

#### StationController.java 变更
```java
// 根据查询类型获取数据
List<EnergyNewDailyClean> result = null;
try {
    switch (queryType) {
        case "day":
            result = energyNewDailyCleanService.selectByDay(stationId, date);
            break;
        case "month":
            result = energyNewDailyCleanService.selectByMonth(stationId, date);
            break;
        case "year":
            result = energyNewDailyCleanService.selectByYear(stationId, date);
            break;
        default:
            return Result.error("查询类型错误");
    }
} catch (Exception queryException) {
    log.error("数据库查询异常 - 电站ID: {}, 日期: {}, 查询类型: {}", 
             stationId, date, queryType, queryException);
    // 返回空列表而不是抛出异常
    result = new ArrayList<>();
}

// 确保result不为null
if (result == null) {
    result = new ArrayList<>();
}
```

#### EnergyNewDailyCleanServiceImpl.java 变更
```java
@Override
public List<EnergyNewDailyClean> selectByMonth(Long stationId, String date) {
    try {
        List<EnergyNewDailyClean> result = baseMapper.selectByMonth(stationId, date);
        return result != null ? result : new ArrayList<>();
    } catch (Exception e) {
        log.error("按月查询新能源日清分数据失败 - 电站ID: {}, 日期: {}", stationId, date, e);
        return new ArrayList<>();
    }
}
```

## 测试建议

### 1. 数据准备
建议在对应的数据库中插入测试数据：

```sql
-- 为电站ID=2插入2025年1月的测试数据
INSERT INTO energy_new_daily_clean (
    date, station_id, mid_long_term_power, mid_long_term_price, 
    mid_long_term_fee, total_power, total_fee, settlement_avg_price
) VALUES 
('2025-01-01', 2, 100.00000, 0.50000, 50.00000, 95.00000, 47.50000, 0.50000),
('2025-01-02', 2, 110.00000, 0.52000, 57.20000, 105.00000, 54.60000, 0.52000),
('2025-01-03', 2, 95.00000, 0.48000, 45.60000, 90.00000, 43.20000, 0.48000);
```

### 2. API测试
使用以下参数测试API：
- 电站ID: 2
- 省份ID: 1
- 日期: 2025-01 (月度查询)

### 3. 预期结果
- 有数据时：返回对应的数据列表
- 无数据时：返回空列表，不再抛出异常
- 错误情况：返回友好的错误信息

## 部署说明

1. 重新编译项目
2. 重启应用服务
3. 验证API功能正常

## 监控建议

1. 关注相关错误日志的减少
2. 监控API响应时间和成功率
3. 定期检查数据库连接状态

## 后续优化

1. 考虑添加数据缓存机制
2. 优化数据库查询性能
3. 完善数据源切换逻辑
4. 增加数据有效性验证
