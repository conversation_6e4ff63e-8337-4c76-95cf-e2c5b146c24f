# JWT Token生成问题修复总结

## 修复时间
2025-07-27 19:50

## 问题描述
JWT Token生成时出现参数不一致的问题，可能导致token验证失败。

## 问题分析

### 根本原因
JwtUtil.sign方法中的JWT claim字段名与verify方法中的字段名不一致：

1. **sign方法**：使用 `"person_number"` 作为claim字段名
2. **verify方法**：使用 `"username"` 作为claim字段名
3. **getUsername方法**：使用 `"username"` 作为claim字段名

这种不一致会导致：
- Token生成成功，但验证失败
- 无法正确提取用户名信息
- 用户登录后可能出现认证问题

## 修复方案

### 统一JWT Claim字段名
**文件**: `jeecg-boot-base-core/src/main/java/org/jeecg/common/system/util/JwtUtil.java`

#### 修复前
```java
/**
 * 生成签名,5min后过期
 *
 * @param secret   用户的密码
 * @return 加密的token
 */
public static String sign(String person_number, String secret) {
    Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
    Algorithm algorithm = Algorithm.HMAC256(secret);
    // 附带username信息
    return JWT.create().withClaim("person_number", person_number).withExpiresAt(date).sign(algorithm);
}
```

#### 修复后
```java
/**
 * 生成签名,5min后过期
 *
 * @param username 用户名/人员编号
 * @param secret   用户的密码
 * @return 加密的token
 */
public static String sign(String username, String secret) {
    Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
    Algorithm algorithm = Algorithm.HMAC256(secret);
    // 附带username信息
    return JWT.create().withClaim("username", username).withExpiresAt(date).sign(algorithm);
}
```

### 修复效果
1. **统一字段名**：所有JWT相关方法都使用 `"username"` 作为claim字段名
2. **参数名优化**：将参数名从 `person_number` 改为 `username`，更加通用
3. **注释完善**：更新方法注释，明确参数含义

## 相关方法一致性验证

### 1. verify方法（已正确）
```java
public static boolean verify(String token, String username, String secret) {
    try {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        JWTVerifier verifier = JWT.require(algorithm).withClaim("username", username).build();
        DecodedJWT jwt = verifier.verify(token);
        return true;
    } catch (Exception e) {
        log.error(e.getMessage(), e);
        return false;
    }
}
```

### 2. getUsername方法（已正确）
```java
public static String getUsername(String token) {
    try {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim("username").asString();
    } catch (JWTDecodeException e) {
        log.warn(e.getMessage(), e);
        return null;
    }
}
```

### 3. sign方法（已修复）
现在使用统一的 `"username"` 字段名。

## UserFrontController使用验证

### 当前调用方式
```java
String token = JwtUtil.sign(front.getPersonNumber(), front.getPassword());
```

### 调用正确性
- ✅ **参数顺序正确**：第一个参数是用户标识，第二个参数是密码
- ✅ **参数类型正确**：都是String类型
- ✅ **语义正确**：personNumber作为用户标识，password作为密钥

## 完整的Token流程

### 1. Token生成（登录时）
```java
// UserFrontController.java
String token = JwtUtil.sign(front.getPersonNumber(), front.getPassword());
localCacheUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
localCacheUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
```

### 2. Token验证（请求时）
```java
// 在认证过滤器或拦截器中
boolean isValid = JwtUtil.verify(token, username, password);
```

### 3. 用户信息提取
```java
// 从token中提取用户名
String username = JwtUtil.getUsername(token);
```

## 测试验证

### 1. 登录测试
```bash
curl -X POST "http://localhost:10015/electricity/api/power_trade/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "personNumber": "test_user",
    "password": "test_password"
  }'
```

### 2. Token验证测试
```java
// 测试代码
String token = JwtUtil.sign("test_user", "test_password");
boolean isValid = JwtUtil.verify(token, "test_user", "test_password");
String extractedUsername = JwtUtil.getUsername(token);

System.out.println("Token: " + token);
System.out.println("Is Valid: " + isValid);
System.out.println("Extracted Username: " + extractedUsername);
```

### 3. 预期结果
- ✅ Token生成成功
- ✅ Token验证通过
- ✅ 用户名提取正确

## 安全考虑

### 1. 密码安全
- JWT使用用户密码作为签名密钥
- 确保密码的安全性和复杂性
- 考虑定期更换密码

### 2. Token安全
- Token有效期设置合理（当前为7天）
- 使用HTTPS传输Token
- 在Redis中缓存Token，支持主动失效

### 3. 算法安全
- 使用HMAC256算法，安全性较高
- 密钥长度足够，不易被破解

## 部署步骤

### 1. 重新编译
```bash
cd lyzw
mvn clean compile
```

### 2. 重启应用
重启Spring Boot应用使修改生效

### 3. 验证功能
- 测试用户登录
- 验证Token生成
- 检查Token验证

## 风险评估

### 低风险
- 只修复了字段名不一致问题
- 不改变核心逻辑
- 向后兼容

### 注意事项
1. 确保所有使用JWT的地方都使用统一的字段名
2. 测试现有用户的Token是否仍然有效
3. 监控登录成功率

## 后续优化建议

### 1. 增强安全性
- 考虑使用RSA非对称加密
- 添加Token刷新机制
- 实现Token黑名单

### 2. 性能优化
- 优化Token缓存策略
- 减少Token验证频率
- 使用更高效的算法

### 3. 监控完善
- 添加Token使用监控
- 异常Token告警
- 用户行为分析
