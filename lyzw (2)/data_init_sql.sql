CREATE
DATABASE IF NOT EXISTS `bi_anhui`;

USE
`bi_anhui`;

-- bi_anhui.Dwr_history definition

CREATE TABLE IF NOT EXISTS `Dwr_history`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`        date NOT NULL,
    `time`        varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `station_id`  bigint unsigned NOT NULL,
    `value`       varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站点卫星数据提取的辐照度\r\n';


-- bi_anhui.Forecast_similar_days_4 definition

CREATE TABLE IF NOT EXISTS `Forecast_similar_days_4`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `date`        date                                                          DEFAULT NULL COMMENT '预测日期',
    `similar_day` date                                                          DEFAULT NULL COMMENT '相似日',
    `value`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '相似度',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `station_id`  bigint unsigned DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY           `Forecast_similar_days_4_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次周新能源出力以天为单位情况预测未来4天相似日';


-- bi_anhui.Forecast_similar_times_4 definition

CREATE TABLE IF NOT EXISTS `Forecast_similar_times_4`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT,
    `date`         date NOT NULL,
    `time`         varchar(255) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `similar_date` date NOT NULL,
    `similar_time` varchar(255) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `value`        varchar(255) COLLATE utf8mb4_unicode_ci                      DEFAULT NULL,
    `type`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_time`  timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='次周新能源出力以时段预测未来4天相似度';


-- bi_anhui.Pre_filing_ratio definition

CREATE TABLE IF NOT EXISTS `Pre_filing_ratio`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `date`         date                                                          NOT NULL COMMENT '预测日期',
    `similar_date` date                                                          NOT NULL COMMENT '相似日期',
    `time`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预测日期时间段',
    `station_id`   bigint unsigned NOT NULL COMMENT '站点编号',
    `value`        decimal(10, 1)                                                NOT NULL COMMENT '比例因子',
    `type`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '策略类型',
    `create_time`  timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `Pre_filing_ratio_date_IDX` (`date`,`similar_date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='基于次周新能源出力情况调整比例因子';


-- bi_anhui.Price_variance_forecasting_strategy definition

CREATE TABLE IF NOT EXISTS `Price_variance_forecasting_strategy`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `date`        date                                                          DEFAULT NULL COMMENT '预测日期',
    `time`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预测日期时间段',
    `value`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '比例因子',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '站点编号',
    `type`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '策略类型',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `Price_variance_forecasting_strategy_date_IDX` (`date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='基于AI价差调整比例因子';


-- bi_anhui.Probability_adjustment_scaling_factor definition

CREATE TABLE IF NOT EXISTS `Probability_adjustment_scaling_factor`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `date`        date                                                          DEFAULT NULL COMMENT '预测日期',
    `time`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预测日期时间段',
    `value`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '比例因子',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '站点编号',
    `type`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '策略类型',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `Probability_adjustment_scaling_factor_date_IDX` (`date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='基于概率价差调整比例因子';


-- bi_anhui.contract_day_roll definition

CREATE TABLE IF NOT EXISTS `contract_day_roll`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `target_date`  date         DEFAULT NULL COMMENT '标的日',
    `operate_date` date         DEFAULT NULL COMMENT '操作日期',
    `create_time`  datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    varchar(100) DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(100) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日滚撮合同';


-- bi_anhui.contract_day_roll_data definition

CREATE TABLE IF NOT EXISTS `contract_day_roll_data`
(
    `contract_id`          bigint unsigned NOT NULL COMMENT '关联合同id',
    `deal_time_start_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分时段开始时间',
    `deal_time_end_time`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分时段开始时间',
    `direction`            tinyint unsigned DEFAULT NULL COMMENT '买卖方向 0买入 1卖出',
    `deal_power`           decimal(10, 4) DEFAULT NULL COMMENT '成交电量',
    `deal_avg_price`       decimal(10, 5) DEFAULT NULL COMMENT '成交均价',
    PRIMARY KEY (`contract_id`, `deal_time_start_time`, `deal_time_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日滚撮合同数据';


-- bi_anhui.contract_period definition

CREATE TABLE IF NOT EXISTS `contract_period`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `period_name` varchar(100) DEFAULT NULL COMMENT '周期名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同周期';


-- bi_anhui.contract_type definition

CREATE TABLE IF NOT EXISTS `contract_type`
(
    `id`        int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name` varchar(100) DEFAULT NULL COMMENT '类型名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同类型';


-- bi_anhui.contract_yearly definition

CREATE TABLE IF NOT EXISTS `contract_yearly`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`     bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `title`          varchar(100)                                                  DEFAULT NULL COMMENT '合同标题',
    `period_id`      int unsigned DEFAULT NULL COMMENT '合同周期id',
    `type_id`        int unsigned DEFAULT NULL COMMENT '合同类型id',
    `total_quantity` decimal(10, 2)                                                DEFAULT NULL COMMENT '合同总电量',
    `start_date`     date                                                          DEFAULT NULL COMMENT '合同开始时间',
    `end_date`       date                                                          DEFAULT NULL COMMENT '合同结束时间',
    `price`          decimal(10, 2)                                                DEFAULT NULL COMMENT '权益电价',
    `buyer`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '购电方',
    `create_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(100)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(100)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY              `contract_yearly_station_id_IDX` (`station_id`,`start_date`,`end_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度合同';


-- bi_anhui.contract_yearly_data definition

CREATE TABLE IF NOT EXISTS `contract_yearly_data`
(
    `contract_id` bigint unsigned NOT NULL COMMENT '合同id',
    `curve_id`    bigint unsigned DEFAULT NULL COMMENT '日分解曲线id',
    `month`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '月份',
    `price`       decimal(10, 2) DEFAULT NULL COMMENT '电价',
    `ratio`       decimal(10, 2) DEFAULT NULL COMMENT '比例',
    UNIQUE KEY `contract_yearly_data_unique` (`contract_id`,`month`),
    KEY           `contract_yearly_data_contract_id_IDX` (`contract_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度合同分解月度曲线';


-- bi_anhui.critical_channel_flow definition

CREATE TABLE IF NOT EXISTS `critical_channel_flow`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `time`          varchar(255)   DEFAULT NULL COMMENT '时间',
    `value`         decimal(20, 6) DEFAULT NULL COMMENT '数据值',
    `business_time` date           DEFAULT NULL COMMENT '业务时间',
    `update_time`   datetime       DEFAULT NULL COMMENT '数据更新时间',
    `ws_time`       datetime       DEFAULT NULL,
    `apply_id`      varchar(255)   DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255)   DEFAULT NULL COMMENT '数据唯一标识符',
    `meas_type`     varchar(255)   DEFAULT NULL COMMENT '数据类型',
    `dev_name`      varchar(255)   DEFAULT NULL COMMENT '重要通道名称',
    `dev_id`        varchar(255)   DEFAULT NULL COMMENT '重要通道 ID',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `critical_channel_flow_unique` (`business_time`,`dev_id`,`time`),
    KEY             `critical_channel_flow_business_time_IDX` (`business_time`,`dev_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='重要通道输电情况';


-- bi_anhui.daily_ahead_foreign_electricity definition

CREATE TABLE IF NOT EXISTS `daily_ahead_foreign_electricity`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `tl_id`         varchar(255)                                                  DEFAULT NULL COMMENT '数据id',
    `tl_name`       varchar(255)                                                  DEFAULT NULL COMMENT '数据类型',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_ahead_foreign_electricity_unique` (`time`,`business_time`),
    KEY             `daily_ahead_foreign_electricity_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前各交易时段外来(外送)电交易计划(公开)';


-- bi_anhui.daily_clearing_electricity definition

CREATE TABLE IF NOT EXISTS `daily_clearing_electricity`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`           date           DEFAULT NULL COMMENT '数据日期',
    `time`           varchar(256)   DEFAULT NULL COMMENT '数据时间，格式为HH:MM:SS',
    `province_id`    bigint unsigned DEFAULT NULL COMMENT '省份id',
    `rq_electricity` decimal(10, 2) DEFAULT NULL COMMENT '日前电量',
    `ss_electricity` decimal(10, 2) DEFAULT NULL COMMENT '实时电量',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY              `daily_clearing_electricity_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前、实时各交易时段出清总电量';


-- bi_anhui.daily_clearing_price_forecast definition

CREATE TABLE IF NOT EXISTS `daily_clearing_price_forecast`
(
    `id`          int            NOT NULL AUTO_INCREMENT,
    `date`        date           NOT NULL,
    `time`        varchar(5)     NOT NULL,
    `value`       decimal(10, 2) NOT NULL COMMENT '预测的全网统一结算点日前电价',
    `create_time` datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_date_time` (`date`,`time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='全网统一结算点日前电价预测结果';


-- bi_anhui.daily_clearing_prices definition

CREATE TABLE IF NOT EXISTS `daily_clearing_prices`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date           DEFAULT NULL COMMENT '数据日期',
    `data_time`     varchar(256)   DEFAULT NULL COMMENT '数据时间，格式为HH:MM:SS',
    `ss_price`      decimal(10, 2) DEFAULT NULL COMMENT '实时电价',
    `rq_price`      decimal(10, 2) DEFAULT NULL COMMENT '日前电价',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_clearing_prices_unique` (`business_time`,`data_time`),
    KEY             `daily_clearing_prices_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前、实时各交易时段出清均价';


-- bi_anhui.daily_freq_reg_service_volume_forecast definition

CREATE TABLE IF NOT EXISTS `daily_freq_reg_service_volume_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `daily_freq_reg_service_volume_forecast_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='各交易时段调频辅助服务出清总量预测';


-- bi_anhui.daily_gen_output_forecast definition

CREATE TABLE IF NOT EXISTS `daily_gen_output_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `daily_gen_output_forecast_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前各交易时段发电总出力预测';


-- bi_anhui.daily_hydro_forecast definition

CREATE TABLE IF NOT EXISTS `daily_hydro_forecast`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_hydro_forecast_unique` (`time`,`business_time`),
    KEY             `daily_hydro_forecast_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前水电含抽蓄发电出力预测';


-- bi_anhui.daily_load_forecast_public definition

CREATE TABLE IF NOT EXISTS `daily_load_forecast_public`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `value_type`    varchar(255)                                                  DEFAULT NULL COMMENT '值类型，例如负荷',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_load_forecast_public_unique` (`time`,`business_time`,`value_type`),
    KEY             `daily_load_forecast_public_business_time_IDX` (`business_time`,`value_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前各交易时段负荷预测(公开)';


-- bi_anhui.daily_new_energy_output_forecast definition

CREATE TABLE IF NOT EXISTS `daily_new_energy_output_forecast`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `value_type`    varchar(255)                                                  DEFAULT NULL COMMENT '值类型，例如光电总出力预测数值、新能源总出力预测数值、风电总出力预测数值',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `daily_new_energy_output_forecast_unique` (`business_time`,`time`,`value_type`),
    KEY             `daily_new_energy_output_forecast_business_time_IDX` (`business_time`,`value_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前新能源总出力及分类出力预测(公开)';


-- bi_anhui.daily_non_market_unit_forecast definition

CREATE TABLE IF NOT EXISTS `daily_non_market_unit_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `daily_non_market_unit_forecast_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前非市场机组出力预测';


-- bi_anhui.daily_renewable_energy_output definition

CREATE TABLE IF NOT EXISTS `daily_renewable_energy_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '数据类型 1 新能源总出力 2 风电出力 3光伏出力',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `daily_renewable_energy_output_date_IDX` (`date`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='各交易时段新能源总出力预测';


-- bi_anhui.daily_supply_demand_balance_forecast definition

CREATE TABLE IF NOT EXISTS `daily_supply_demand_balance_forecast`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `daily_supply_demand_balance_forecast_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前电力电量供需平衡预测';


-- bi_anhui.data_type definition

CREATE TABLE IF NOT EXISTS `data_type`
(
    `id`        int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name` varchar(256) DEFAULT NULL COMMENT '类型名称',
    `type_code` varchar(100) DEFAULT NULL COMMENT '类型编码',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收益分析模块的数据类型';


-- bi_anhui.day_ahead_congestion_price_forecast definition

CREATE TABLE IF NOT EXISTS `day_ahead_congestion_price_forecast`
(
    `id`           int            NOT NULL AUTO_INCREMENT,
    `date`         date           NOT NULL,
    `time`         varchar(5)     NOT NULL,
    `value`        decimal(10, 2) NOT NULL COMMENT '预测的阻塞价格',
    `station_id`   int            NOT NULL,
    `station_name` varchar(50)    NOT NULL,
    `province_id`  int            NOT NULL,
    `create_time`  datetime       NOT NULL,
    `update_time`  datetime       NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_date_time_station` (`date`,`time`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前阻塞价格预测表';


-- bi_anhui.day_ahead_node_clear_electricity definition

CREATE TABLE IF NOT EXISTS `day_ahead_node_clear_electricity`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `day_ahead_node_clear_electricity_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前节点出清电量';


-- bi_anhui.day_ahead_node_clear_price definition

CREATE TABLE IF NOT EXISTS `day_ahead_node_clear_price`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `day_ahead_node_clear_price_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前节点出清电价';


-- bi_anhui.day_ahead_node_price_forecast_v1 definition

CREATE TABLE IF NOT EXISTS `day_ahead_node_price_forecast_v1`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`         date        NOT NULL COMMENT '预测日期',
    `time`         varchar(8)  NOT NULL COMMENT '时间点，格式HH:MM',
    `value`        decimal(10, 2) DEFAULT NULL COMMENT '预测价格值',
    `station_id`   int            DEFAULT NULL COMMENT '场站ID',
    `station_name` varchar(64) NOT NULL COMMENT '场站名称',
    `province_id`  int            DEFAULT NULL COMMENT '省份ID',
    `create_time`  datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_forecast` (`date`,`time`,`station_name`) COMMENT '唯一索引确保同一时间同一场站只有一条记录',
    KEY            `day_ahead_node_price_forecast_v1_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前节点价格预测表，基于负荷信息、新能源出力、来外电、竞价空间做出来的节点价格预测';


-- bi_anhui.day_ahead_node_price_forecast_v2 definition

CREATE TABLE IF NOT EXISTS `day_ahead_node_price_forecast_v2`
(
    `id`           bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`         date        NOT NULL COMMENT '预测日期',
    `time`         varchar(8)  NOT NULL COMMENT '时间点，格式HH:MM',
    `value`        decimal(10, 2) DEFAULT NULL COMMENT '预测价格值',
    `station_id`   int            DEFAULT NULL COMMENT '场站ID',
    `station_name` varchar(64) NOT NULL COMMENT '场站名称',
    `province_id`  int            DEFAULT NULL COMMENT '省份ID',
    `create_time`  datetime       DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_forecast` (`date`,`time`,`station_name`) COMMENT '唯一索引确保同一时间同一场站只有一条记录',
    KEY            `day_ahead_node_price_forecast_v2_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前节点价格预测表，单纯基于新能源出力预测的日前节点价格';

-- bi_anhui.day_ahead_node_price_forecast_v3 definition

CREATE TABLE  IF NOT EXISTS `day_ahead_node_price_forecast_v3` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date` date DEFAULT NULL COMMENT '预测日期',
    `value` decimal(10,2) DEFAULT NULL COMMENT '预测价格值',
    `time` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间点，格式HH:MM',
    `station_id` int DEFAULT NULL COMMENT '场站ID',
    `station_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '场站名称',
    `province_id` int DEFAULT NULL COMMENT '省份ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用于电价预测（）';


-- bi_anhui.day_power_curve definition

CREATE TABLE IF NOT EXISTS `day_power_curve`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `curve_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '曲线名称',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日发电曲线';


-- bi_anhui.day_power_curve_data definition

CREATE TABLE IF NOT EXISTS `day_power_curve_data`
(
    `curve_id` bigint unsigned NOT NULL COMMENT '关联日发电曲线表id',
    `time`     varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '时间',
    `price`    decimal(20, 6) DEFAULT NULL COMMENT '电价',
    `ratio`    decimal(10, 2) DEFAULT NULL COMMENT '比例',
    UNIQUE KEY `day_power_curve_data_unique` (`curve_id`,`time`),
    KEY        `day_power_curve_data_curve_id_IDX` (`curve_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日发电曲线数据表';


-- bi_anhui.energy_mid_to_long_term definition

CREATE TABLE IF NOT EXISTS `energy_mid_to_long_term`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`                    date NOT NULL COMMENT '日期',
    `station_id`              bigint unsigned NOT NULL COMMENT '电站id',
    `mid_to_long_term_power`  decimal(10, 5) DEFAULT NULL COMMENT '中长期合约电量',
    `real_time_deviation_fee` decimal(10, 5) DEFAULT NULL COMMENT '实际上网电量',
    `manufacturer_power`      decimal(10, 5) DEFAULT NULL COMMENT '功率预测发电量',
    `create_time`             datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`               varchar(100)   DEFAULT NULL COMMENT '创建人',
    `update_by`               varchar(100)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `energy_mid_to_long_term_unique` (`date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='中长期策略复盘';


-- bi_anhui.energy_new_daily_clean definition

CREATE TABLE IF NOT EXISTS `energy_new_daily_clean`
(
    `id`                        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`                      date NOT NULL COMMENT '日期',
    `station_id`                bigint unsigned NOT NULL COMMENT '电站id',
    `mid_long_term_power`       decimal(20, 5) DEFAULT NULL COMMENT '中长期合约电量',
    `mid_long_term_price`       decimal(20, 5) DEFAULT NULL COMMENT '中长期单价',
    `mid_long_term_fee`         decimal(20, 5) DEFAULT NULL COMMENT '中长期电费',
    `guarantee_power`           decimal(20, 5) DEFAULT NULL COMMENT '保障电量',
    `guarantee_price`           decimal(20, 5) DEFAULT NULL COMMENT '保障电价',
    `guarantee_fee`             decimal(20, 5) DEFAULT NULL COMMENT '保障电费',
    `day_ahead_deviation_power` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `day_ahead_deviation_price` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差单价',
    `day_ahead_deviation_fee`   decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电费',
    `realtime_deviation_power`  decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `realtime_deviation_price`  decimal(20, 5) DEFAULT NULL COMMENT '实时偏差单价',
    `realtime_deviation_fee`    decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电费',
    `excess_profit_recovery`    decimal(20, 5) DEFAULT NULL COMMENT '超额收益回收',
    `day_ahead_profit_recovery` decimal(20, 5) DEFAULT NULL COMMENT '日前偏差收益回收',
    `total_power`               decimal(20, 5) DEFAULT NULL COMMENT '上网电量',
    `total_fee`                 decimal(20, 5) DEFAULT NULL COMMENT '总电费',
    `settlement_avg_price`      decimal(20, 5) DEFAULT NULL COMMENT '日结算均价',
    `create_time`               datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`                 varchar(100)   DEFAULT NULL COMMENT '创建人',
    `update_by`                 varchar(100)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `energy_new_daily_clean_unique` (`date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='新能源日清分';


-- bi_anhui.energy_storage_daily_clean definition

CREATE TABLE IF NOT EXISTS `energy_storage_daily_clean`
(
    `id`                                                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date`                                               date NOT NULL COMMENT '日期',
    `station_id`                                         bigint unsigned DEFAULT NULL COMMENT '电站id',
    `user_day_ahead_deviation_power`                     decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `user_day_ahead_deviation_average_price`             decimal(20, 5) DEFAULT NULL COMMENT '日前平均电价',
    `user_day_ahead_deviation_fee`                       decimal(20, 5) DEFAULT NULL COMMENT '日前电费',
    `user_realtime_deviation_power`                      decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `user_realtime_deviation_average_price`              decimal(20, 5) DEFAULT NULL COMMENT '实时平均单价',
    `user_realtime_deviation_fee`                        decimal(20, 5) DEFAULT NULL COMMENT '实时电费',
    `user_total_power`                                   decimal(20, 5) DEFAULT NULL COMMENT '用电总电量',
    `user_total_fee`                                     decimal(20, 5) DEFAULT NULL COMMENT '用电总费用',
    `power_generation_day_ahead_deviation_power`         decimal(20, 5) DEFAULT NULL COMMENT '日前偏差电量',
    `power_generation_day_ahead_deviation_average_price` decimal(20, 5) DEFAULT NULL COMMENT '日前平均电价',
    `power_generation_day_ahead_deviation_fee`           decimal(20, 5) DEFAULT NULL COMMENT '日前电费',
    `power_generation_realtime_deviation_power`          decimal(20, 5) DEFAULT NULL COMMENT '实时偏差电量',
    `power_generation_realtime_deviation_average_price`  decimal(20, 5) DEFAULT NULL COMMENT '实时平均单价',
    `power_generation_realtime_deviation_fee`            decimal(20, 5) DEFAULT NULL COMMENT '实时电费',
    `power_generation_total_power`                       decimal(20, 5) DEFAULT NULL COMMENT '用电总电量',
    `power_generation_total_fee`                         decimal(20, 5) DEFAULT NULL COMMENT '用电总费用',
    `create_time`                                        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                                        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`                                          varchar(100)   DEFAULT NULL COMMENT '创建人',
    `update_by`                                          varchar(100)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `energy_storage_daily_clean_unique` (`date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能日清分';


-- bi_anhui.file_station_relation definition

CREATE TABLE IF NOT EXISTS `file_station_relation`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_url`    varchar(1000) DEFAULT NULL COMMENT '文件在minio地址',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `type`        tinyint unsigned DEFAULT NULL COMMENT '文件类型',
    `settle_date` date          DEFAULT NULL COMMENT '文件结算日期',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `file_station_relation_unique` (`station_id`,`type`,`settle_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件关联电站表';


-- bi_anhui.gen_contract_curve_decompose definition

CREATE TABLE IF NOT EXISTS `gen_contract_curve_decompose`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `type`         tinyint unsigned DEFAULT NULL COMMENT '数据类型 1 电价 2 电量',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `gen_contract_curve_decompose_date_IDX` (`date`,`type`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日前中长期交易合同分解电量曲线(发电侧)';


-- bi_anhui.grid_real_time_backup_info definition

CREATE TABLE IF NOT EXISTS `grid_real_time_backup_info`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '对应的返回值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `members_id`    varchar(255)                                                  DEFAULT NULL COMMENT '成员ID',
    `num_type`      varchar(255)                                                  DEFAULT NULL COMMENT '数值类型，例如负荷、正备用等',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `grid_real_time_backup_info_business_time_IDX` (`business_time`,`num_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电网实际负荷实时系统备用信息(公开)';


-- bi_anhui.hydro_output_period definition

CREATE TABLE IF NOT EXISTS `hydro_output_period`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `hydro_output_period_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='各交易时段水电总出力';


-- bi_anhui.key_line_limit definition

CREATE TABLE IF NOT EXISTS `key_line_limit`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `sec_id`        varchar(255)   DEFAULT NULL COMMENT '输电通道 ID',
    `sec_name`      varchar(255)   DEFAULT NULL COMMENT '输电通道名称',
    `business_time` date           DEFAULT NULL COMMENT '业务时间',
    `limit_value`   decimal(20, 6) DEFAULT NULL COMMENT '限值',
    `ws_time`       datetime       DEFAULT NULL,
    `apply_id`      varchar(255)   DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255)   DEFAULT NULL COMMENT '数据唯一标识符',
    `meas_type`     varchar(255)   DEFAULT NULL COMMENT '数据类型',
    `represent`     varchar(255)   DEFAULT NULL COMMENT '描述',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `key_line_limit_business_time_IDX` (`business_time`,`sec_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='关键输电线路限额';


-- bi_anhui.line_transformer_flow definition

CREATE TABLE IF NOT EXISTS `line_transformer_flow`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `time`          varchar(255)   DEFAULT NULL COMMENT '时间',
    `value`         decimal(20, 6) DEFAULT NULL COMMENT '数据值',
    `business_time` date           DEFAULT NULL COMMENT '业务时间',
    `update_time`   datetime       DEFAULT NULL COMMENT '数据更新时间',
    `ws_time`       datetime       DEFAULT NULL,
    `apply_id`      varchar(255)   DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255)   DEFAULT NULL COMMENT '数据唯一标识符',
    `name`          varchar(255)   DEFAULT NULL COMMENT '重要线路名称',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `line_transformer_flow_business_time_IDX` (`business_time`,`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='线路与变压器潮流';


-- bi_anhui.market_notice definition

CREATE TABLE IF NOT EXISTS `market_notice`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `notice_title`   varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '公告标题',
    `type_id`        int unsigned DEFAULT NULL COMMENT '公告类型id（关联公告类型表主键）',
    `notice_date`    date                                                          DEFAULT NULL COMMENT '披露日期',
    `notice_label`   text COMMENT '公告标签',
    `notice_content` text COMMENT '公共内容',
    `create_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(256)                                                  DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(256)                                                  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='市场公告';


-- bi_anhui.monthly_gen_commission definition

CREATE TABLE IF NOT EXISTS `monthly_gen_commission`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '数据更新时间',
    `volume`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规划容量',
    `bw_time`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '并网时间',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `guid`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据唯一标识符',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '电厂类型',
    `name`          varchar(255)                                                  DEFAULT NULL COMMENT '电厂名称',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `monthly_gen_commission_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度发电设备投产情况';


-- bi_anhui.monthly_grid_commission definition

CREATE TABLE IF NOT EXISTS `monthly_grid_commission`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date         DEFAULT NULL COMMENT '业务时间',
    `update_time`   datetime     DEFAULT NULL COMMENT '数据更新时间',
    `dw_level`      varchar(255) DEFAULT NULL COMMENT '电压等级',
    `dw_time`       varchar(255) DEFAULT NULL COMMENT '投产时间',
    `ws_time`       datetime     DEFAULT NULL,
    `apply_id`      varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255) DEFAULT NULL COMMENT '数据唯一标识符',
    `name`          varchar(255) DEFAULT NULL COMMENT '变电站名称',
    `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `monthly_grid_commission_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月度电网设备投产情况';


-- bi_anhui.must_run_units definition

CREATE TABLE IF NOT EXISTS `must_run_units`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date         DEFAULT NULL COMMENT '业务时间',
    `ws_time`       datetime     DEFAULT NULL,
    `name`          varchar(255) DEFAULT NULL COMMENT '机组名称',
    `apply_id`      varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255) DEFAULT NULL COMMENT '数据唯一标识符',
    `phy_id`        varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `meas_type`     varchar(255) DEFAULT NULL COMMENT '数据类型',
    `reason`        varchar(255) DEFAULT NULL COMMENT '容量',
    `state`         varchar(255) DEFAULT NULL COMMENT '状态',
    `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='必开必停机组组合名单及原因';


-- bi_anhui.next_gen_commission definition

CREATE TABLE IF NOT EXISTS `next_gen_commission`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `installed`       decimal(20, 6) DEFAULT NULL COMMENT '装机容量（MW）',
    `business_time`   date           DEFAULT NULL COMMENT '业务时间',
    `update_time`     datetime       DEFAULT NULL COMMENT '数据更新时间',
    `production_time` varchar(255)   DEFAULT NULL COMMENT '预计投产时间',
    `dispatch_name`   varchar(255)   DEFAULT NULL COMMENT '厂（场）站调度名称',
    `ws_time`         datetime       DEFAULT NULL,
    `apply_id`        varchar(255)   DEFAULT NULL COMMENT '申请批次ID',
    `data_id`         varchar(255)   DEFAULT NULL COMMENT '数据唯一标识符',
    `type`            varchar(255)   DEFAULT NULL COMMENT '时间范围 M月 Y年',
    `category`        varchar(255)   DEFAULT NULL COMMENT '发电类型',
    `create_time`     datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`     datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `next_gen_commission_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次月发电设备投产计划';


-- bi_anhui.next_gen_maintenance definition

CREATE TABLE IF NOT EXISTS `next_gen_maintenance`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time`   date         DEFAULT NULL COMMENT '业务时间',
    `update_time`     datetime     DEFAULT NULL COMMENT '数据更新时间',
    `connection_time` varchar(255) DEFAULT NULL COMMENT '计划并网时间',
    `train_time`      varchar(255) DEFAULT NULL COMMENT '计划解列时间',
    `apply_id`        varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `data_id`         varchar(255) DEFAULT NULL COMMENT '数据唯一标识符',
    `name`            varchar(255) DEFAULT NULL COMMENT '设备名称',
    `create_time`     datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`     datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `next_gen_maintenance_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次月发电设备检修计划';


-- bi_anhui.next_grid_maintenance definition

CREATE TABLE IF NOT EXISTS `next_grid_maintenance`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time`   date         DEFAULT NULL COMMENT '业务时间',
    `update_time`     datetime     DEFAULT NULL COMMENT '数据更新时间',
    `declaring_units` varchar(255) DEFAULT NULL COMMENT '申报单位',
    `start_time`      varchar(255) DEFAULT NULL COMMENT '计划开工时间',
    `complete_time`   varchar(255) DEFAULT NULL COMMENT '计划完工时间',
    `apply_id`        varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `data_id`         varchar(255) DEFAULT NULL COMMENT '数据唯一标识符',
    `name`            varchar(255) DEFAULT NULL COMMENT '设备名称',
    `create_time`     datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`     datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `next_grid_maintenance_business_time_IDX` (`business_time`,`declaring_units`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次月电网设备检修计划';


-- bi_anhui.node_price_diff_forecast definition

CREATE TABLE IF NOT EXISTS `node_price_diff_forecast`
(
    `id`           bigint                                 NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `date`         date                                   NOT NULL COMMENT '预测日期',
    `time`         varchar(5) COLLATE utf8mb4_general_ci  NOT NULL COMMENT '预测时间点',
    `value`        decimal(10, 3)                         NOT NULL COMMENT '预测价差值',
    `station_id`   int                                    NOT NULL COMMENT '电站ID',
    `station_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '电站名称',
    `province_id`  int                                    NOT NULL COMMENT '省份ID',
    `create_time`  datetime                               NOT NULL COMMENT '创建时间',
    `update_time`  datetime                               NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `node_price_diff_forecast_unique` (`date`,`time`,`station_name`),
    KEY            `idx_date_station` (`date`,`station_id`),
    KEY            `idx_station_name` (`station_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='节点价差预测表';


-- bi_anhui.non_market_unit_output_curve definition

CREATE TABLE IF NOT EXISTS `non_market_unit_output_curve`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `value`         decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`          varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `data_id`       varchar(255)                                                  DEFAULT NULL COMMENT '数据唯一标识符',
    `update_time`   datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `business_time` date                                                          DEFAULT NULL COMMENT '业务时间',
    `meas_type`     varchar(255)                                                  DEFAULT NULL COMMENT '测量类型',
    `apply_id`      varchar(255)                                                  DEFAULT NULL COMMENT '申请批次ID',
    `value_type`    varchar(255)                                                  DEFAULT NULL COMMENT '值类型，例如非市场化机组总出力',
    `ws_time`       datetime                                                      DEFAULT NULL COMMENT '工作状态时间',
    `create_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `non_market_unit_output_curve_unique` (`business_time`,`time`),
    KEY             `non_market_unit_output_curve_business_time_IDX` (`business_time`,`value_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='非市场化机组实际出力曲线(公开)';


-- bi_anhui.notice_file definition

CREATE TABLE IF NOT EXISTS `notice_file`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_name`   varchar(256)  DEFAULT NULL COMMENT '文件名称',
    `file_url`    varchar(1000) DEFAULT NULL COMMENT '文件url',
    `notice_id`   bigint unsigned DEFAULT NULL COMMENT '文件关联公告id',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256)  DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256)  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公告附件';


-- bi_anhui.notice_type definition

CREATE TABLE IF NOT EXISTS `notice_type`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_name`   varchar(256) DEFAULT NULL COMMENT '类型名称',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公告类型';


-- bi_anhui.person_profit definition

CREATE TABLE IF NOT EXISTS `person_profit`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `date`        date                                                          DEFAULT NULL COMMENT '时间日期',
    `value`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人工收益值',
    `station_id`  bigint unsigned DEFAULT NULL COMMENT '场站编号',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='人工收益计算=功率*（调整因子-1）*（日前-实时）';


-- bi_anhui.power_on_grid definition

CREATE TABLE IF NOT EXISTS `power_on_grid`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `power_on_grid_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机组上网电量';


-- bi_anhui.power_side_settle definition

CREATE TABLE IF NOT EXISTS `power_side_settle`
(
    `id`                          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_id`                     bigint unsigned DEFAULT NULL COMMENT '关联文件id',
    `station_type`                tinyint unsigned DEFAULT NULL COMMENT '结算单元类型 1 风电 2 光伏 3储能',
    `data_type`                   tinyint unsigned DEFAULT NULL COMMENT '数据类型，只用于储能结算单 0购电侧 1售电侧',
    `actual_internet_electricity` decimal(20, 6) DEFAULT NULL COMMENT '实际上网电量',
    `settlement_electricity`      decimal(20, 6) DEFAULT NULL COMMENT '结算电量',
    `contract_electricity`        decimal(20, 6) DEFAULT NULL COMMENT '合同电量',
    `deviation_electricity`       decimal(20, 6) DEFAULT NULL COMMENT '偏差电量',
    `settlement_electric_fee`     decimal(20, 6) DEFAULT NULL COMMENT '结算电费',
    PRIMARY KEY (`id`),
    KEY                           `power_side_settle_file_id_IDX` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统推发电侧结算单';


-- bi_anhui.province_daily_clearing_detail definition

CREATE TABLE IF NOT EXISTS `province_daily_clearing_detail`
(
    `id`                                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`                          bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `settle_date`                         date           DEFAULT NULL COMMENT '结算时间',
    `time`                                varchar(256)   DEFAULT NULL COMMENT '时间',
    `guarantee_power`                     decimal(20, 6) DEFAULT NULL COMMENT '保障电量',
    `guarantee_price`                     decimal(20, 6) DEFAULT NULL COMMENT '保障电价',
    `medium_and_long_term_contract_power` decimal(20, 6) DEFAULT NULL COMMENT '中长期合约电量',
    `medium_and_long_term_contract_price` decimal(20, 6) DEFAULT NULL COMMENT '中长期合约电价',
    `day_ahead_deviation_power`           decimal(20, 6) DEFAULT NULL COMMENT '日前偏差电量',
    `day_ahead_deviation_price`           decimal(20, 6) DEFAULT NULL COMMENT '日前偏差电价',
    `real_time_deviation_power`           decimal(20, 6) DEFAULT NULL COMMENT '实时偏差电量',
    `real_time_deviation_price`           decimal(20, 6) DEFAULT NULL COMMENT '实时偏差电价',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='省内日清分明细结算';


-- bi_anhui.province_daily_clearing_sum definition

CREATE TABLE IF NOT EXISTS `province_daily_clearing_sum`
(
    `id`                       bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_id`                  int unsigned DEFAULT NULL COMMENT '数据类型',
    `station_id`               bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `settle_date`              date           DEFAULT NULL COMMENT '结算时间',
    `current_electricity`      decimal(20, 6) DEFAULT NULL COMMENT '本期电量',
    `avg_unit_price`           decimal(20, 6) DEFAULT NULL COMMENT '平均电价',
    `current_electricity_free` decimal(20, 6) DEFAULT NULL COMMENT '本期电费',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='省内日清分结算单汇总';


-- bi_anhui.real_time_node_clear_electricity definition

CREATE TABLE IF NOT EXISTS `real_time_node_clear_electricity`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `real_time_node_clear_electricity_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时节点出清电量';


-- bi_anhui.real_time_node_clear_price definition

CREATE TABLE IF NOT EXISTS `real_time_node_clear_price`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date                                                          DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`         varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `station_name` varchar(256)                                                  DEFAULT NULL COMMENT '电站名称',
    `province_id`  bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `real_time_node_clear_price_date_IDX` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实时节点出清电价';


-- bi_anhui.rollover_profit definition

CREATE TABLE IF NOT EXISTS `rollover_profit`
(
    `id`                   int NOT NULL AUTO_INCREMENT,
    `station_id`           int            DEFAULT NULL COMMENT '场站ID',
    `station_name`         varchar(50)    DEFAULT NULL COMMENT '场站名称',
    `target_date`          date           DEFAULT NULL COMMENT '标的日',
    `time_period`          varchar(20)    DEFAULT NULL COMMENT '时间段',
    `direction`            varchar(10)    DEFAULT NULL COMMENT '买卖方向',
    `deal_power`           decimal(10, 2) DEFAULT NULL COMMENT '成交电量',
    `deal_avg_price`       decimal(10, 2) DEFAULT NULL COMMENT '成交均价',
    `benchmark_price`      decimal(10, 2) DEFAULT NULL COMMENT '燃煤标杆电价',
    `original_position`    decimal(10, 2) DEFAULT NULL COMMENT '原中长期持仓量',
    `actual_online_power`  decimal(10, 2) DEFAULT NULL COMMENT '实际上网电量',
    `day_ahead_node_price` decimal(10, 2) DEFAULT NULL COMMENT '日前节点价格',
    `settlement_price`     decimal(10, 2) DEFAULT NULL COMMENT '统一结算点日前价格',
    `rollover_profit`      decimal(10, 2) DEFAULT NULL COMMENT '滚撮收益',
    `created_at`           timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`           timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='滚撮收益表';


-- bi_anhui.screen_market_info definition

CREATE TABLE IF NOT EXISTS `screen_market_info`
(
    `type`  varchar(100)   DEFAULT NULL COMMENT '数据类型',
    `value` decimal(10, 2) DEFAULT NULL COMMENT '值',
    UNIQUE KEY `screen_market_info_unique` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='大屏展示市场信息';


-- bi_anhui.screen_market_trade definition

CREATE TABLE IF NOT EXISTS `screen_market_trade`
(
    `type`            varchar(100) NOT NULL COMMENT '数据类型',
    `year`            varchar(100) NOT NULL COMMENT '年份',
    `month`           varchar(100) NOT NULL COMMENT '月份',
    `power`           decimal(10, 2) DEFAULT NULL COMMENT '成交电量',
    `price`           decimal(10, 2) DEFAULT NULL COMMENT '成交均价',
    `discharge_price` decimal(10, 2) DEFAULT NULL COMMENT '放电均价',
    UNIQUE KEY `screen_market_trade_unique` (`type`,`year`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='市场交易情况';


-- bi_anhui.screen_trade_settlement definition

CREATE TABLE IF NOT EXISTS `screen_trade_settlement`
(
    `id`                           int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `station_id`                   bigint unsigned DEFAULT NULL COMMENT '电站id',
    `year`                         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '年份',
    `month`                        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '月份',
    `settle_power`                 decimal(15, 2)                                                DEFAULT NULL COMMENT '结算电量',
    `settle_electricity_fee`       decimal(15, 2)                                                DEFAULT NULL COMMENT '结算电费',
    `assessment_fee`               decimal(15, 2)                                                DEFAULT NULL COMMENT '双细则考核电费',
    `settlement_average_price`     decimal(10, 4)                                                DEFAULT NULL COMMENT '结算均价',
    `bench_mark_electricity_price` decimal(10, 4)                                                DEFAULT NULL COMMENT '标杆电价',
    `limited_power`                decimal(15, 2)                                                DEFAULT NULL COMMENT '限电量',
    `current_month_power`          decimal(15, 2)                                                DEFAULT NULL COMMENT '当月发电量',
    `current_month_plan_power`     decimal(15, 2)                                                DEFAULT NULL COMMENT '当月计划发电量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `screen_trade_settlement_unique` (`station_id`,`year`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易结算信息';


-- bi_anhui.section_info definition

CREATE TABLE IF NOT EXISTS `section_info`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`         date           DEFAULT NULL COMMENT '日期',
    `value`        decimal(20, 6) DEFAULT NULL COMMENT '预测值',
    `section_name` varchar(256)   DEFAULT NULL COMMENT '断面名称',
    `create_time`  datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='断面信息';


-- bi_anhui.spot_trade_summary definition

CREATE TABLE IF NOT EXISTS `spot_trade_summary`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date          DEFAULT NULL COMMENT '日期',
    `file_name`   varchar(1000) DEFAULT NULL COMMENT '文件名称',
    `file_url`    varchar(1000) DEFAULT NULL COMMENT '文件url',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `spot_trade_summary_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='现货成交总体情况';


-- bi_anhui.station_contract_assessment definition

CREATE TABLE IF NOT EXISTS `station_contract_assessment`
(
    `id`                                   int            NOT NULL AUTO_INCREMENT,
    `year`                                 int            NOT NULL,
    `month`                                int            NOT NULL,
    `station_id`                           varchar(50)    NOT NULL,
    `station_name`                         varchar(100)   NOT NULL,
    `medium_long_term_contract_percentage` decimal(10, 2) NOT NULL,
    `assessment_fee`                       decimal(15, 2) NOT NULL,
    `created_at`                           timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                           timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_month_station` (`year`,`month`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- bi_anhui.station_daily_generation definition

CREATE TABLE IF NOT EXISTS `station_daily_generation`
(
    `id`               int            NOT NULL AUTO_INCREMENT,
    `station_id`       int            NOT NULL,
    `station_name`     varchar(50)    NOT NULL,
    `data_date`        date           NOT NULL,
    `time_period`      varchar(20)    NOT NULL,
    `generation_value` decimal(10, 3) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_generation_record` (`station_id`,`data_date`,`time_period`),
    KEY                `idx_station_date` (`station_id`,`data_date`),
    KEY                `idx_date` (`data_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- bi_anhui.station_hourly_generation definition

CREATE TABLE IF NOT EXISTS `station_hourly_generation`
(
    `id`               int            NOT NULL AUTO_INCREMENT,
    `station_id`       int            NOT NULL COMMENT '场站ID',
    `station_name`     varchar(50)    NOT NULL COMMENT '场站名称',
    `data_date`        date           NOT NULL COMMENT '数据日期',
    `time_period`      varchar(20)    NOT NULL COMMENT '时间段',
    `generation_value` decimal(10, 3) NOT NULL COMMENT '发电量',
    `create_time`      datetime       NOT NULL COMMENT '创建时间',
    `update_time`      datetime       NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                `idx_station_date` (`station_id`,`data_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='场站小时发电量数据';


-- bi_anhui.strategy_data definition

CREATE TABLE IF NOT EXISTS `strategy_data`
(
    `strategy_id` bigint unsigned NOT NULL COMMENT '关联策略基本信息表id',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '时间',
    `power`       decimal(20, 6) DEFAULT NULL COMMENT '功率预测值',
    `factor`      decimal(10, 2) DEFAULT NULL COMMENT '因数',
    UNIQUE KEY `strategy_data_unique` (`strategy_id`,`time`),
    KEY           `strategy_data_strategy_id_IDX` (`strategy_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略数据表';


-- bi_anhui.strategy_declaration_record definition

CREATE TABLE IF NOT EXISTS `strategy_declaration_record`
(
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '电站id',
    `declaration_date` date                                                         DEFAULT NULL COMMENT '申报日期',
    `strategy_id`      bigint unsigned DEFAULT NULL COMMENT '申报的策略id',
    `factory_id`       int unsigned DEFAULT NULL COMMENT '申报功率厂家id',
    `version`          varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申报的功率预测的版本',
    `create_time`      datetime                                                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime                                                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256)                                                 DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256)                                                 DEFAULT NULL COMMENT '更新人',
    KEY                `strategy_declaration_record_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略申报记录表';


-- bi_anhui.strategy_info definition

CREATE TABLE IF NOT EXISTS `strategy_info`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `strategy_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '策略名称',
    `type`             tinyint unsigned DEFAULT NULL COMMENT '策略类型',
    `declaration_date` date                                                          DEFAULT NULL COMMENT '策略关联的申报日期',
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `create_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `strategy_info_unique` (`declaration_date`,`station_id`,`type`),
    KEY                `strategy_info_declaration_date_IDX` (`declaration_date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略基本信息表';


-- bi_anhui.strategy_quotation_price_curve definition

CREATE TABLE IF NOT EXISTS `strategy_quotation_price_curve`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`       bigint unsigned NOT NULL COMMENT '电站id',
    `declaration_date` date NOT NULL COMMENT '申报日期',
    `start_power`      decimal(10, 2) DEFAULT NULL COMMENT '开始出力',
    `end_power`        decimal(10, 2) DEFAULT NULL COMMENT '结束出力',
    `price`            decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `price_cap`        decimal(10, 2) DEFAULT NULL COMMENT '价格上限',
    `price_floor`      decimal(10, 2) DEFAULT NULL COMMENT '价格下限',
    `create_time`      datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256)   DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY                `strategy_quotation_price_curve_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略报价曲线';


-- bi_anhui.strategy_storage_declaration_record definition

CREATE TABLE IF NOT EXISTS `strategy_storage_declaration_record`
(
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '电站id',
    `declaration_date` date         DEFAULT NULL COMMENT '申报日期',
    `strategy_id`      bigint unsigned DEFAULT NULL COMMENT '申报的策略id',
    `create_time`      datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256) DEFAULT NULL COMMENT '更新人',
    KEY                `strategy_declaration_record_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能策略申报记录表';


-- bi_anhui.strategy_storage_info definition

CREATE TABLE IF NOT EXISTS `strategy_storage_info`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `strategy_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '策略名称',
    `type`             tinyint unsigned DEFAULT NULL COMMENT '策略类型',
    `declaration_date` date                                                          DEFAULT NULL COMMENT '策略关联的申报日期',
    `station_id`       bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `create_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`      datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `strategy_storage_info_unique` (`declaration_date`,`station_id`,`type`),
    KEY                `strategy_info_declaration_date_IDX` (`declaration_date`,`station_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能策略基本信息表';


-- bi_anhui.strategy_storage_param definition

CREATE TABLE IF NOT EXISTS `strategy_storage_param`
(
    `declaration_date`    date                                                          DEFAULT NULL COMMENT '策略关联的申报日期',
    `station_id`          bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `max_discharge_power` decimal(10, 4)                                                DEFAULT NULL COMMENT '最大放电功率',
    `min_discharge_power` decimal(10, 4)                                                DEFAULT NULL COMMENT '最小放电功率',
    `max_charge_power`    decimal(10, 4)                                                DEFAULT NULL COMMENT '最大充电功率',
    `min_charge_power`    decimal(10, 4)                                                DEFAULT NULL COMMENT '最小充电功率',
    `soc_max`             decimal(10, 4)                                                DEFAULT NULL COMMENT 'SOC上限',
    `soc_min`             decimal(10, 4)                                                DEFAULT NULL COMMENT 'SOC下限',
    `create_time`         datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`         datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    UNIQUE KEY `strategy_storage_param_unique` (`declaration_date`,`station_id`),
    KEY                   `strategy_info_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能动态参数表';


-- bi_anhui.strategy_storage_quotation_price_curve definition

CREATE TABLE IF NOT EXISTS `strategy_storage_quotation_price_curve`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`       bigint unsigned NOT NULL COMMENT '电站id',
    `strategy_id`      bigint unsigned DEFAULT NULL COMMENT '策略id',
    `type`             tinyint unsigned DEFAULT NULL COMMENT '数据类型 0充电报价曲线 1放电报价曲线',
    `declaration_date` date NOT NULL COMMENT '申报日期',
    `start_power`      decimal(10, 2) DEFAULT NULL COMMENT '开始出力',
    `end_power`        decimal(10, 2) DEFAULT NULL COMMENT '结束出力',
    `price`            decimal(10, 2) DEFAULT NULL COMMENT '价格',
    `price_cap`        decimal(10, 2) DEFAULT NULL COMMENT '价格上限',
    `price_floor`      decimal(10, 2) DEFAULT NULL COMMENT '价格下限',
    `create_time`      datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`        varchar(256)   DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY                `strategy_quotation_price_curve_declaration_date_IDX` (`declaration_date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='储能策略报价曲线';


-- bi_anhui.trade_calendar_record definition

CREATE TABLE IF NOT EXISTS `trade_calendar_record`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type_id`     int unsigned DEFAULT NULL COMMENT '关联交易类型id',
    `trade_date`  date         DEFAULT NULL COMMENT '交易日期',
    `start_time`  varchar(256) DEFAULT NULL COMMENT '开始时间（HH:mm）',
    `end_time`    varchar(256) DEFAULT NULL COMMENT '结束时间（HH:mm）',
    `remark`      text COMMENT '备注',
    `create_time` datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   varchar(256) DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(256) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易日历任务记录表';


-- bi_anhui.trade_type definition

CREATE TABLE IF NOT EXISTS `trade_type`
(
    `id`    int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `label` varchar(256) DEFAULT NULL COMMENT '交易类型名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易类型';


-- bi_anhui.transmission_section_status definition

CREATE TABLE IF NOT EXISTS `transmission_section_status`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '预测值',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `section_id`  bigint unsigned DEFAULT NULL COMMENT '电站id',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实际运行输电断面情况';


-- bi_anhui.unit_actual_output definition

CREATE TABLE IF NOT EXISTS `unit_actual_output`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `date`        date                                                          DEFAULT NULL COMMENT '日期',
    `value`       decimal(20, 6)                                                DEFAULT NULL COMMENT '实际发电出力',
    `time`        varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '时间标识，例如0015',
    `province_id` bigint unsigned DEFAULT NULL COMMENT '省份id',
    `create_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `unit_actual_output_date_IDX` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机组实际发电出力记录';


-- bi_anhui.units_under_time_constraint definition

CREATE TABLE IF NOT EXISTS `units_under_time_constraint`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date         DEFAULT NULL COMMENT '业务时间',
    `ws_time`       datetime     DEFAULT NULL,
    `name`          varchar(255) DEFAULT NULL COMMENT '机组名称',
    `apply_id`      varchar(255) DEFAULT NULL COMMENT '申请批次ID',
    `data_id`       varchar(255) DEFAULT NULL COMMENT '数据唯一标识符',
    `phy_id`        varchar(255) DEFAULT NULL COMMENT '机组 ID',
    `meas_type`     varchar(255) DEFAULT NULL COMMENT '数据类型',
    `time`          varchar(255) DEFAULT NULL COMMENT '开停机时长',
    `phy_name`      varchar(255) DEFAULT NULL COMMENT '机组名称',
    `state`         varchar(255) DEFAULT NULL COMMENT '类型',
    `create_time`   datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `units_under_time_constraint_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='开停机不满最小约束时间机组名单';


-- bi_anhui.upload_ali_file_record definition

CREATE TABLE IF NOT EXISTS `upload_ali_file_record`
(
    `file_id`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '上传到阿里百炼平台上的文件id',
    `file_name`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件名',
    `file_url`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件上传到minio的路径',
    `file_md5`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '文件md5值',
    `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误信息',
    `has_success`   tinyint unsigned DEFAULT '1' COMMENT '是否解析成功0失败 1成功',
    `has_delete`    tinyint unsigned DEFAULT '0' COMMENT '是否在阿里云百炼oss上删除 0未删除 1已删除',
    `create_time`   datetime                                                       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    PRIMARY KEY (`file_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传到阿里百炼平台的文件记录表';


-- bi_anhui.weekly_new_energy_forecast definition

CREATE TABLE IF NOT EXISTS `weekly_new_energy_forecast`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `business_time` date           DEFAULT NULL COMMENT '业务时间',
    `update_time`   datetime       DEFAULT NULL COMMENT '数据更新时间',
    `guid`          varchar(255)   DEFAULT NULL COMMENT '唯一标识符',
    `apply_id`      varchar(255)   DEFAULT NULL COMMENT '申请批次ID',
    `value`         decimal(20, 6) DEFAULT NULL COMMENT '预测值',
    `meas_type`     varchar(255)   DEFAULT NULL COMMENT '测量类型',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `weekly_new_energy_forecast_unique` (`business_time`,`meas_type`),
    KEY             `weekly_new_energy_forecast_business_time_IDX` (`business_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='次周新能源出力预测';


-- bi_anhui.yearly_power_forecast definition

CREATE TABLE IF NOT EXISTS `yearly_power_forecast`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`     bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `year`           varchar(256)   DEFAULT NULL COMMENT '年份',
    `month`          varchar(256)   DEFAULT NULL COMMENT '月份',
    `forecast_value` decimal(20, 6) DEFAULT NULL COMMENT '预测发电值',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(256)   DEFAULT NULL,
    `update_by`      varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY              `yearly_power_plan_year_IDX` (`year`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度发电量预测';


-- bi_anhui.yearly_power_plan definition

CREATE TABLE IF NOT EXISTS `yearly_power_plan`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `station_id`   bigint unsigned DEFAULT NULL COMMENT '关联电站id',
    `year`         varchar(256)   DEFAULT NULL COMMENT '年份',
    `month`        varchar(256)   DEFAULT NULL COMMENT '月份',
    `plan_value`   decimal(20, 6) DEFAULT NULL COMMENT '计划发电值',
    `actual_value` decimal(20, 6) DEFAULT NULL COMMENT '实际发电值',
    `create_time`  datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`    varchar(256)   DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(256)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY            `yearly_power_plan_year_IDX` (`year`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度发电计划';

INSERT IGNORE INTO bi_anhui.contract_period
(id, period_name)
VALUES(1, '年度合同');
INSERT IGNORE INTO bi_anhui.contract_period
(id, period_name)
VALUES(2, '多月合同');

INSERT IGNORE INTO bi_anhui.contract_type
(id, type_name)
VALUES(1, '省内绿电合同');

INSERT IGNORE INTO bi_anhui.notice_type
(id, type_name, create_time, update_time, create_by, update_by)
VALUES(1, '公众', '2025-02-18 09:11:49', '2025-02-18 09:11:49', NULL, NULL);
INSERT IGNORE INTO bi_anhui.notice_type
(id, type_name, create_time, update_time, create_by, update_by)
VALUES(2, '公开', '2025-02-18 09:11:49', '2025-02-18 09:11:49', NULL, NULL);

INSERT IGNORE INTO bi_anhui.trade_type
(id, label)
VALUES(1, '日滚动撮合交易');
INSERT IGNORE INTO bi_anhui.trade_type
(id, label)
VALUES(2, '绿电挂牌交易');

-- bi_anhui.strategy_adjust_data definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_data` (
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `curve_code` varchar(50) DEFAULT NULL COMMENT '引用数据code',
    UNIQUE KEY `strategy_adjust_data_unique` (`strategy_adjust_id`,`curve_code`),
    KEY `strategy_adjust_data_strategy_adjust_id_IDX` (`strategy_adjust_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议引用数据表';


-- bi_anhui.strategy_adjust_file definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_file` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `file_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '附件url',
    `file_name` varchar(100) DEFAULT NULL COMMENT '附件名称',
    PRIMARY KEY (`id`),
    KEY `strategy_adjust_file_strategy_adjust_id_IDX` (`strategy_adjust_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议关联附件表';


-- bi_anhui.strategy_adjust_info definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_info` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `target_date` date DEFAULT NULL COMMENT '标的日',
    `type` varchar(10) DEFAULT NULL COMMENT '策略建议类型 0 日前申报 1 滚动撮合',
    `strategy_content` text COMMENT '策略建议内容',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(10) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(10) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `strategy_adjust_info_target_date_IDX` (`target_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略建议信息';


-- bi_anhui.strategy_adjust_station definition

CREATE TABLE IF NOT EXISTS `strategy_adjust_station` (
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `strategy_adjust_id` bigint unsigned DEFAULT NULL COMMENT '策略建议id',
    `target_date` date DEFAULT NULL COMMENT '标的日',
    `type` varchar(10) DEFAULT NULL COMMENT '策略建议类型',
    UNIQUE KEY `strategy_adjust_station_unique` (`target_date`,`station_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电站关联策略建议表';

-- bi_anhui.trade_diary definition

CREATE TABLE IF NOT EXISTS `trade_diary` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `target_date` date DEFAULT NULL COMMENT '标的日',
    `station_id` bigint unsigned DEFAULT NULL COMMENT '电站id',
    `day_ahead_record` text COMMENT '日前申报记录',
    `rolling_record` text COMMENT '滚动撮合记录',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(10) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(10) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `trade_diary_unique` (`target_date`,`station_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易日历';


-- bi_anhui.trade_diary_file definition

CREATE TABLE IF NOT EXISTS `trade_diary_file` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `trade_dairy_id` bigint unsigned DEFAULT NULL COMMENT '关联交易日历id',
    `type` varchar(10) DEFAULT NULL COMMENT '附件类型 日前申报 dayAhead 滚撮 rolling',
    `file_name` varchar(100) DEFAULT NULL COMMENT '文件名称',
    `file_url` varchar(1000) DEFAULT NULL COMMENT '文件url',
    PRIMARY KEY (`id`),
    KEY `trade_diary_file_trade_dairy_id_IDX` (`trade_dairy_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易日历附件';