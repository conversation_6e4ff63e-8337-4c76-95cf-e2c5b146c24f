package org.jeecg.modules.config.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.service.IInzConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: 配置项管理
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
/*@Api(tags="配置项管理")*/
@RestController
@RequestMapping("/config/inzConfig")
@Slf4j
public class InzConfigController extends JeecgController<InzConfig, IInzConfigService> {
    @Autowired
    private IInzConfigService inzConfigService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 分页列表查询
     *
     * @param inzConfig
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "配置项管理-分页列表查询")
    /*	@ApiOperation(value="配置项管理-分页列表查询", notes="配置项管理-分页列表查询")*/
    @GetMapping(value = "/list")
    public Result<IPage<InzConfig>> queryPageList(InzConfig inzConfig,
                                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                  HttpServletRequest req) {
        QueryWrapper<InzConfig> queryWrapper = QueryGenerator.initQueryWrapper(inzConfig, req.getParameterMap());
        Page<InzConfig> page = new Page<InzConfig>(pageNo, pageSize);
        IPage<InzConfig> pageList = inzConfigService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     *   添加
     *
     * @param inzConfig
     * @return
     */
    @AutoLog(value = "配置项管理-添加")
    /*@ApiOperation(value="配置项管理-添加", notes="配置项管理-添加")*/
    @RequiresPermissions("config:inz_config:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzConfig inzConfig) {
        redisUtil.set("electricity:config:" + inzConfig.getCode(), inzConfig.getValue());
        inzConfigService.save(inzConfig);
        return Result.OK("添加成功！");
    }

    /**
     *  编辑
     *
     * @param inzConfig
     * @return
     */
    @AutoLog(value = "配置项管理-编辑")
    /*@ApiOperation(value="配置项管理-编辑", notes="配置项管理-编辑")*/
    @RequiresPermissions("config:inz_config:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<String> edit(@RequestBody InzConfig inzConfig) {
        inzConfigService.updateById(inzConfig);
        redisUtil.set("electricity:config:" + inzConfig.getCode(), inzConfig.getValue());
        return Result.OK("编辑成功!");
    }

    /**
     *   通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "配置项管理-通过id删除")
    /*@ApiOperation(value="配置项管理-通过id删除", notes="配置项管理-通过id删除")*/
    @RequiresPermissions("config:inz_config:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        inzConfigService.removeById(id);
        InzConfig config = inzConfigService.getById(id);
        redisUtil.del("electricity:config:" + config.getCode());
        return Result.OK("删除成功!");
    }

    /**
     *  批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "配置项管理-批量删除")
    /*@ApiOperation(value="配置项管理-批量删除", notes="配置项管理-批量删除")*/
    @RequiresPermissions("config:inz_config:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.inzConfigService.removeByIds(Arrays.asList(ids.split(",")));
        // 批量删除 Redis 中的配置项
        for (String id : ids.split(",")) {
            InzConfig config = inzConfigService.getById(id);
            redisUtil.del("electricity:config:" + config.getCode());
        }
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "配置项管理-通过id查询")
    /*@ApiOperation(value="配置项管理-通过id查询", notes="配置项管理-通过id查询")*/
    @GetMapping(value = "/queryById")
    public Result<InzConfig> queryById(@RequestParam(name="id",required=true) String id) {
        InzConfig inzConfig = inzConfigService.getById(id);
        if(inzConfig==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzConfig);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzConfig
     */
    @RequiresPermissions("config:inz_config:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzConfig inzConfig) {
        return super.exportXls(request, inzConfig, InzConfig.class, "配置项管理");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("config:inz_config:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzConfig.class);
    }

}
