package org.jeecg.modules.user_front.controller;

import cn.hutool.db.sql.Order;
import cn.hutool.http.HttpException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.user_front.entity.PayOrder;
import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserDeviceService;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.vo.InzUserFrontPage;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date: 2025-03-28
 * @Version: V1.0
 */
//@Api(tags="用户表")
@RestController
@RequestMapping("/user_front/inzUserFront")
@Slf4j
public class InzUserFrontController {
    @Autowired
    private IInzUserFrontService inzUserFrontService;
    @Autowired
    private IInzUserDeviceService inzUserDeviceService;

    /**
     * 分页列表查询
     *
     * @param inzUserFront
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "用户表-分页列表查询")
//	@ApiOperation(value="用户表-分页列表查询", notes="用户表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzUserFront>> queryPageList(InzUserFront inzUserFront,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("status", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<InzUserFront> queryWrapper = QueryGenerator.initQueryWrapper(inzUserFront, req.getParameterMap(), customeRuleMap);
        queryWrapper.lambda().eq(InzUserFront::getRealName, CommonUtils.getUserIdByToken());
        Page<InzUserFront> page = new Page<InzUserFront>(pageNo, pageSize);
        IPage<InzUserFront> pageList = inzUserFrontService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    //	 @ApiOperation(value="用户选择图书管理-分页列表查询", notes="用户选择图书管理-分页列表查询")
    @GetMapping(value = "/listAll")
    public Result<List<InzUserFront>> listAll(InzUserFront inzUserFront,
                                              HttpServletRequest req) {
        List<InzUserFront> pageList = inzUserFrontService.list();
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzUserFrontPage
     * @return
     */
    @AutoLog(value = "用户表-添加")
//	@ApiOperation(value="用户表-添加", notes="用户表-添加")
    @RequiresPermissions("user_front:inz_user_front:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzUserFrontPage inzUserFrontPage) {
        InzUserFront inzUserFront = new InzUserFront();
        BeanUtils.copyProperties(inzUserFrontPage, inzUserFront);
        inzUserFrontService.saveMain(inzUserFront, inzUserFrontPage.getInzUserDeviceList());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzUserFrontPage
     * @return
     */
    @AutoLog(value = "用户表-编辑")
//	@ApiOperation(value="用户表-编辑", notes="用户表-编辑")
    @RequiresPermissions("user_front:inz_user_front:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzUserFrontPage inzUserFrontPage) {
        InzUserFront inzUserFront = new InzUserFront();
        // 定义日期格式（线程安全写法）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00")); // 设置时区为东八区

        try {
            // 处理vipTime转换
            if (StringUtils.isNotBlank(inzUserFrontPage.getVipTime())) {
                // 拼接时间部分
                String dateTimeStr = inzUserFrontPage.getVipTime().trim() + " 00:00:00";
                // 解析为Date对象
                Date vipTime = sdf.parse(dateTimeStr);
                inzUserFront.setVipTime(vipTime);
            }
        } catch (ParseException e) {
            log.error("日期格式转换失败 | 原始值: {} | 错误: {}", inzUserFrontPage.getVipTime(), e.getMessage());
            return Result.error("会员时间格式错误，请使用 yyyy-MM-dd 格式");
        }


        BeanUtils.copyProperties(inzUserFrontPage, inzUserFront);
        InzUserFront inzUserFrontEntity = inzUserFrontService.getById(inzUserFront.getId());
        if (inzUserFrontEntity == null) {
            return Result.error("未找到对应数据");
        }

        inzUserFrontService.updateMain(inzUserFront, inzUserFrontPage.getInzUserDeviceList());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "用户表-通过id删除")
//	@ApiOperation(value="用户表-通过id删除", notes="用户表-通过id删除")
    @RequiresPermissions("user_front:inz_user_front:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzUserFrontService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "用户表-批量删除")
//	@ApiOperation(value="用户表-批量删除", notes="用户表-批量删除")
    @RequiresPermissions("user_front:inz_user_front:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzUserFrontService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户表-通过id查询")
//	@ApiOperation(value="用户表-通过id查询", notes="用户表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzUserFront> queryById(@RequestParam(name = "id", required = true) String id) {
        InzUserFront inzUserFront = inzUserFrontService.getById(id);
        if (inzUserFront == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzUserFront);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "用户登录设备-通过主表ID查询")
//	@ApiOperation(value="用户登录设备-通过主表ID查询", notes="用户登录设备-通过主表ID查询")
    @GetMapping(value = "/queryInzUserDeviceByMainId")
    public Result<IPage<InzUserDevice>> queryInzUserDeviceListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<InzUserDevice> inzUserDeviceList = inzUserDeviceService.selectByMainId(id);
        IPage<InzUserDevice> page = new Page<>();
        page.setRecords(inzUserDeviceList);
        page.setTotal(inzUserDeviceList.size());
        return Result.OK(page);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzUserFront
     */
    @RequiresPermissions("user_front:inz_user_front:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzUserFront inzUserFront) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<InzUserFront> queryWrapper = QueryGenerator.initQueryWrapper(inzUserFront, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<InzUserFront> inzUserFrontList = inzUserFrontService.list(queryWrapper);

        // Step.3 组装pageList
        List<InzUserFrontPage> pageList = new ArrayList<InzUserFrontPage>();
        for (InzUserFront main : inzUserFrontList) {
            InzUserFrontPage vo = new InzUserFrontPage();
            BeanUtils.copyProperties(main, vo);
            List<InzUserDevice> inzUserDeviceList = inzUserDeviceService.selectByMainId(main.getId());
            vo.setInzUserDeviceList(inzUserDeviceList);
            pageList.add(vo);
        }

        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "用户表列表");
        mv.addObject(NormalExcelConstants.CLASS, InzUserFrontPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("用户表数据", "导出人:" + sysUser.getRealname(), "用户表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("user_front:inz_user_front:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<InzUserFrontPage> list = ExcelImportUtil.importExcel(file.getInputStream(), InzUserFrontPage.class, params);
                for (InzUserFrontPage page : list) {
                    InzUserFront po = new InzUserFront();
                    BeanUtils.copyProperties(page, po);
                    inzUserFrontService.saveMain(po, page.getInzUserDeviceList());
                }
                return Result.OK("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK("文件导入失败！");
    }
}
