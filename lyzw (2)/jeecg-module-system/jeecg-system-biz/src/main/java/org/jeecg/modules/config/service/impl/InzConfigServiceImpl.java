package org.jeecg.modules.config.service.impl;

import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.mapper.InzConfigMapper;
import org.jeecg.modules.config.service.IInzConfigService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 配置项管理
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Service
public class InzConfigServiceImpl extends ServiceImpl<InzConfigMapper, InzConfig> implements IInzConfigService {

}
