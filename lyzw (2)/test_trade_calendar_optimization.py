#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易日历接口优化验证脚本
验证以下功能：
1. 不传date参数时返回所有数据
2. 江苏省数据源时跳过附件查询
3. 安徽省数据源时正常加载附件
"""

import requests
import json
import time
from datetime import datetime

# API配置
BASE_URL = "http://localhost:10015"
API_ENDPOINT = "/electricity/api/power_trade/calendar/queryByDate"

def test_no_date_parameter():
    """
    测试不传date参数的情况
    """
    print("=== 测试不传date参数（应返回所有数据） ===")
    
    test_cases = [
        {"provinceId": 1, "description": "江苏省 - 不传date"},
        {"provinceId": 2, "description": "安徽省 - 不传date"},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        
        url = f"{BASE_URL}{API_ENDPOINT}"
        params = {"provinceId": case["provinceId"]}
        
        try:
            response = requests.get(url, params=params, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('result', [])
                    print(f"✅ 查询成功，返回 {len(data)} 条记录")
                    
                    if data:
                        # 显示日期范围
                        dates = [record.get('tradeDate') for record in data if record.get('tradeDate')]
                        if dates:
                            print(f"   日期范围: {min(dates)} 到 {max(dates)}")
                        
                        # 检查附件情况
                        has_files = any(record.get('files') for record in data)
                        if case["provinceId"] == 1:  # 江苏省
                            if not has_files:
                                print("✅ 江苏省正确跳过了附件加载")
                            else:
                                print("❌ 江苏省不应该有附件数据")
                        elif case["provinceId"] == 2:  # 安徽省
                            print(f"   附件情况: {'有附件' if has_files else '无附件'}")
                    else:
                        print("   返回空数据")
                else:
                    print(f"❌ 查询失败: {result.get('message')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)

def test_with_date_parameter():
    """
    测试传入date参数的情况
    """
    print("\n=== 测试传入date参数（应返回指定日期数据） ===")
    
    test_cases = [
        {"provinceId": 1, "date": "2025-01-15", "description": "江苏省 - 指定日期"},
        {"provinceId": 2, "date": "2025-01-15", "description": "安徽省 - 指定日期"},
        {"provinceId": 1, "date": "2024-12-25", "description": "江苏省 - 历史日期"},
        {"provinceId": 2, "date": "2024-12-25", "description": "安徽省 - 历史日期"},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        
        url = f"{BASE_URL}{API_ENDPOINT}"
        params = {
            "provinceId": case["provinceId"],
            "date": case["date"]
        }
        
        try:
            response = requests.get(url, params=params, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('result', [])
                    print(f"✅ 查询成功，返回 {len(data)} 条记录")
                    
                    if data:
                        # 验证日期过滤是否正确
                        all_correct_date = True
                        for record in data:
                            trade_date = record.get('tradeDate')
                            if trade_date:
                                record_date = trade_date.split('T')[0] if 'T' in trade_date else trade_date.split(' ')[0]
                                if record_date != case["date"]:
                                    all_correct_date = False
                                    print(f"❌ 发现错误日期: {record_date} (期望: {case['date']})")
                                    break
                        
                        if all_correct_date:
                            print("✅ 日期过滤正确")
                        
                        # 检查附件情况
                        has_files = any(record.get('files') for record in data)
                        if case["provinceId"] == 1:  # 江苏省
                            if not has_files:
                                print("✅ 江苏省正确跳过了附件加载")
                            else:
                                print("❌ 江苏省不应该有附件数据")
                        elif case["provinceId"] == 2:  # 安徽省
                            print(f"   附件情况: {'有附件' if has_files else '无附件'}")
                    else:
                        print("   返回空数据（可能该日期无交易日历）")
                else:
                    print(f"❌ 查询失败: {result.get('message')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n=== 测试边界情况 ===")
    
    test_cases = [
        {"provinceId": 1, "date": "", "description": "江苏省 - 空字符串日期"},
        {"provinceId": 2, "date": "", "description": "安徽省 - 空字符串日期"},
        {"provinceId": 0, "date": "2025-01-15", "description": "全国汇总 - 指定日期"},
        {"provinceId": 0, "description": "全国汇总 - 不传日期"},
    ]
    
    for case in test_cases:
        print(f"\n--- {case['description']} ---")
        
        url = f"{BASE_URL}{API_ENDPOINT}"
        params = {"provinceId": case["provinceId"]}
        
        if "date" in case:
            params["date"] = case["date"]
        
        try:
            response = requests.get(url, params=params, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('result', [])
                    print(f"✅ 查询成功，返回 {len(data)} 条记录")
                else:
                    print(f"❌ 查询失败: {result.get('message')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)

def generate_test_report():
    """
    生成测试报告
    """
    print("\n" + "="*60)
    print("交易日历接口优化验证报告")
    print("="*60)
    
    print("\n🎯 优化内容:")
    print("1. 不传date参数时返回所有数据")
    print("2. 江苏省数据源时跳过附件查询（因为江苏数据库没有附件表）")
    print("3. 安徽省数据源时正常加载附件")
    print("4. 增强日志记录，便于问题追踪")
    
    print("\n💡 技术实现:")
    print("1. Service层：江苏省跳过loadTradeCalendarFiles()调用")
    print("2. XML映射：使用条件判断，date为空时不添加过滤条件")
    print("3. Controller层：添加详细的查询日志")
    
    print("\n📋 验证要点:")
    print("✅ 不传date参数 → 返回所有数据")
    print("✅ 传入date参数 → 只返回指定日期数据")
    print("✅ 江苏省查询 → 无附件数据")
    print("✅ 安徽省查询 → 可能有附件数据")
    print("✅ 全国汇总 → 正常工作")
    
    print("\n⚠️  注意事项:")
    print("1. 确保江苏和安徽数据库都有测试数据")
    print("2. 检查数据源配置是否正确")
    print("3. 验证日志输出是否符合预期")

def main():
    """
    主测试函数
    """
    print("交易日历接口优化验证")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试不传date参数
        test_no_date_parameter()
        
        # 测试传入date参数
        test_with_date_parameter()
        
        # 测试边界情况
        test_edge_cases()
        
        # 生成测试报告
        generate_test_report()
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生异常: {e}")

if __name__ == "__main__":
    main()
